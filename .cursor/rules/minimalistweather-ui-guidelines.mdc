---
description: 
globs: 
alwaysApply: false
---
# MinimalistWeather UI/UX 設計指南

本文件詳細說明 MinimalistWeather 專案的 UI 設計風格、元件使用規範與視覺一致性要求。

## 整體風格

- **極簡主義**：簡潔、留白、無多餘裝飾元素
- **黑白主色調**：主要使用黑色文字、白/灰色背景，避免過多彩色
- **專注內容**：將天氣數據作為視覺焦點，其他元素弱化處理

## 顏色系統

所有顏色必須使用 [HexColor.swift](mdc:MinimalistWeather/Utilities/HexColor.swift) 工具類，不可直接使用 Color 或 UIColor：

```swift
// 正確用法
Text("天氣")
    .foregroundColor(HexColor.color("222222"))

// 錯誤用法
Text("天氣")
    .foregroundColor(.black)
```

### 標準顏色
- **主要文字**：`222222`
- **次要文字**：`888888`
- **背景色**：`F9F9F9`
- **錯誤提示**：`FF4E4E`

## 字型系統

字型加載必須使用 [FontLoader.swift](mdc:MinimalistWeather/Utilities/FontLoader.swift)，主要使用：

- **一般文字**：系統字體 (中小尺寸)

```swift
// 一般文字
Text("設定")
    .font(.system(size: CGFloat(18).auto())) // 使用 AutoInch 自適應
    .foregroundColor(HexColor.color("222222"))
```

## 圖示系統

所有圖示必須使用 [AppIconsSymbol.swift](mdc:MinimalistWeather/Utilities/AppIconsSymbol.swift) 創建，不可直接使用 Image：

```swift
// 正確用法
AppIconsSymbol.createView(for: AppIcons.humidity, fontSize: CGFloat(40).auto(), color: HexColor.color("222222"))

// 錯誤用法
Image(systemName: "humidity")
```

## 頁面導航

- **主導航**：垂直分頁系統，使用 UIPageViewController 實現
- **模態視圖**：所有子頁面使用 .sheet() 呈現，不使用 NavigationView

```swift
// 主分頁結構
struct MainPagingView: View {
    var body: some View {
        VerticalPageView(viewModel: viewModel, isShowingLocationSearch: $isShowingLocationSearch)
            .background(HexColor.color("F9F9F9"))
            .edgesIgnoringSafeArea(.all)
    }
}

// 模態頁面
.sheet(isPresented: $isShowingLocationSearch) {
    LocationSearchView(viewModel: viewModel, scrollToTopAction: {
        NotificationCenter.default.post(name: Notification.Name("ScrollToPage1"), object: nil)
    })
}
```

## 自適應佈局（AutoInch）

所有視圖元素的尺寸、間距必須使用 AutoInch 框架的擴展方法實現自適應：

```swift
// 引入 AutoInch
import AutoInch

// 自適應字體尺寸
.font(.system(size: CGFloat(16).auto())) // 自適應不同螢幕

// 自適應間距
.padding(.horizontal, CGFloat(20).auto())
.padding(.top, CGFloat(15).auto())

// 自適應寬高
.frame(width: CGFloat(100).auto(), height: CGFloat(50).auto())

// 自適應圓角
.cornerRadius(CGFloat(10).auto())
```

## UIPageViewController 導航實現

垂直分頁導航使用 UIViewControllerRepresentable 包裝 UIPageViewController：

```swift
// 垂直分頁視圖包裝器
struct VerticalPageView: UIViewControllerRepresentable {
    @ObservedObject var viewModel: WeatherViewModel
    @Binding var isShowingLocationSearch: Bool
    
    func makeUIViewController(context: Context) -> UIPageViewController {
        let pageViewController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .vertical) // 垂直方向導航
        
        pageViewController.dataSource = context.coordinator
        pageViewController.delegate = context.coordinator
        
        // 設置初始頁面
        if let firstVC = context.coordinator.viewControllers.first {
            pageViewController.setViewControllers([firstVC], direction: .forward, animated: false)
        }
        
        return pageViewController
    }
    
    // 其他實現...
}
```

## 布局規範

- **水平邊距**：統一使用 CGFloat(60).auto() (變數名 `horizontalPadding` 或 `displayAreaPadding`)
- **元素間距**：大區塊間 CGFloat(20-30).auto()，小元素間 CGFloat(10).auto()
- **對齊方式**：主要內容居中，次要內容左對齊

## 互動反饋

- **按鈕**：無背景色，僅使用文字或圖示，點擊時無明顯視覺反饋
- **列表項**：整行可點擊，使用 `.contentShape(Rectangle())`
- **加載狀態**：使用 [LoadingView.swift](mdc:MinimalistWeather/Utilities/LoadingView.swift) 顯示加載動畫

## 特殊狀態處理

- **錯誤提示**：文字顯示，使用紅色 (`FF4E4E`)
- **網路離線**：全屏半透明遮罩 + 圖標 + 文字提示
- **首次啟動**：自動顯示位置搜索視圖

```swift
// 網路離線覆蓋層
if !networkMonitor.isConnected {
    VStack {
        Spacer()
        HStack {
            Spacer()
            VStack(spacing: CGFloat(10).auto()) {
                Image(systemName: "wifi.slash")
                    .font(.system(size: CGFloat(40).auto()))
                Text("網絡連接中斷")
                    .font(.headline)
                Text("請檢查您的網絡連接並重試")
                    .font(.subheadline)
                    .multilineTextAlignment(.center)
            }
            .padding()
            .foregroundColor(.white)
            .background(Color.black.opacity(0.7))
            .cornerRadius(CGFloat(10).auto())
            Spacer()
        }
        Spacer()
    }
    .transition(.opacity)
    .animation(.easeInOut, value: networkMonitor.isConnected)
    .zIndex(100) // 確保在其他視圖之上
}
```

## 動畫效果

- **過渡動畫**：使用 `.animation(.easeInOut)`，持續時間 0.2-0.3 秒
- **頁面切換**：使用 `withAnimation` 包裝，避免生硬切換
- **數據更新**：使用淡入淡出效果，避免跳變

## 無障礙設計

- **最小點擊區域**：至少 44x44pt
- **文字大小**：主要內容不小於 CGFloat(16).auto()，次要內容不小於 CGFloat(12).auto()
- **顏色對比度**：確保文字與背景對比足夠

## 擴充建議

- 新 UI 元件必須遵循現有風格，優先考慮可重用性
- 複雜動畫效果需封裝為獨立元件
- 自定義元件應支持深色模式與動態字型
- 所有數值尺寸必須使用 AutoInch 框架的 CGFloat(值).auto() 方法
