---
description: 
globs: 
alwaysApply: false
---
# SwiftUI 天氣 App 開發步驟 (基於 MVVM)

## 零、前置作業 (已完成)

*   **開發框架**：SwiftUI
*   **Target iOS 版本**：iOS 15.0+
*   **設計模式**：MVVM
*   **基礎框架**：已建立
*   **特色需求**：自定義字體圖標、垂直分頁滾動、自定義 UI 組件
*   **API**：OpenWeather API (需要申請 API Key)
*   **核心功能頁面**：
    *   Page 1: 當日天氣詳情
    *   Page 2: 設定與列表 (已儲存位置、溫度單位、七日預報)
*   **特殊需求**：
    *   無 GPS 定位，純手動搜尋地區
    *   網路狀態處理 (斷線、連線錯誤、初始檢查)

---

## 一、專案結構確認與環境設定

1.  **審查現有結構**：
    *   確認 `Model`, `View`, `ViewModel` 資料夾已建立且基本檔案就緒。
    *   檢查 `ContentView` 或主要的根視圖是否已設定好基礎的垂直滾動框架 (`ScrollView` + `LazyVStack` 可能是一種實現方式)。
2.  **整合自定義字體圖標**：
    *   將字體圖標檔 (.ttf 或 .otf) 加入專案。
    *   更新 `Info.plist` 以註冊字體 (`Fonts provided by application`)。
    *   建立一個 `FontIcon` Enum 或 Struct Helper，方便在 SwiftUI 中使用圖標 (例如：`Image(systemName:)` 的自定義版本)。
3.  **設定 OpenWeather API Key**：
    *   安全地儲存您的 OpenWeather API Key (例如：使用 `plist` 檔案或 Xcode Configuration，避免直接寫在程式碼中)。
4.  **安裝必要套件 (如果需要)**：
    *   若需使用特定網路函式庫 (如 Alamofire) 或其他輔助工具，透過 Swift Package Manager 加入。 (雖然 `URLSession` 和 `async/await` 已足夠強大)。

---

## 二、資料模型定義 (Model)

1.  **`WeatherData.swift`**:
    *   定義用於解析 OpenWeather API 當日天氣回應的 `struct`。
    *   包含必要欄位：溫度 (`main.temp`)、濕度 (`main.humidity`)、天氣狀況 (`weather[0].main`, `weather[0].description`, `weather[0].icon`)、最高溫 (`main.temp_max`)、最低溫 (`main.temp_min`)、降雨機率 (可能需要從 Forecast API 取得，或當日 API 的 `rain` 或 `snow` 欄位判斷)。
    *   確保所有模型都符合 `Codable` 協議。
2.  **`ForecastData.swift`**:
    *   定義用於解析 OpenWeather API 七日 (或多日) 預報回應的 `struct`。
    *   通常包含一個 `list` 陣列，每個元素代表一個時間點或一天的預報。
    *   每個預報元素需包含：日期/時間 (`dt`)、溫度 (`main.temp`, `temp.day` 等)、最高/最低溫 (`temp.max`, `temp.min`)、天氣狀況 (`weather[0].icon`)、降雨機率 (`pop`)。
    *   確保符合 `Codable`。
3.  **`SavedLocation.swift`**:
    *   定義儲存使用者位置的 `struct`。
    *   包含欄位：地區名稱 (`name: String`)、緯度 (`lat: Double`)、經度 (`lon: Double`)。(雖然不用 GPS，但 API 查詢通常需要經緯度或城市 ID，搜尋時可能需要轉換)。
    *   使其符合 `Codable` 以便儲存。
    *   可能需要一個 `id: UUID` 以便在列表中識別。
4.  **`AppSettings.swift`**:
    *   定義溫度單位 `enum TemperatureUnit: String, Codable, CaseIterable { case celsius, fahrenheit }`。
    *   可以包含其他需要持久化的設定。

---

## 三、網路服務層建構 (Networking Layer)

1.  **`NetworkError.swift`**:
    *   定義自定義錯誤 `enum`，包含常見網路錯誤 (e.g., `invalidURL`, `requestFailed`, `decodingError`, `serverError`, `noData`, `offline`)。
2.  **`APIService.swift`** (或 `WeatherService.swift`):
    *   建立一個 `class` 或 `struct` 來處理所有 OpenWeather API 的請求。
    *   使用 `URLSession` 和 `async/await`。
    *   **`fetchCurrentWeather(for location: String / coordinates: CLLocationCoordinate2D)`**: 根據地區名稱或經緯度獲取當前天氣。回傳 `Result<WeatherData, NetworkError>`。
    *   **`fetchForecast(for location: String / coordinates: CLLocationCoordinate2D)`**: 獲取天氣預報。回傳 `Result<ForecastData, NetworkError>`。
    *   **`searchLocations(query: String)`** (可選): 如果 OpenWeather 提供地理編碼 API，可以在這裡實現，將地區名稱轉換為經緯度和標準化的地區名稱。或者使用 Apple 的 `CLGeocoder` (但需要 CoreLocation 框架，即使不用 GPS 定位)。
    *   內部處理 URL 組裝、加入 API Key、發送請求、解析 JSON 回應及錯誤處理。

---

## 四、ViewModel 設計與實作

1.  **`CurrentWeatherViewModel.swift` (`ObservableObject`)**:
    *   `@Published var weatherData: WeatherData?`：儲存當前天氣資料。
    *   `@Published var isLoading: Bool = false`：表示是否正在載入。
    *   `@Published var errorMessage: String?`：顯示錯誤訊息。
    *   `@Published var currentDisplayLocation: SavedLocation?`：表示目前顯示的是哪個地區的天氣。
    *   `var temperatureUnit: TemperatureUnit`：從 Settings 獲取。
    *   `func fetchWeather(for location: SavedLocation)`：觸發 API 呼叫，更新 `weatherData`, `isLoading`, `errorMessage`。
    *   提供計算屬性 (Computed Properties) 將原始資料轉換為 View 可直接顯示的格式 (e.g., `temperatureString`, `humidityString`, `precipitationChanceString`, `highLowTempString`, `weatherIconName`)，並處理溫度單位轉換。
2.  **`SettingsViewModel.swift` (`ObservableObject`)**:
    *   `@Published var savedLocations: [SavedLocation] = []`：儲存使用者新增的地點列表。
    *   `@Published var selectedTemperatureUnit: TemperatureUnit = .celsius`：當前選擇的溫度單位。
    *   `func addLocation(_ location: SavedLocation)`：新增地點到列表並儲存。
    *   `func deleteLocation(at offsets: IndexSet)`：從列表刪除地點並儲存。
    *   `func loadSettings()`：從持久化儲存 (如 `UserDefaults`) 載入 `savedLocations` 和 `selectedTemperatureUnit`。
    *   `func saveSettings()`：將 `savedLocations` 和 `selectedTemperatureUnit` 儲存到持久化儲存。
    *   可能需要 `func searchAndAddLocation(query: String)` 來整合搜尋邏輯 (呼叫 `APIService`)。
3.  **`ForecastViewModel.swift` (`ObservableObject`)**:
    *   `@Published var forecastItems: [DailyForecastViewModel] = []`：儲存處理過的每日預報項目。
    *   `@Published var isLoading: Bool = false`。
    *   `@Published var errorMessage: String?`。
    *   `var temperatureUnit: TemperatureUnit`：從 Settings 獲取。
    *   `func fetchForecast(for location: SavedLocation)`：觸發 API 呼叫，處理 `ForecastData`，將其轉換為 `DailyForecastViewModel` 列表並更新 `forecastItems`。
    *   **`DailyForecastViewModel.swift` (`Identifiable`)**: 一個輔助 `struct`，包含單日預報的顯示資訊 (日期字串, 天氣圖標名稱, 降雨機率字串, 高低溫字串)。

---

## 五、UI 畫面開發 (View)

1.  **`ContentView.swift` (主畫面)**:
    *   包含 `ScrollViewReader` 和 `ScrollView(.vertical)`，設定 `showsIndicators: false`, `.isPagingEnabled(true)` (需要自定義實現垂直分頁效果，SwiftUI 原生 `TabView` 是水平的)。
    *   使用 `LazyVStack` 放置 Page 1 和 Page 2。
    *   實例化主要的 ViewModel (可能是 `CurrentWeatherViewModel` 和 `SettingsViewModel`，或一個統一管理的 `AppViewModel`)，並作為 `@StateObject`。
    *   將 ViewModel 傳遞給子視圖 (使用 `@EnvironmentObject` 或直接傳遞)。
    *   包含網路狀態監控的邏輯，顯示全域的錯誤/離線畫面。
2.  **`CurrentWeatherView.swift` (Page 1)**:
    *   接收 `CurrentWeatherViewModel` (`@ObservedObject` 或 `@EnvironmentObject`)。
    *   使用 `VStack`, `HStack`, `Spacer` 排列天氣資訊 (圖標 `Image`, 溫度 `Text`, 濕度 `Text`, 降雨機率 `Text`, 高低溫 `Text`)。
    *   根據 `viewModel.isLoading` 顯示載入指示器 (e.g., `ProgressView`)。
    *   根據 `viewModel.errorMessage` 顯示錯誤訊息。
    *   加上 `.id("page1")` 修飾符。
3.  **`SettingsView.swift` (Page 2)**:
    *   接收 `SettingsViewModel`。
    *   使用 `List` 或 `VStack` 顯示設定選項。
    *   **`SettingsRowView.swift`**: 建立可重用的設定列組件。
    *   **已儲存位置列**:
        *   使用 `NavigationLink` 或 `.sheet` 導向 `SavedLocationsView`。
    *   **溫度單位列**:
        *   使用 `Picker` 綁定到 `settingsViewModel.selectedTemperatureUnit`，並在變更時呼叫 `saveSettings()`。
    *   **最近七天預報列**:
        *   使用 `NavigationLink` 或 `.sheet` 導向 `ForecastView`，需要傳遞當前選擇的地區 (從 `CurrentWeatherViewModel` 或共享的狀態獲取)。
    *   加上 `.id("page2")` 修飾符。
4.  **`SavedLocationsView.swift` (子畫面)**:
    *   接收 `SettingsViewModel`。
    *   包含搜尋欄 (`.searchable`)，綁定到一個 `@State var searchQuery`。搜尋時呼叫 `SettingsViewModel` 的搜尋功能。
    *   使用 `List` 顯示 `settingsViewModel.savedLocations`。
        *   每個 Row 是一個自定義 View，顯示地區名稱。
        *   實現 `.onDelete` 修飾符，呼叫 `settingsViewModel.deleteLocation()`。
        *   Row 加上 `.onTapGesture`，觸發返回 Page 1 並更新天氣顯示的邏輯 (可能需要透過 Callback、Binding 或更新共享狀態來通知 `ContentView` 或 `CurrentWeatherViewModel` 更改 `currentDisplayLocation` 並觸發 `fetchWeather`)。
    *   處理搜尋結果的顯示與添加邏輯。
5.  **`ForecastView.swift` (子畫面)**:
    *   接收 `ForecastViewModel` (`@StateObject` 或 `@ObservedObject`)。
    *   接收需要顯示預報的 `SavedLocation`。
    *   在 `.onAppear` 中呼叫 `viewModel.fetchForecast()`。
    *   使用 `List` 顯示 `viewModel.forecastItems`。
        *   每個 Row 是一個自定義 View，顯示日期、天氣圖標、降雨機率、高低溫。
    *   根據 `viewModel.isLoading` 和 `viewModel.errorMessage` 顯示狀態。

---

## 六、網路狀態處理與錯誤畫面

1.  **`NetworkMonitor.swift`**:
    *   使用 `NWPathMonitor` 監控網路連線狀態。
    *   將狀態 (`@Published var isConnected: Bool`) 發佈出去，可作為 `ObservableObject` 並注入為 `@EnvironmentObject`。
2.  **初始啟動檢查**:
    *   在 `App` 或 `ContentView` 的 `onAppear` 中，檢查初始網路狀態。如果離線，顯示一個特定的初始離線畫面。
3.  **畫面整合**:
    *   在 `ContentView` 或需要網路的 View 中，觀察 `NetworkMonitor.isConnected`。
    *   如果 `!isConnected`，顯示一個覆蓋層或特定的 "離線模式" UI。
    *   在 ViewModel 的錯誤處理中，區分網路錯誤 (`.offline`) 和伺服器/API 錯誤 (`.serverError`, `.decodingError` 等)。
    *   為不同的錯誤狀態設計對應的提示畫面或訊息 (e.g., "無法連接伺服器", "請檢查網路連線")。

---

## 七、資料持久化

1.  **`PersistenceService.swift`** (可選，但推薦):
    *   建立一個專門處理資料儲存的服務 (e.g., 使用 `UserDefaults` 或 Core Data)。
    *   提供 `saveLocations(_ locations: [SavedLocation])`, `loadLocations() -> [SavedLocation]`, `saveTemperatureUnit(_ unit: TemperatureUnit)`, `loadTemperatureUnit() -> TemperatureUnit` 等方法。
    *   `SettingsViewModel` 在 `loadSettings` 和 `saveSettings` 中呼叫此服務的方法。
    *   記得處理 `Codable` 的編碼與解碼。

---

## 八、整合、測試與優化

1.  **完整流程測試**:
    *   測試從搜尋、新增地點、選擇地點、查看當前天氣、查看預報的完整流程。
    *   測試刪除地點。
    *   測試切換溫度單位是否正確反映在所有相關畫面上。
2.  **邊界條件與錯誤測試**:
    *   測試無網路狀態下的行為。
    *   測試 API 回應錯誤 (可以暫時修改 API Key 或 URL 來模擬)。
    *   測試搜尋無結果或無效的地區名稱。
    *   測試空的地點列表。
3.  **UI/UX 優化**:
    *   確保垂直分頁滾動流暢。
    *   自定義 UI 組件符合設計要求。
    *   加入適當的過渡動畫。
    *   優化載入狀態的顯示，避免畫面閃爍。
    *   確保自定義字體圖標正確顯示。
4.  **程式碼審查與重構**:
    *   檢查 MVVM 模式是否貫徹。
    *   移除重複程式碼。
    *   確保程式碼可讀性和維護性。

---
