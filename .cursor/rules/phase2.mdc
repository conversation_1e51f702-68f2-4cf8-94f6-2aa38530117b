---
description: 
globs: 
alwaysApply: false
---
# SwiftUI 天氣 App 開發計劃 - 第二階段：功能深化與 API 整合

**前提：** 基礎 MVVM 框架、UI 骨架、自定義組件及垂直分頁已完成。第一階段的核心 ViewModel 與 View 已建立。

**目標：** 整合 OpenWeatherMap Geocoding API 進行地點搜尋，改用 v2.5 API 獲取天氣與 5 日預報，並實現地點儲存與上次檢視狀態的持久化。

---

## 九、 資料模型 (Model) 調整與新增

1.  **`SavedLocation.swift` (更新)**:
    *   **必要：** 確認 `SavedLocation` 結構中包含 `lat: Double` 和 `lon: Double` 欄位，用於儲存從 Geocoding API 獲取的經緯度。
    *   **確認：** 保持 `id: UUID` (或其他唯一識別符) 和 `name: String`。
    *   **確認：** 繼續符合 `Codable` 協議。
    ```swift
    struct SavedLocation: Identifiable, Codable, Hashable { // Hashable for searchable suggestions
        let id: UUID
        var name: String // 可能來自 Geocoding API 的 name 或 local_names
        let lat: Double
        let lon: Double
    }
    ```
2.  **`GeocodingResult.swift` (新增)**:
    *   定義用於解析 OpenWeather Geocoding API (`/geo/1.0/direct`) 回應的 `struct`。
    *   API 回應是一個陣列 `[GeocodingResult]`。
    *   每個 `GeocodingResult` 需包含：
        *   `name: String` (主要地點名稱)
        *   `local_names: [String: String]?` (可選，用於顯示本地化名稱，初期可先忽略，僅用 `name`)
        *   `lat: Double` (緯度)
        *   `lon: Double` (經度)
        *   `country: String` (國家代碼)
        *   `state: String?` (州/省，可選)
    *   確保符合 `Codable`。
    ```swift
    struct GeocodingResult: Codable, Identifiable, Hashable { // Identifiable & Hashable for SwiftUI lists/suggestions
        // Use a computed id or handle potential duplicates if needed
        var id: String { "\(name)-\(lat)-\(lon)" } // Simple example ID
        let name: String
        let local_names: [String: String]?
        let lat: Double
        let lon: Double
        let country: String
        let state: String?
    }
    ```
3.  **`WeatherData.swift` & `ForecastData.swift` (審查)**:
    *   檢查 OpenWeather v2.5 的 `/weather` 和 `/forecast` API 回應結構是否與先前定義的模型有差異。通常 v2.5 的結構是比較穩定的，但仍建議快速核對文件確認欄位名稱（如溫度 `main.temp`, 圖標 `weather[0].icon`, 預報列表 `list` 等）無誤。
    *   **特別注意 `/forecast`**: 確認回應中的 `list` 陣列包含未來 5 天 *每 3 小時* 的預報。ViewModel 層需要處理這個 3 小時資料來聚合成每日預報。

---

## 十、 網路服務層 (Networking Layer) 更新

1.  **`APIService.swift` (更新)**:
    *   **修改 Base URL (如果需要)**：確認 API 請求的基礎 URL 指向 `api.openweathermap.org/data/2.5/`。
    *   **修改 `fetchCurrentWeather`**:
        *   方法簽名改為接收 `lat: Double` 和 `lon: Double`。
        *   更新 API Endpoint 為: `https://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={API key}&units=metric` (或其他單位)。
        ```swift
        func fetchCurrentWeather(lat: Double, lon: Double, unit: TemperatureUnit = .celsius) async -> Result<WeatherData, NetworkError>
        ```
    *   **修改 `fetchForecast`**:
        *   方法簽名改為接收 `lat: Double` 和 `lon: Double`。
        *   更新 API Endpoint 為: `https://api.openweathermap.org/data/2.5/forecast?lat={lat}&lon={lon}&appid={API key}&units=metric`。
        *   **注意**: 此 API 回傳的是 5 天內每 3 小時的資料，ViewModel 需要後續處理。
        ```swift
        func fetchForecast(lat: Double, lon: Double, unit: TemperatureUnit = .celsius) async -> Result<ForecastData, NetworkError>
        ```
    *   **新增 `searchLocations(query: String)`**:
        *   實現呼叫 Geocoding API (`/geo/1.0/direct`) 的功能。
        *   API Endpoint: `https://api.openweathermap.org/geo/1.0/direct?q={query}&limit=5&appid={API key}` (limit=5 可依需求調整)。
        *   回傳 `Result<[GeocodingResult], NetworkError>`。
        ```swift
        func searchLocations(query: String) async -> Result<[GeocodingResult], NetworkError>
        ```

---

## 十一、 ViewModel 功能強化

1.  **`SettingsViewModel.swift` (更新)**:
    *   `@Published var searchResults: [GeocodingResult] = []`：儲存地理編碼搜尋結果。
    *   `@Published var isSearching: Bool = false`：標示是否正在執行搜尋。
    *   **`performSearch(query: String)`**:
        *   當搜尋文字改變時觸發 (配合 SwiftUI 的 `.searchable`)。
        *   設定 `isSearching = true`。
        *   呼叫 `APIService.searchLocations(query: query)`。
        *   成功時，更新 `searchResults`；失敗時，處理錯誤 (可顯示提示)。
        *   最後設定 `isSearching = false`。
    *   **`addLocation(from result: GeocodingResult)`**:
        *   當使用者從搜尋結果中選擇一個地點時呼叫。
        *   創建一個新的 `SavedLocation` 實例，包含從 `result` 取得的 `name`, `lat`, `lon`。
        *   檢查是否已存在相同地點 (基於 lat/lon 或 name)。
        *   如果不存在，則將新的 `SavedLocation` 加入 `savedLocations` 陣列。
        *   呼叫 `saveSettings()` 來持久化更新後的列表。
    *   **`loadSettings()`**:
        *   除了載入 `savedLocations` 和 `selectedTemperatureUnit` 外，需額外載入**上次查看的地點 ID** (見步驟十三)。
        *   如果 `savedLocations` 為空，可能需要引導使用者搜尋第一個地點。
    *   **`saveSettings()`**:
        *   儲存 `savedLocations` (現在包含 lat/lon) 和 `selectedTemperatureUnit`。
        *   儲存**上次查看的地點 ID** (見步驟十三)。

2.  **`CurrentWeatherViewModel.swift` (更新)**:
    *   **移除/修改** 原本基於地點名稱的 `fetchWeather`。
    *   **`fetchWeather(for location: SavedLocation)`**:
        *   這是主要的觸發方法。
        *   設定 `isLoading = true`, `errorMessage = nil`。
        *   從 `location` 參數取得 `lat` 和 `lon`。
        *   呼叫 `APIService.fetchCurrentWeather(lat: location.lat, lon: location.lon, unit: temperatureUnit)`。
        *   處理回傳的 `Result`，更新 `weatherData` 或 `errorMessage`。
        *   設定 `isLoading = false`。
        *   **重要**: 當天氣載入成功後，更新 `currentDisplayLocation = location` 並觸發保存**此地點 ID 為 "上次查看"** (見步驟十三)。
    *   **初始化邏輯**:
        *   在 `init` 或首次載入時，從 `SettingsViewModel` 或持久化層讀取 "上次查看的地點 ID"。
        *   在 `savedLocations` 列表中找到對應的 `SavedLocation`。
        *   如果找到，自動呼叫 `fetchWeather(for: lastViewedLocation)`。
        *   如果找不到 (例如首次啟動或列表為空)，可以顯示預設畫面或提示使用者添加地點。

3.  **`ForecastViewModel.swift` (更新)**:
    *   **移除/修改** 原本基於地點名稱的 `fetchForecast`。
    *   **`fetchForecast(for location: SavedLocation)`**:
        *   接收 `SavedLocation` 作為參數。
        *   設定 `isLoading = true`, `errorMessage = nil`。
        *   呼叫 `APIService.fetchForecast(lat: location.lat, lon: location.lon, unit: temperatureUnit)`。
        *   **處理 5 日/3 小時資料**:
            *   成功獲取 `ForecastData` (包含 `list` 陣列) 後，需要編寫邏輯來處理這個 `list`。
            *   遍歷 `list`，將 3 小時的預報數據聚合為**每日**的預報摘要 (通常取每天的最高溫、最低溫、代表性天氣圖標、總降雨機率等)。
            *   一種常見做法是：
                *   按日期分組 `list` 中的項目。
                *   對於每一天，計算最高溫 (`temp_max`) 和最低溫 (`temp_min`)。
                *   選擇一個代表性的天氣圖標 (例如，中午或下午的圖標，或者出現頻率最高的天氣狀況)。
                *   計算或選擇一個代表性的降雨機率 (`pop`)。
            *   將處理後的每日摘要轉換為 `DailyForecastViewModel` 陣列並更新 `@Published var forecastItems`。
        *   處理 API 錯誤。
        *   設定 `isLoading = false`。

---

## 十二、 UI 畫面 (View) 整合與更新

1.  **`SavedLocationsView.swift` (重大更新)**:
    *   **整合搜尋**:
        *   使用 `.searchable(text: $searchQuery, placement: .navigationBarDrawer(displayMode: .always), prompt: "搜尋城市名稱")`。
        *   綁定 `searchQuery` 到 `SettingsViewModel` 中的一個 `@State` 或直接觸發 ViewModel 的搜尋方法。
        *   **實現自動完成 (Autocomplete / Suggestions)**:
            *   在 `.searchable` 中加入 `.onSubmit(of: .search) { viewModel.performSearch(query: searchQuery) }` (如果需要按 Enter 才搜尋)。
            *   **或 (更佳)** 使用 `suggestions` 修飾符：
            ```swift
            .searchable(...)
            .onChange(of: searchQuery) { newValue in
                // Optionally add debounce here
                viewModel.performSearch(query: newValue) // Trigger search on text change
            }
            .searchSuggestions { // Display results as suggestions
                if viewModel.isSearching {
                    ProgressView()
                } else {
                    ForEach(viewModel.searchResults) { result in
                        Text("\(result.name), \(result.country)") // Customize display
                            .searchCompletion("\(result.name)") // Optional: auto-fill search bar on tap
                            .onTapGesture {
                                viewModel.addLocation(from: result)
                                // Optional: Clear search query and results after adding
                                searchQuery = ""
                                viewModel.searchResults = []
                                dismiss() // Close search or view if needed
                            }
                    }
                }
            }
            ```
    *   **列表顯示**:
        *   `List` 現在顯示 `settingsViewModel.savedLocations`。
        *   Row 的 `onTapGesture` 行為：
            *   需要通知父層 (例如 `ContentView` 或透過 `CurrentWeatherViewModel`) 使用者選擇了這個 `SavedLocation`。
            *   這會觸發 `CurrentWeatherViewModel.fetchWeather(for: selectedLocation)`。
            *   可能需要關閉此 `SavedLocationsView` (如果它是 Sheet 或 Navigation)。
    *   **刪除**: `.onDelete` 保持不變，呼叫 `settingsViewModel.deleteLocation()`。

2.  **`ForecastView.swift` (調整)**:
    *   確認傳入的是 `SavedLocation`。
    *   在 `.onAppear` 中觸發 `viewModel.fetchForecast(for: passedInLocation)`。
    *   列表現在顯示的是處理過的 5 日預報 (`viewModel.forecastItems`)。

3.  **`ContentView.swift` 或主容器 View (調整)**:
    *   需要協調 `SettingsViewModel` 和 `CurrentWeatherViewModel` 之間的狀態，特別是當在 `SavedLocationsView` 中選擇了一個地點後，需要更新 `CurrentWeatherViewModel` 的 `currentDisplayLocation` 並觸發天氣刷新。這可以通過 `@EnvironmentObject`、Callbacks 或 Binding 實現。
    *   **處理上次查看狀態**:
        *   在 App 啟動時，讀取上次查看的地點 ID。
        *   找到對應的 `SavedLocation`。
        *   將此地點設置為 `CurrentWeatherViewModel` 的初始顯示地點。

---

## 十三、 資料持久化更新

1.  **`PersistenceService.swift` 或 `UserDefaults` 擴展 (更新)**:
    *   **`saveLocations`**: 確保儲存的 `[SavedLocation]` 包含 `lat` 和 `lon`。
    *   **`loadLocations`**: 確保能正確解碼包含 `lat` 和 `lon` 的 `SavedLocation`。
    *   **新增 `saveLastViewedLocationID(_ id: UUID?)`**:
        *   將當前在 Page 1 顯示的天氣對應的 `SavedLocation.id` 存儲到 `UserDefaults`。使用一個固定的 Key (e.g., `"lastViewedLocationID"`).
    *   **新增 `loadLastViewedLocationID() -> UUID?`**:
        *   從 `UserDefaults` 讀取儲存的 ID。
2.  **整合**:
    *   `SettingsViewModel` 在 `saveSettings` 時，除了儲存列表和單位，**不**直接儲存最後查看 ID (這個由 `CurrentWeatherViewModel` 觸發比較合理)。
    *   `SettingsViewModel` 在 `loadSettings` 時，需要呼叫 `loadLastViewedLocationID()` 以便 `CurrentWeatherViewModel` 初始化時使用。
    *   `CurrentWeatherViewModel` 在成功 `fetchWeather` 後，呼叫 `persistenceService.saveLastViewedLocationID(location.id)`。

---

## 十四、 測試階段

1.  **地點搜尋測試**:
    *   輸入不同城市名稱，驗證 Geocoding API 是否返回結果。
    *   測試自動完成/建議功能是否流暢。
    *   點擊搜尋結果，驗證地點是否正確添加到 `SavedLocationsView` 列表並包含經緯度。
    *   測試重複添加相同地點。
    *   測試無結果的搜尋。
2.  **天氣/預報 API 測試**:
    *   選擇一個已儲存的地點，驗證 Page 1 是否使用 v2.5 API 並基於 lat/lon 顯示正確的當前天氣。
    *   導航到預報頁面，驗證是否使用 v2.5 API 並顯示處理後的 5 日預報。
    *   切換溫度單位，驗證所有溫度顯示是否正確更新。
3.  **持久化測試**:
    *   新增/刪除地點，關閉 App 再重新開啟，驗證列表是否正確恢復。
    *   查看某個地點 A 的天氣，關閉 App，重新開啟，驗證 App 是否自動顯示地點 A 的天氣。
    *   切換到地點 B，關閉 App，重新開啟，驗證 App 是否顯示地點 B 的天氣。
    *   刪除上次查看的地點後重啟 App，驗證行為是否合理 (例如顯示列表中的第一個地點或提示)。
4.  **錯誤處理測試**:
    *   測試網路中斷時的搜尋、天氣讀取、預報讀取行為。
    *   測試無效 API Key 的情況。

---
