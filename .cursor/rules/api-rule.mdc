---
description:
globs:
alwaysApply: false
---
# 🌤️ 天氣中繼 API 整合指南

> 專為行動應用程式開發者設計的天氣 API 串接文件

## 📋 目錄

- [快速開始](#快速開始)
- [API 概覽](#api-概覽)
- [認證與配置](#認證與配置)
- [端點詳細說明](#端點詳細說明)
- [回應格式](#回應格式)
- [錯誤處理](#錯誤處理)
- [最佳實踐](#最佳實踐)
- [SDK 範例](#sdk-範例)
- [常見問題](#常見問題)
- [更新日誌](#更新日誌)

## 🚀 快速開始

### 基本資訊
- **API 基礎 URL**: `https://your-worker-domain.workers.dev`
- **API 版本**: v1
- **支援協議**: HTTPS
- **回應格式**: JSON
- **快取時間**: 10 分鐘

### 第一個請求
```bash
curl "https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654"
```

### 快速測試
```javascript
// JavaScript 範例
fetch('https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654')
  .then(response => response.json())
  .then(data => console.log(data));
```

## 🌐 API 概覽

### 支援的天氣資料提供者

| 提供者代碼 | 名稱 | 特色 | 建議使用場景 |
|-----------|------|------|--------------|
| `OW` | OpenWeather | 全球覆蓋、資料豐富 | 國際應用 |
| `AW` | Apple WeatherKit | 高精度、Apple 生態系 | iOS 應用 |
| `CW` | 中央氣象署 | 台灣官方、在地化 | 台灣地區應用 |
| `GW` | Google Weather | 多語言支援 | 多語言應用 |

### API 端點總覽

| 端點 | 用途 | 快取時間 | 資料範圍 |
|------|------|----------|----------|
| `/api/v1/current` | 即時天氣 | 10 分鐘 | 當前時刻 |
| `/api/v1/hourly` | 每小時預報 | 10 分鐘 | 24-120 小時 |
| `/api/v1/daily` | 每日預報 | 10 分鐘 | 5-7 天 |

## 🔑 認證與配置

### 無需認證
此 API 不需要 API 金鑰或認證 token，可直接使用。

### CORS 支援
API 支援跨域請求，適合前端直接呼叫：
```javascript
// 前端可直接呼叫，無需代理
fetch('https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654')
```

## 📡 端點詳細說明

### 1. 即時天氣 `/api/v1/current`

獲取指定位置的即時天氣資訊。

#### 請求參數

| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 資料提供者 | `OW`, `AW`, `CW`, `GW` |
| `lat` | number | ✅ | 緯度 | `25.0330` |
| `lon` | number | ✅ | 經度 | `121.5654` |
| `lang` | string | ✅ | 語言代碼 | `zh`, `en`, `zh-TW` |

#### 範例請求
```bash
# 台北即時天氣 (OpenWeather)
curl "https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654&lang=zh"

# 東京即時天氣 (Apple Weather)
curl "https://your-domain.workers.dev/api/v1/current?data=AW&lat=35.6762&lon=139.6503&lang=en"
```

### 2. 每小時預報 `/api/v1/hourly`

獲取指定位置的每小時天氣預報。

#### 請求參數

| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 資料提供者 | `OW`, `AW`, `CW`, `GW` |
| `lat` | number | ✅ | 緯度 | `25.0330` |
| `lon` | number | ✅ | 經度 | `121.5654` |
| `plan` | string | ✅ | 資料計劃 | `limit`, `full` |
| `lang` | string | ✅ | 語言代碼 | `zh`, `en`, `zh-TW` |

#### 資料計劃說明

| 計劃 | OpenWeather | Apple Weather | 中央氣象署 | Google Weather |
|------|-------------|---------------|-------------|----------------|
| `limit` | 24 小時 (8 筆) | 24 小時 | 24 小時 | 24 小時 |
| `full` | 120 小時 (40 筆) | 24 小時 | 72 小時 | 120 小時 |

#### 範例請求
```bash
# 台北 24 小時預報
curl "https://your-domain.workers.dev/api/v1/hourly?data=CW&lat=25.0330&lon=121.5654&plan=limit"

# 紐約 5 天預報
curl "https://your-domain.workers.dev/api/v1/hourly?data=OW&lat=40.7128&lon=-74.0060&plan=full"
```

### 3. 每日預報 `/api/v1/daily`

獲取指定位置的每日天氣預報。

#### 請求參數

| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 資料提供者 | `OW`, `AW`, `CW`, `GW` |
| `lat` | number | ✅ | 緯度 | `25.0330` |
| `lon` | number | ✅ | 經度 | `121.5654` |
| `lang` | string | ✅ | 語言代碼 | `zh`, `en`, `zh-TW` |

#### 範例請求
```bash
# 台北一週預報
curl "https://your-domain.workers.dev/api/v1/daily?data=CW&lat=25.0330&lon=121.5654"
```

## 📊 回應格式

### 成功回應結構
```json
{
  "success": true,
  "cache_status": "HIT|MISS",
  "data": {
    // 實際天氣資料
  }
}
```

### 即時天氣回應範例
```json
{
  "success": true,
  "cache_status": "HIT",
  "data": {
    "current": [{
      "timestamp": "2024-01-15T08:00:00.000Z",
      "weather": {
        "condition": "多雲",
        "icon": "04d"
      },
      "temperature": {
        "current": 18.5,
        "feels_like": 17.8,
        "min": 16.2,
        "max": 21.3
      },
      "atmosphere": {
        "humidity": 65,
        "sea_level": 1013,
        "ground_level": 1011,
        "visibility": 10.0
      },
      "wind": {
        "speed": 3.2,
        "direction": 230,
        "gust": 5.1
      },
      "precipitation": {
        "rain_amount": 0.0,
        "snow_amount": 0.0,
        "probability": -99
      },
      "clouds": {
        "coverage": 75
      },
      "uv": {
        "index": -999
      },
      "sunevents": {
        "sunrise": "2024-01-15T22:42:00.000Z",
        "sunset": "2024-01-15T09:18:00.000Z"
      }
    }],
    "metadata": {
      "provider": "OpenWeather",
      "api_type": "current",
      "response_code": 200
    }
  }
}
```

### 資料欄位說明

#### 天氣狀況 (`weather`)
- `condition`: 天氣描述 (中文或英文)
- `icon`: 天氣圖示代碼

#### 溫度資訊 (`temperature`)
- `current`: 當前溫度 (°C)
- `feels_like`: 體感溫度 (°C)
- `min`: 最低溫度 (°C)
- `max`: 最高溫度 (°C)

#### 大氣資訊 (`atmosphere`)
- `humidity`: 相對濕度 (%)
- `sea_level`: 海平面氣壓 (hPa)
- `ground_level`: 地面氣壓 (hPa)
- `visibility`: 能見度 (km)

#### 風力資訊 (`wind`)
- `speed`: 風速 (m/s)
- `direction`: 風向 (度)
- `gust`: 陣風 (m/s)

#### 降水資訊 (`precipitation`)
- `rain_amount`: 降雨量 (mm)
- `snow_amount`: 降雪量 (mm)
- `probability`: 降雨機率 (%)

#### 特殊值說明
- `-99`: 暫無資料
- `-999`: 不支援此項目

## ⚠️ 錯誤處理

### 錯誤回應格式
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Missing required \"lat\" and \"lon\" parameters"
  }
}
```

### 常見錯誤代碼

| 代碼 | 說明 | 解決方法 |
|------|------|----------|
| `400` | 參數錯誤 | 檢查必要參數是否完整 |
| `404` | 端點不存在 | 確認 API 路徑正確 |
| `500` | 伺服器錯誤 | 稍後重試或聯絡技術支援 |

### 錯誤處理範例
```javascript
fetch('https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // 處理成功資料
      console.log(data.data);
    } else {
      // 處理錯誤
      console.error('API Error:', data.error.message);
    }
  })
  .catch(error => {
    console.error('Network Error:', error);
  });
```

## 📱 SDK 範例

### iOS (Swift)
```swift
import Foundation

class WeatherAPI {
    private let baseURL = "https://your-domain.workers.dev/api/v1"
    
    func getCurrentWeather(lat: Double, lon: Double, provider: String = "OW", completion: @escaping (Result<WeatherData, Error>) -> Void) {
        let url = URL(string: "\(baseURL)/current?data=\(provider)&lat=\(lat)&lon=\(lon)")!
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                return
            }
            
            do {
                let weatherResponse = try JSONDecoder().decode(WeatherResponse.self, from: data)
                if weatherResponse.success {
                    completion(.success(weatherResponse.data))
                } else {
                    completion(.failure(NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: weatherResponse.error?.message ?? "Unknown error"])))
                }
            } catch {
                completion(.failure(error))
            }
        }.resume()
    }
}

struct WeatherResponse: Codable {
    let success: Bool
    let data: WeatherData?
    let error: APIError?
}

struct APIError: Codable {
    let code: Int
    let message: String
}
```

## ❓ 常見問題

### Q: 為什麼有些欄位顯示 -99？
A: -99 表示該欄位的資料無法從原始 API 取得。例如 OpenWeather 的即時天氣 API 不提供降雨機率，所以會顯示 -99。

### Q: 如何選擇合適的天氣資料提供者？
A: 
- **台灣地區**: 建議使用 `CW` (中央氣象署)
- **國際應用**: 建議使用 `OW` (OpenWeather)
- **iOS 應用**: 可考慮 `AW` (Apple WeatherKit)
- **多語言需求**: 建議使用 `GW` (Google Weather)

### Q: API 有請求頻率限制嗎？
A: 沒有明確的請求頻率限制，但由於有 10 分鐘的快取機制，建議不要過於頻繁地請求相同位置的資料。

### Q: 如何處理網路連線問題？
A: 建議實作重試機制和多提供者降級策略，確保應用程式的穩定性。

### Q: 支援哪些語言？
A: 目前支援中文 (zh, zh-TW) 和英文 (en)，但只有 Google Weather 支援動態語言切換。

## 📝 更新日誌

### v1.0.0 (2024-01-15)
- 初始版本發布
- 支援 OpenWeather、Apple WeatherKit、中央氣象署、Google Weather
- 提供即時天氣、每小時預報、每日預報功能
- 實作快取機制和錯誤處理

---

## 📞 技術支援

如有任何問題或建議，請聯絡開發團隊：
- 📧 Email: <EMAIL>
- 📱 GitHub Issues: [專案連結]
- 📖 文件更新: [文件連結]

---

*最後更新: 2024-01-15* 