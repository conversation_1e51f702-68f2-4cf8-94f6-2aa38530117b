---
description: 
globs: 
alwaysApply: false
---
# MinimalistWeather 專案結構與開發規範

本專案採用 SwiftUI + MVVM 架構，所有新功能開發必須遵循下列分層與慣例：

## 目錄分層
- **Model**：資料結構，無邏輯。範例：[WeatherData.swift](mdc:MinimalistWeather/Model/WeatherData.swift)
- **View**：UI 呈現，無商業邏輯。範例：[MainPagingView.swift](mdc:MinimalistWeather/View/MainPagingView.swift)
- **ViewModel**：狀態管理與商業邏輯。範例：[WeatherViewModel.swift](mdc:MinimalistWeather/ViewModel/WeatherViewModel.swift)
- **Services**：API、資料存取、外部資源。範例：[WeatherService.swift](mdc:MinimalistWeather/Services/WeatherService.swift)
- **Utilities**：工具、UI 輔助元件。範例：[NetworkMonitor.swift](mdc:MinimalistWeather/Utilities/NetworkMonitor.swift)

## 架構原則
- **View**：只負責 UI 呈現與用戶互動，所有狀態與邏輯交由 ViewModel 處理
- **ViewModel**：負責狀態管理、商業邏輯、與 Service/Model 溝通，所有 UI 綁定狀態必須 @Published
- **Model**：純資料結構，無邏輯
- **Service**：負責 API、資料存取、網路等外部資源存取，ViewModel 只能透過 Service 取得資料
- **Utilities**：工具類、靜態方法、UI 輔助元件等
- Service/Repository 需以協議（protocol）設計，方便 mock/測試

## 狀態管理
- 全域網路狀態由 [NetworkMonitor.swift](mdc:MinimalistWeather/Utilities/NetworkMonitor.swift) 以 @EnvironmentObject 注入
- 溫度單位、儲存位置等偏好設定，統一由 [AppSettings.swift](mdc:MinimalistWeather/Model/AppSettings.swift) 單例管理
- ViewModel 只持有 Service/Repository 實例，不直接操作 UserDefaults、檔案、API

## 資料流
- 天氣資料取得流程：View → ViewModel → WeatherService → API → Model → ViewModel → View
- 位置資料取得與儲存：ViewModel → LocationRepository → UserDefaults/本地存儲
- 新增/移除/切換位置，必須即時反映於 UI 並持久化

## UI/UX 慣例
- 主畫面為垂直分頁（天氣/設定），使用 UIPageViewController 實現垂直滑動分頁導航
- 所有顏色、字型、icon 統一用 [HexColor.swift](mdc:MinimalistWeather/Utilities/HexColor.swift)、[FontLoader.swift](mdc:MinimalistWeather/Utilities/FontLoader.swift)、[AppIconsSymbol.swift](mdc:MinimalistWeather/Utilities/AppIconsSymbol.swift)
- 設定頁面所有互動（如單位切換、位置管理）皆以 sheet/modal 呈現，避免多層 navigation
- 網路異常時，顯示全螢幕遮罩提示，並禁止互動

## 響應式佈局與自適應設計
- 專案整合 AutoInch 框架，提供設備尺寸自適應功能
- 所有View文件需引入 `import AutoInch`
- 所有數值需使用 CGFloat 包裝並使用 .auto() 方法
  ```swift
  Text("天氣") 
      .font(.system(size: CGFloat(18).auto()))
  ```
- 邊距、間距使用 .auto() 方法
  ```swift
  .padding(.horizontal, CGFloat(20).auto())
  ```
- 圖標大小應使用 .auto() 確保在不同設備上的一致性
  ```swift
  AppIconsSymbol.createView(for: AppIcons.humidity, fontSize: CGFloat(40).auto(), color: HexColor.color("222222"))
  ```

## 分頁導航實現
- 主畫面垂直分頁使用 UIViewControllerRepresentable 包裝 UIPageViewController
  ```swift
  // 主分頁結構
  struct MainPagingView: View {
      var body: some View {
          VerticalPageView(viewModel: viewModel, isShowingLocationSearch: $isShowingLocationSearch)
      }
  }
  
  // 垂直分頁視圖包裝器
  struct VerticalPageView: UIViewControllerRepresentable {
      // 實現方法略...
  }
  ```
- 頁面間通訊應使用 NotificationCenter 實現
- 避免在 SwiftUI View 間直接傳遞 UIPageViewController 實例

## 擴充規則
- 新功能必須遵循現有 MVVM 分層，嚴禁 fat View 或 fat ViewModel
- 新增 Service/Repository 請獨立檔案，並以協議（protocol）設計，方便未來 mock/測試
- UI 新元件請優先考慮可重用性，放入 Utilities
- 不允許直接在 View 內呼叫 API 或存取本地資料
- 任何全域狀態變更（如主題、語系）需以 ObservableObject + @EnvironmentObject 注入

## 命名與檔案結構
- Model、View、ViewModel、Service、Utilities 各自獨立資料夾
- 檔名與型別名一致，且以功能為主（如 WeatherViewModel.swift、WeatherService.swift）
- 變數、方法、型別命名皆採用駝峰式，且語意明確

## 其他
- 開發階段可用 mock data，正式版需關閉
- API Key 統一由 Info.plist 或 Resources/ApiConfig.plist 讀取，不可硬編碼
- 任何第三方套件需經審查，優先純 Swift 實作

## 建議
- 若未來要加新功能（如推播、主題切換、多語系），請先設計 protocol 與資料流，確保不破壞現有分層
- 若需跨頁溝通，優先考慮 @EnvironmentObject 或 NotificationCenter，避免直接傳遞 closure
- 若有複雜 UI 動畫，請封裝為 Utilities 元件
