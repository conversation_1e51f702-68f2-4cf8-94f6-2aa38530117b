/*
  Localizable.strings
  WeatherWidgets (Canadian French)
*/

// Day names (short form for widget)
"today" = "Auj";
"tomorrow" = "Dem";
"monday" = "Lun";
"tuesday" = "Mar";
"wednesday" = "Mer";
"thursday" = "Jeu";
"friday" = "Ven";
"saturday" = "Sam";
"sunday" = "Dim";

// Widget states
"loading" = "Chargement...";
"select_location" = "Sélectionner l'emplacement";
"config_error" = "Erreur de configuration";
"load_failed" = "Échec du chargement";
"not_set" = "Non défini";
"no_weather_data" = "Impossible de charger les données météo";
"please_set_location" = "Veuillez définir l'emplacement";

// Widget titles
"5_day_forecast" = "Prévisions 5 jours";
"current_weather" = "Météo actuelle";
"weekly_forecast" = "Prévisions quotidiennes";
"hourly_forecast" = "Prévisions horaires";

// Widget descriptions
"current_weather_desc" = "Affiche les informations météo actuelles";
"weekly_forecast_desc" = "Affiche les prévisions météo sur 5 jours";
"hourly_forecast_desc" = "Prévisions sur 5h et risque de pluie.";
"horizontal_weekly_forecast_desc" = "Affiche les prévisions sur 5 jours en disposition horizontale";

// Widget layout
"horizontal" = "Horizontal";

// Preview and test
"snapshot" = "Montréal";
"test" = "Partiellement nuageux";

// Weather sources
"central_weather_administration" = "Central Weather Administration";
