//
//  HourlyForecastWidget.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/24.
//

import WidgetKit
import SwiftUI
import AutoInch

// MARK: - Hourly Forecast Widget View

struct HourlyForecastWidgetEntryView : View {
    var entry: HourlyForecastEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        ZStack {
            if entry.forecasts.isEmpty || (entry.forecasts.count == 1 && entry.forecasts.first?.iconCode == "location") {
                // 未設定位置的 UI
                notConfiguredView
            } else {
                // 正常的預報顯示 UI
                switch family {
                case .systemSmall:
                    smallWidgetView
                case .systemMedium:
                    mediumWidgetView
                default:
                    mediumWidgetView
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 未設定位置的視圖
    private var notConfiguredView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(spacing: CGFloat(8).auto()) {
                    Spacer()

                    // 位置圖標
                    WidgetAppIconsSymbol.createWeatherIconView(
                        for: "03d",
                        fontSize: CGFloat(30).auto(),
                        color: WidgetColors.themed(.primaryText)
                    )

                    // 提示文字
                    Text(WidgetLocalizer.localized("please_set_location"))
                        .font(.system(size: CGFloat(11).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.primaryText))
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    // 副標題
                    Text(WidgetLocalizer.localized("not_set"))
                        .font(.system(size: CGFloat(9).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.secondaryText))
                        .multilineTextAlignment(.center)

                    Spacer()
                }
                .padding(CGFloat(12).auto())
            } else {
                ZStack {
                    VStack(spacing: CGFloat(8).auto()) {
                        Spacer()

                        // 位置圖標
                        WidgetAppIconsSymbol.createWeatherIconView(
                            for: "03d",
                            fontSize: CGFloat(30).auto(),
                            color: WidgetColors.themed(.primaryText)
                        )

                        // 提示文字
                        Text(WidgetLocalizer.localized("please_set_location"))
                            .font(.system(size: CGFloat(11).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.primaryText))
                            .multilineTextAlignment(.center)
                            .lineLimit(2)

                        // 副標題
                        Text(WidgetLocalizer.localized("not_set"))
                            .font(.system(size: CGFloat(9).auto(), weight: .regular, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.secondaryText))
                            .multilineTextAlignment(.center)

                        Spacer()
                    }
                    .padding(CGFloat(12).auto())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }

    // Small Widget View - 顯示前5小時的預報
    private var smallWidgetView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(spacing: 0) {
                    // 預報列表
                    VStack(spacing: CGFloat(5).auto()) {
                        ForEach(Array(entry.forecasts.prefix(5).enumerated()), id: \.offset) { index, forecast in
                            HStack(spacing: CGFloat(8).auto()) {
                                // 時間
                                Text(forecast.timeString)
                                    .font(.system(size: CGFloat(10).auto(), weight: .semibold, design: .rounded))
                                    .foregroundColor(WidgetColors.themed(.primaryText))
                                    .frame(width: CGFloat(35).auto(), alignment: .leading)

                                // 天氣圖標 - 使用自定義字體或系統圖標
                                WidgetAppIconsSymbol.createWeatherIconView(
                                    for: forecast.iconCode,
                                    fontSize: CGFloat(18).auto(),
                                    color: WidgetColors.themed(.primaryText)
                                )
                                .frame(width: CGFloat(20).auto())

                                Spacer()

                                // 降雨機率
                                Text("\(forecast.precipitationProbability)%")
                                    .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(WidgetColors.themed(.primaryText))
                                    .frame(width: CGFloat(35).auto(), alignment: .trailing)
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(CGFloat(12).auto())
            } else {
                ZStack {
                    VStack(spacing: 0) {
                        // 預報列表
                        VStack(spacing: CGFloat(5).auto()) {
                            ForEach(Array(entry.forecasts.prefix(5).enumerated()), id: \.offset) { index, forecast in
                                HStack(spacing: CGFloat(8).auto()) {
                                    // 時間
                                    Text(forecast.timeString)
                                        .font(.system(size: CGFloat(10).auto(), weight: .semibold, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                        .frame(width: CGFloat(35).auto(), alignment: .leading)

                                    // 天氣圖標 - 使用自定義字體或系統圖標
                                    WidgetAppIconsSymbol.createWeatherIconView(
                                        for: forecast.iconCode,
                                        fontSize: CGFloat(18).auto(),
                                        color: WidgetColors.themed(.primaryText)
                                    )
                                    .frame(width: CGFloat(20).auto())

                                    Spacer()

                                    // 降雨機率
                                    Text("\(forecast.precipitationProbability)%")
                                        .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                        .frame(width: CGFloat(35).auto(), alignment: .trailing)
                                }
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(CGFloat(12).auto())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }

    // Medium Widget View - 顯示5小時預報
    private var mediumWidgetView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(spacing: 0) {
                    // 預報列表
                    VStack(spacing: CGFloat(5).auto()) {
                        ForEach(Array(entry.forecasts.prefix(5).enumerated()), id: \.offset) { index, forecast in
                            HStack(spacing: CGFloat(8).auto()) {
                                // 時間
                                Text(forecast.timeString)
                                    .font(.system(size: CGFloat(10).auto(), weight: .semibold, design: .rounded))
                                    .foregroundColor(WidgetColors.themed(.primaryText))
                                    .frame(width: CGFloat(35).auto(), alignment: .leading)

                                // 天氣圖標 - 使用自定義字體或系統圖標
                                WidgetAppIconsSymbol.createWeatherIconView(
                                    for: forecast.iconCode,
                                    fontSize: CGFloat(18).auto(),
                                    color: WidgetColors.themed(.primaryText)
                                )
                                .frame(width: CGFloat(20).auto())

                                // 天氣描述
                                Text(forecast.condition)
                                    .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(WidgetColors.themed(.secondaryText))
                                    .lineLimit(1)
                                    .frame(maxWidth: .infinity, alignment: .leading)

                                Spacer()

                                // 降雨機率
                                Text("\(forecast.precipitationProbability)%")
                                    .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(WidgetColors.themed(.primaryText))
                                    .frame(width: CGFloat(35).auto(), alignment: .trailing)
                            }
                        }
                    }
                }
                .padding(CGFloat(12).auto())
                .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                ZStack {
                    VStack(spacing: 0) {
                        // 預報列表
                        VStack(spacing: CGFloat(5).auto()) {
                            ForEach(Array(entry.forecasts.prefix(5).enumerated()), id: \.offset) { index, forecast in
                                HStack(spacing: CGFloat(8).auto()) {
                                    // 時間
                                    Text(forecast.timeString)
                                        .font(.system(size: CGFloat(10).auto(), weight: .semibold, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                        .frame(width: CGFloat(35).auto(), alignment: .leading)

                                    // 天氣圖標 - 使用自定義字體或系統圖標
                                    WidgetAppIconsSymbol.createWeatherIconView(
                                        for: forecast.iconCode,
                                        fontSize: CGFloat(18).auto(),
                                        color: WidgetColors.themed(.primaryText)
                                    )
                                    .frame(width: CGFloat(20).auto())

                                    // 天氣描述
                                    Text(forecast.condition)
                                        .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.secondaryText))
                                        .lineLimit(1)
                                        .frame(maxWidth: .infinity, alignment: .leading)

                                    Spacer()

                                    // 降雨機率
                                    Text("\(forecast.precipitationProbability)%")
                                        .font(.system(size: CGFloat(12).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                        .frame(width: CGFloat(35).auto(), alignment: .trailing)
                                }
                            }
                        }
                    }
                    .padding(CGFloat(12).auto())
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }
}

// MARK: - Hourly Forecast Widget Configuration

struct HourlyForecastWidget: Widget {
    let kind: String = "HourlyForecastWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: HourlyForecastProvider()) { entry in
            if #available(iOS 17.0, *) {
                HourlyForecastWidgetEntryView(entry: entry)
                    .containerBackground(WidgetColors.themed(.primaryBackground), for: .widget)
            } else {
                HourlyForecastWidgetEntryView(entry: entry)
            }
        }
        .configurationDisplayName(WidgetLocalizer.localized("hourly_forecast"))
        .description(WidgetLocalizer.localized("hourly_forecast_desc"))
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}
