//
//  HorizontalWeeklyForecastWidget.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import WidgetKit
import SwiftUI
import AutoInch

// MARK: - Horizontal Weekly Forecast Widget View

struct HorizontalWeeklyForecastWidgetEntryView : View {
    var entry: HorizontalWeeklyForecastEntry

    var body: some View {
        ZStack {
            if entry.forecasts.isEmpty || (entry.forecasts.count == 1 && entry.forecasts.first?.iconCode == "location") {
                // 未設定位置的 UI
                notConfiguredView
            } else {
                // 正常的預報顯示 UI
                normalForecastView
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - 未設定位置的視圖
    private var notConfiguredView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(spacing: CGFloat(8).auto()) {
                    Spacer()

                    // 位置圖標
                    WidgetAppIconsSymbol.createWeatherIconView(
                        for: "03d",
                        fontSize: CGFloat(30).auto(),
                        color: WidgetColors.themed(.primaryText)
                    )

                    // 提示文字
                    Text(WidgetLocalizer.localized("please_set_location"))
                        .font(.system(size: CGFloat(11).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.primaryText))
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    // 副標題
                    Text(WidgetLocalizer.localized("not_set"))
                        .font(.system(size: CGFloat(9).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.secondaryText))
                        .multilineTextAlignment(.center)

                    Spacer()
                }
                .padding(CGFloat(12).auto())
            } else {
                ZStack {
                    VStack(spacing: CGFloat(8).auto()) {
                        Spacer()

                        // 位置圖標
                        WidgetAppIconsSymbol.createWeatherIconView(
                            for: "03d",
                            fontSize: CGFloat(30).auto(),
                            color: WidgetColors.themed(.primaryText)
                        )

                        // 提示文字
                        Text(WidgetLocalizer.localized("please_set_location"))
                            .font(.system(size: CGFloat(11).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.primaryText))
                            .multilineTextAlignment(.center)
                            .lineLimit(2)

                        // 副標題
                        Text(WidgetLocalizer.localized("not_set"))
                            .font(.system(size: CGFloat(9).auto(), weight: .regular, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.secondaryText))
                            .multilineTextAlignment(.center)

                        Spacer()
                    }
                    .padding(CGFloat(12).auto())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }

    // MARK: - 正常預報顯示視圖
    private var normalForecastView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(spacing: CGFloat(8).auto()) {
                    Spacer()

                    // 橫向預報列表
                    HStack(spacing: CGFloat(4).auto()) {
                        ForEach(Array(entry.forecasts.prefix(5).enumerated()), id: \.offset) { index, forecast in
                            VStack(spacing: CGFloat(12).auto()) {
                                // 溫度（使用 API 回傳的 current 值）
                                HStack(spacing: 0) {
                                    Text("°")
                                        .font(.system(size: CGFloat(8).auto(), weight: .bold, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                        .opacity(0.0)
                                    Text("\(forecast.currentTemp)")
                                        .font(.system(size: CGFloat(10).auto(), weight: .bold, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                    Text("°")
                                        .font(.system(size: CGFloat(8).auto(), weight: .bold, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.primaryText))
                                }

                                // 天氣圖標 - 使用自定義字體或系統圖標
                                WidgetAppIconsSymbol.createWeatherIconView(
                                    for: forecast.iconCode,
                                    fontSize: CGFloat(30).auto(),
                                    color: WidgetColors.themed(.primaryText)
                                )
                                .frame(height: CGFloat(20).auto())

                                // 日期
                                Text(forecast.dayName)
                                    .font(.system(size: CGFloat(10).auto(), weight: .regular, design: .rounded))
                                    .foregroundColor(WidgetColors.themed(.secondaryText))
                                    .lineLimit(1)

                            }
                            .frame(maxWidth: .infinity)
                        }
                    }

                    Spacer()
                }
                .padding(CGFloat(12).auto())
            } else {
                ZStack {
                    VStack(spacing: CGFloat(8).auto()) {
                        Spacer()

                        // 橫向預報列表
                        HStack(spacing: CGFloat(4).auto()) {
                            ForEach(Array(entry.forecasts.prefix(5).enumerated()), id: \.offset) { index, forecast in
                                VStack(spacing: CGFloat(12).auto()) {
                                    // 溫度（使用 API 回傳的 current 值）
                                    HStack(spacing: 0) {
                                        Text("°")
                                            .font(.system(size: CGFloat(8).auto(), weight: .bold, design: .rounded))
                                            .foregroundColor(WidgetColors.themed(.primaryText))
                                            .opacity(0.0)
                                        Text("\(forecast.currentTemp)")
                                            .font(.system(size: CGFloat(10).auto(), weight: .bold, design: .rounded))
                                            .foregroundColor(WidgetColors.themed(.primaryText))
                                        Text("°")
                                            .font(.system(size: CGFloat(8).auto(), weight: .bold, design: .rounded))
                                            .foregroundColor(WidgetColors.themed(.primaryText))
                                    }

                                    // 天氣圖標 - 使用自定義字體或系統圖標
                                    WidgetAppIconsSymbol.createWeatherIconView(
                                        for: forecast.iconCode,
                                        fontSize: CGFloat(30).auto(),
                                        color: WidgetColors.themed(.primaryText)
                                    )
                                    .frame(height: CGFloat(20).auto())

                                    // 日期
                                    Text(forecast.dayName)
                                        .font(.system(size: CGFloat(10).auto(), weight: .regular, design: .rounded))
                                        .foregroundColor(WidgetColors.themed(.secondaryText))
                                        .lineLimit(1)

                                }
                                .frame(maxWidth: .infinity)
                            }
                        }

                        Spacer()
                    }
                    .padding(CGFloat(12).auto())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }
}

// MARK: - Horizontal Weekly Forecast Widget Configuration

struct HorizontalWeeklyForecastWidget: Widget {
    let kind: String = "HorizontalWeeklyForecastWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: HorizontalWeeklyForecastProvider()) { entry in
            if #available(iOS 17.0, *) {
                HorizontalWeeklyForecastWidgetEntryView(entry: entry)
                    .containerBackground(WidgetColors.themed(.primaryBackground), for: .widget)
            } else {
                HorizontalWeeklyForecastWidgetEntryView(entry: entry)
            }
        }
        .configurationDisplayName(WidgetLocalizer.localized("weekly_forecast") + " " + WidgetLocalizer.localized("horizontal"))
        .description(WidgetLocalizer.localized("horizontal_weekly_forecast_desc"))
        .supportedFamilies([.systemMedium])
    }
}
