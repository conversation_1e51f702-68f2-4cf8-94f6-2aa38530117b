//
//  CurrentWeatherWidget.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import WidgetKit
import SwiftUI
import AutoInch

// MARK: - Current Weather Widget View

struct CurrentWeatherWidgetEntryView : View {
    var entry: CurrentWeatherEntry

    var body: some View {
        if entry.temperature.isEmpty && entry.iconCode == "location" {
            // 未設定位置的 UI
            notConfiguredView
        } else {
            // 正常的天氣顯示 UI
            weatherView
        }
    }

    // MARK: - 未設定位置的視圖
    private var notConfiguredView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(spacing: CGFloat(8).auto()) {
                    Spacer()

                    // 位置圖標
                    WidgetAppIconsSymbol.createWeatherIconView(
                        for: "03d",
                        fontSize: CGFloat(30).auto(),
                        color: WidgetColors.themed(.primaryText)
                    )
                    // Image(systemName: "location.slash")
                    //     .font(.system(size: CGFloat(24).auto(), weight: .medium))
                    //     .foregroundColor(WidgetColors.themed(.secondaryText))

                    // 提示文字
                    Text(WidgetLocalizer.localized("please_set_location"))
                        .font(.system(size: CGFloat(11).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.primaryText))
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    // 副標題
                    Text(WidgetLocalizer.localized("not_set"))
                        .font(.system(size: CGFloat(9).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.secondaryText))
                        .multilineTextAlignment(.center)

                    Spacer()
                }
                .padding(CGFloat(12).auto())
            } else {
                ZStack {
                    VStack(spacing: CGFloat(8).auto()) {
                        Spacer()

                        // 位置圖標
                        WidgetAppIconsSymbol.createWeatherIconView(
                            for: "03d",
                            fontSize: CGFloat(30).auto(),
                            color: WidgetColors.themed(.primaryText)
                        )

                        // 提示文字
                        Text(WidgetLocalizer.localized("please_set_location"))
                            .font(.system(size: CGFloat(11).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.primaryText))
                            .multilineTextAlignment(.center)
                            .lineLimit(2)

                        // 副標題
                        Text(WidgetLocalizer.localized("not_set"))
                            .font(.system(size: CGFloat(9).auto(), weight: .regular, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.secondaryText))
                            .multilineTextAlignment(.center)

                        Spacer()
                    }
                    .padding(CGFloat(12).auto())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }

    // MARK: - 正常天氣顯示視圖
    private var weatherView: some View {
        Group {
            if #available(iOS 17.0, *) {
                VStack(alignment: .leading, spacing: CGFloat(4).auto()) {
                    // 溫度
                    HStack(spacing: 0) {
                        Text(entry.temperature)
                            .font(.system(size: CGFloat(56).auto(), weight: .light, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.primaryText))
                        Text("°")
                            .font(.system(size: CGFloat(56).auto(), weight: .thin, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.primaryText))
                    }

                    Spacer()

                    Text(entry.locationName)
                        .font(.system(size: CGFloat(12).auto(), weight: .semibold, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.primaryText))
                        .lineLimit(1)
                        .padding(.bottom, CGFloat(4).auto())

                    // 天氣狀況
                    Text(entry.condition)
                        .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(WidgetColors.themed(.secondaryText))
                        .lineLimit(1)

                    Spacer()
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(CGFloat(12).auto())
            } else {
                ZStack {
                    VStack(alignment: .leading, spacing: CGFloat(4).auto()) {
                        // 溫度
                        HStack(spacing: 0) {
                            Text(entry.temperature)
                                .font(.system(size: CGFloat(56).auto(), weight: .light, design: .rounded))
                                .foregroundColor(WidgetColors.themed(.primaryText))
                            Text("°")
                                .font(.system(size: CGFloat(56).auto(), weight: .thin, design: .rounded))
                                .foregroundColor(WidgetColors.themed(.primaryText))
                        }

                        Spacer()

                        Text(entry.locationName)
                            .font(.system(size: CGFloat(12).auto(), weight: .semibold, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.primaryText))
                            .lineLimit(1)
                            .padding(.bottom, CGFloat(4).auto())

                        // 天氣狀況
                        Text(entry.condition)
                            .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(WidgetColors.themed(.secondaryText))
                            .lineLimit(1)

                        Spacer()
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(CGFloat(12).auto())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(WidgetColors.themed(.primaryBackground))
            }
        }
    }
}

// MARK: - Current Weather Widget Configuration

struct CurrentWeatherWidget: Widget {
    let kind: String = "CurrentWeatherWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: CurrentWeatherProvider()) { entry in
            if #available(iOS 17.0, *) {
                CurrentWeatherWidgetEntryView(entry: entry)
                    .containerBackground(WidgetColors.themed(.primaryBackground), for: .widget)
            } else {
                CurrentWeatherWidgetEntryView(entry: entry)
            }
        }
        .configurationDisplayName(WidgetLocalizer.localized("current_weather"))
        .description(WidgetLocalizer.localized("current_weather_desc"))
        .supportedFamilies([.systemSmall])
    }
}
