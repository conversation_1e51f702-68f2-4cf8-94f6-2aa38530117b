//
//  HourlyForecastProvider.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/24.
//

import WidgetKit
import SwiftUI

// MARK: - Hourly Forecast Timeline Provider

struct HourlyForecastProvider: TimelineProvider {
    /// 快取最後一次 API 請求的時間和結果
    private static var lastAPICall: (date: Date, result: HourlyForecastEntry, locationId: String, theme: String, weatherSource: String, temperatureUnit: String, timeFormat: String, language: String)?

    func placeholder(in context: Context) -> HourlyForecastEntry {
        HourlyForecastEntry(
            date: Date(),
            locationName: WidgetLocalizer.localized("select_location"),
            forecasts: Array(repeating: HourlyForecast(
                timeString: "12:00",
                condition: WidgetLocalizer.localized("loading"),
                iconCode: "01d",
                precipitationProbability: 0
            ), count: 5)
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (HourlyForecastEntry) -> ()) {
        // 使用多語系的示例數據
        let now = Date()
        let calendar = Calendar.current
        let timeFormatter = DateFormatter()

        // 讀取時間格式設定（如果無法讀取則使用24小時制）
        let timeFormat: TimeFormat
        if let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather"),
           let timeFormatRaw = groupDefaults.string(forKey: "timeFormat"),
           let format = TimeFormat(rawValue: timeFormatRaw) {
            timeFormat = format
        } else {
            timeFormat = .twentyFourHour
        }

        // 設定時區
        timeFormatter.timeZone = TimeZone.current

        // 根據時間格式設定 DateFormatter
        if timeFormat.is12Hour {
            // 強制使用英文 locale 來確保 12 小時制格式正常工作
            timeFormatter.locale = Locale(identifier: "en_US_POSIX")
            timeFormatter.dateFormat = "h a"  // 去掉分鐘，只顯示小時
            timeFormatter.amSymbol = "am"
            timeFormatter.pmSymbol = "pm"
            print("🕐 Hourly Widget Snapshot: 使用12小時制格式（無分鐘）")
        } else {
            timeFormatter.locale = Locale.current
            timeFormatter.dateFormat = "HH:mm"
            print("🕐 Hourly Widget Snapshot: 使用24小時制格式")
        }

        let sampleForecasts = (0..<5).map { index in
            let futureTime = calendar.date(byAdding: .hour, value: index, to: now)!
            return HourlyForecast(
                timeString: timeFormatter.string(from: futureTime),
                condition: ["Sunny", "Cloudy", "Rainy", "Partly Cloudy", "Clear"][index],
                iconCode: ["01d", "02d", "09d", "03d", "01n"][index],
                precipitationProbability: [10, 30, 80, 20, 5][index]
            )
        }

        let entry = HourlyForecastEntry(
            date: Date(),
            locationName: WidgetLocalizer.localized("snapshot"),
            forecasts: sampleForecasts
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<HourlyForecastEntry>) -> ()) {
        print("🔍 Hourly Forecast Widget getTimeline 被呼叫 - Family: \(context.family), isPreview: \(context.isPreview)")

        // 從 App Group 讀取位置數據
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            print("❌ Hourly Widget 錯誤: 無法初始化 App Group UserDefaults！")
            let entry = HourlyForecastEntry(date: Date(), locationName: "Error", forecasts: [])
            let timeline = Timeline(entries: [entry], policy: .never)
            completion(timeline)
            return
        }

        // 讀取主題設定並記錄
        let themeRawValue = groupDefaults.string(forKey: "themeMode") ?? "light"
        print("🎨 Hourly Widget getTimeline: 讀取主題設定 = \(themeRawValue)")
        
        // 讀取溫度單位設定
        let temperatureUnitRaw = groupDefaults.string(forKey: "temperatureUnit") ?? "celsius"
        let temperatureUnit = TemperatureUnit(rawValue: temperatureUnitRaw) ?? .celsius
        print("🌡️ Hourly Widget getTimeline: 讀取溫度單位設定 = \(temperatureUnitRaw)")

        // 讀取時間格式設定
        let timeFormatRaw = groupDefaults.string(forKey: "timeFormat") ?? "twentyFourHour"
        let timeFormat = TimeFormat(rawValue: timeFormatRaw) ?? .twentyFourHour
        print("🕐 Hourly Widget getTimeline: 讀取時間格式設定 = \(timeFormatRaw), 解析結果 = \(timeFormat)")

        // 讀取當前語言設定
        let currentLanguage = WidgetLanguageDetector.getCurrentLanguageCode()
        print("🌐 Hourly Widget getTimeline: 讀取語言設定 = \(currentLanguage)")

        // 讀取位置數據
        let locationName = groupDefaults.string(forKey: "widget_selected_location_name")
        let locationId = groupDefaults.string(forKey: "widget_selected_location_id")
        let locationLat = groupDefaults.double(forKey: "widget_selected_location_lat")
        let locationLon = groupDefaults.double(forKey: "widget_selected_location_lon")
        let locationCountry = groupDefaults.string(forKey: "widget_selected_location_country")
        let locationTimezone = groupDefaults.string(forKey: "widget_selected_location_timezone")
        let weatherSourceRaw = groupDefaults.string(forKey: "widget_selected_location_weather_source")

        // 檢查是否有完整的位置數據
        guard let name = locationName, !name.isEmpty,
              let id = locationId, !id.isEmpty,
              locationLat != 0.0, locationLon != 0.0 else {
            print("ℹ️ Hourly Widget: 在 App Group 中未找到完整的位置數據。")
            let entry = createNotConfiguredEntry()
            let timeline = Timeline(entries: [entry], policy: .after(Date().addingTimeInterval(60 * 60)))
            completion(timeline)
            return
        }

        print("✅ Hourly Widget: 找到位置數據 - \(name) (\(locationLat), \(locationLon)), 時區 = \(locationTimezone ?? "nil")")

        // 創建位置對象
        let weatherSource = weatherSourceRaw.flatMap { WeatherSource(rawValue: $0) } ?? .OW
        let location = SavedLocation(
            id: UUID(uuidString: id) ?? UUID(),
            name: name,
            formattedAddress: "\(name), \(locationCountry ?? "")",
            lat: locationLat,
            lon: locationLon,
            country: locationCountry ?? "",
            weatherSource: weatherSource,
            timezone: locationTimezone
        )

        print("🔍 Hourly Widget: 使用天氣來源 = \(location.effectiveWeatherSource.displayName)")

        // 檢查是否可以使用快取的結果（5分鐘內且所有設定未變更）
        let now = Date()
        let currentWeatherSource = location.effectiveWeatherSource.rawValue

        // 檢查快取是否有效
        var canUseCache = false
        if let lastCall = HourlyForecastProvider.lastAPICall,
           now.timeIntervalSince(lastCall.date) < 300 {

            // 檢查所有設定是否相同
            if lastCall.locationId == id &&
               lastCall.theme == themeRawValue &&
               lastCall.weatherSource == currentWeatherSource &&
               lastCall.temperatureUnit == temperatureUnitRaw &&
               lastCall.timeFormat == timeFormatRaw &&
               lastCall.language == currentLanguage {
                canUseCache = true
            } else {
                // 記錄變更原因
                if lastCall.locationId != id {
                    print("🔄 Hourly Widget: 位置已變更，重新載入")
                }
                if lastCall.theme != themeRawValue {
                    print("🔄 Hourly Widget: 主題已變更，重新載入")
                }
                if lastCall.weatherSource != currentWeatherSource {
                    print("🔄 Hourly Widget: 天氣來源已變更，重新載入 (\(lastCall.weatherSource) -> \(currentWeatherSource))")
                }
                if lastCall.temperatureUnit != temperatureUnitRaw {
                    print("🔄 Hourly Widget: 溫度單位已變更，重新載入 (\(lastCall.temperatureUnit) -> \(temperatureUnitRaw))")
                }
                if lastCall.timeFormat != timeFormatRaw {
                    print("🔄 Hourly Widget: 時間格式已變更，重新載入 (\(lastCall.timeFormat) -> \(timeFormatRaw))")
                }
                if lastCall.language != currentLanguage {
                    print("🔄 Hourly Widget: 語言已變更，重新載入 (\(lastCall.language) -> \(currentLanguage))")
                }
            }
        }

        // 如果可以使用快取，直接返回
        if canUseCache, let lastCall = HourlyForecastProvider.lastAPICall {
            print("🔄 Hourly Widget: 使用快取的預報數據（\(Int(now.timeIntervalSince(lastCall.date)))秒前）")
            let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date())!
            let timeline = Timeline(entries: [lastCall.result], policy: .after(nextUpdate))
            completion(timeline)
            return
        }

        // 如果不能使用快取，清除舊的快取
        if HourlyForecastProvider.lastAPICall != nil {
            print("🔄 Hourly Widget: 清除舊的快取數據")
            HourlyForecastProvider.lastAPICall = nil
        }

        // 獲取每小時預報數據
        fetchHourlyForecastData(for: location, temperatureUnit: temperatureUnit, timeFormat: timeFormat) { forecasts in
            let entry = HourlyForecastEntry(
                date: Date(),
                locationName: name,
                forecasts: forecasts
            )

            // 更新快取
            HourlyForecastProvider.lastAPICall = (Date(), entry, id, themeRawValue, currentWeatherSource, temperatureUnitRaw, timeFormatRaw, currentLanguage)

            // 設定下次更新時間為1小時後
            let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date())!
            let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
            completion(timeline)
        }
    }

    // MARK: - 私有方法

    /// 創建未設定 Widget 的 Entry
    private func createNotConfiguredEntry() -> HourlyForecastEntry {
        let notConfiguredForecast = HourlyForecast(
            timeString: "",
            condition: WidgetLocalizer.localized("please_set_location"),
            iconCode: "location",
            precipitationProbability: 0
        )

        return HourlyForecastEntry(
            date: Date(),
            locationName: WidgetLocalizer.localized("not_set"),
            forecasts: [notConfiguredForecast]
        )
    }

    /// 獲取每小時預報數據
    private func fetchHourlyForecastData(for location: SavedLocation, temperatureUnit: TemperatureUnit, timeFormat: TimeFormat, completion: @escaping ([HourlyForecast]) -> Void) {
        print("🔍 Hourly Widget fetchHourlyForecastData: 開始獲取數據，時間格式 = \(timeFormat)")

        let weatherService = WidgetWeatherService.shared

        // 使用 hourly API
        weatherService.getHourlyForecastForCoordinates(
            location.lat,
            lon: location.lon,
            locationName: location.name,
            weatherSource: location.effectiveWeatherSource
        ) { result in
            switch result {
            case .success(let forecastData):
                print("✅ Hourly Widget: 成功獲取預報數據，共 \(forecastData.count) 筆")
                let hourlyForecasts = self.convertToWidgetFormat(forecastData, temperatureUnit: temperatureUnit, timeFormat: timeFormat, location: location)
                completion(hourlyForecasts)
            case .failure(let error):
                print("❌ Hourly Widget: 獲取預報數據失敗 - \(error)")
                completion([])
            }
        }
    }

    /// 轉換預報數據為 Widget 格式
    private func convertToWidgetFormat(_ forecastData: [HourlyForecastData], temperatureUnit: TemperatureUnit, timeFormat: TimeFormat, location: SavedLocation) -> [HourlyForecast] {
        print("🔍 Hourly Widget convertToWidgetFormat: 開始轉換，時間格式 = \(timeFormat), is12Hour = \(timeFormat.is12Hour)")

        let timeFormatter = DateFormatter()

        // 設定時區為位置的時區（如果有的話），否則使用系統時區
        let targetTimeZone: TimeZone
        if let timezoneString = location.timezone, let locationTimeZone = TimeZone(identifier: timezoneString) {
            targetTimeZone = locationTimeZone
            print("🌍 Hourly Widget: 使用位置時區 = \(timezoneString)")
        } else {
            targetTimeZone = TimeZone.current
            print("🌍 Hourly Widget: 位置時區不可用，使用系統時區 = \(TimeZone.current.identifier)")
        }
        timeFormatter.timeZone = targetTimeZone

        // 根據時間格式設定 DateFormatter
        if timeFormat.is12Hour {
            // 強制使用英文 locale 來確保 12 小時制格式正常工作
            timeFormatter.locale = Locale(identifier: "en_US_POSIX")
            timeFormatter.dateFormat = "h a"  // 去掉分鐘，只顯示小時
            timeFormatter.amSymbol = "am"
            timeFormatter.pmSymbol = "pm"
            print("🕐 Hourly Widget: 使用12小時制格式（無分鐘），DateFormatter pattern = \(timeFormatter.dateFormat ?? "nil"), locale = \(timeFormatter.locale?.identifier ?? "nil"), 時區 = \(timeFormatter.timeZone?.identifier ?? "nil")")

            // 測試 DateFormatter 是否正常工作
            let testDate = Date()
            let testResult = timeFormatter.string(from: testDate)
            print("🧪 Hourly Widget: DateFormatter 測試 - 當前時間 = \(testDate), 格式化結果 = \(testResult)")
        } else {
            timeFormatter.locale = Locale.current
            timeFormatter.dateFormat = "HH:mm"
            print("🕐 Hourly Widget: 使用24小時制格式，DateFormatter pattern = \(timeFormatter.dateFormat ?? "nil"), locale = \(timeFormatter.locale?.identifier ?? "nil"), 時區 = \(timeFormatter.timeZone?.identifier ?? "nil")")
        }

        // 只取前5筆數據
        let results = Array(forecastData.prefix(5)).map { data in
            let formattedTime = timeFormatter.string(from: data.date)
            print("🌧️ Hourly Widget: 原始時間 = \(data.date), 格式化後時間 = \(formattedTime), 降雨機率 = \(data.precipitationProbability)%")

            return HourlyForecast(
                timeString: formattedTime,
                condition: data.condition,
                iconCode: data.iconCode,
                precipitationProbability: data.precipitationProbability
            )
        }

        print("🔍 Hourly Widget convertToWidgetFormat: 轉換完成，共 \(results.count) 筆數據")
        return results
    }
}
