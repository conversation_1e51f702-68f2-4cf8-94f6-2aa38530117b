//
//  CurrentWeatherProvider.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import WidgetKit
import SwiftUI

// MARK: - Current Weather Timeline Provider

struct CurrentWeatherProvider: TimelineProvider {
    /// 快取最後一次 API 請求的時間和結果
    private static var lastAPICall: (date: Date, result: CurrentWeatherEntry, locationId: String, theme: String, weatherSource: String, temperatureUnit: String, language: String)?

    /// 格式化溫度顯示
    /// - Parameters:
    ///   - celsius: 攝氏溫度
    ///   - unit: 溫度單位
    /// - Returns: 格式化後的溫度字串
    static func formatTemperature(_ celsius: Double, unit: TemperatureUnit) -> String {
        let convertedTemp: Double
        switch unit {
        case .celsius:
            convertedTemp = celsius
        case .fahrenheit:
            convertedTemp = celsius * 9 / 5 + 32
        }
        return String(Int(convertedTemp.rounded()))
    }

    func placeholder(in context: Context) -> CurrentWeatherEntry {
        CurrentWeatherEntry(
            date: Date(),
            temperature: "--",
            condition: WidgetLocalizer.localized("loading"),
            iconCode: "01d",
            locationName: WidgetLocalizer.localized("select_location")
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (CurrentWeatherEntry) -> ()) {
        let entry = CurrentWeatherEntry(
            date: Date(),
            temperature: "22",
            condition: WidgetLocalizer.localized("test"),
            iconCode: "01d",
            locationName: WidgetLocalizer.localized("snapshot")
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<CurrentWeatherEntry>) -> ()) {
        print("🔍 Current Weather Widget getTimeline 被呼叫 - Family: \(context.family), isPreview: \(context.isPreview)")

        // 從 App Group 讀取位置數據
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            print("❌ Widget 錯誤: 無法初始化 App Group UserDefaults！")
            let entry = createNotConfiguredEntry()
            let timeline = Timeline(entries: [entry], policy: .never)
            completion(timeline)
            return
        }

        print("✅ Widget: App Group UserDefaults 初始化成功。")

        // 讀取主題設定並記錄
        let themeRawValue = groupDefaults.string(forKey: "themeMode") ?? "light"
        print("🎨 Widget getTimeline: 讀取主題設定 = \(themeRawValue)")

        // 讀取溫度單位設定
        let temperatureUnitRaw = groupDefaults.string(forKey: "temperatureUnit") ?? "celsius"
        let temperatureUnit = TemperatureUnit(rawValue: temperatureUnitRaw) ?? .celsius
        print("🌡️ Widget getTimeline: 讀取溫度單位設定 = \(temperatureUnitRaw)")

        // 讀取當前語言設定
        let currentLanguage = WidgetLanguageDetector.getCurrentLanguageCode()
        print("🌐 Widget getTimeline: 讀取語言設定 = \(currentLanguage)")

        // 讀取位置數據
        let locationName = groupDefaults.string(forKey: "widget_selected_location_name")
        let locationId = groupDefaults.string(forKey: "widget_selected_location_id")
        let locationLat = groupDefaults.double(forKey: "widget_selected_location_lat")
        let locationLon = groupDefaults.double(forKey: "widget_selected_location_lon")
        let locationCountry = groupDefaults.string(forKey: "widget_selected_location_country")
        let weatherSourceRaw = groupDefaults.string(forKey: "widget_selected_location_weather_source")

        print("🔍 Widget: 從 App Group 讀取數據:")
        print("🔍 Name = \(locationName ?? "nil")")
        print("🔍 ID = \(locationId ?? "nil")")
        print("🔍 Lat = \(locationLat)")
        print("🔍 Lon = \(locationLon)")
        print("🔍 Country = \(locationCountry ?? "nil")")
        print("🔍 Weather Source = \(weatherSourceRaw ?? "nil")")

        // 檢查是否有完整的位置數據
        guard let name = locationName, !name.isEmpty,
              let id = locationId, !id.isEmpty,
              locationLat != 0.0, locationLon != 0.0 else {
            print("ℹ️ Widget: 在 App Group 中未找到完整的位置數據。")
            let entry = createNotConfiguredEntry()
            let timeline = Timeline(entries: [entry], policy: .after(Date().addingTimeInterval(60 * 60)))
            completion(timeline)
            return
        }

        print("✅ Widget: 找到位置數據 - \(name) (\(locationLat), \(locationLon))")

        // 創建位置對象
        let weatherSource = weatherSourceRaw.flatMap { WeatherSource(rawValue: $0) } ?? .OW
        let location = SavedLocation(
            id: UUID(uuidString: id) ?? UUID(),
            name: name,
            formattedAddress: "\(name), \(locationCountry ?? "")",
            lat: locationLat,
            lon: locationLon,
            country: locationCountry ?? "",
            weatherSource: weatherSource
        )

        print("🔍 Widget: 使用天氣來源 - \(location.effectiveWeatherSource.rawValue)")

        // 檢查是否可以使用快取的結果（2分鐘內且位置、主題、天氣來源、溫度單位和語言未變更）
        let now = Date()
        let currentWeatherSource = location.effectiveWeatherSource.rawValue
        if let lastCall = CurrentWeatherProvider.lastAPICall,
           now.timeIntervalSince(lastCall.date) < 120,
           lastCall.locationId == id,
           lastCall.theme == themeRawValue,
           lastCall.weatherSource == currentWeatherSource,
           lastCall.temperatureUnit == temperatureUnitRaw,
           lastCall.language == currentLanguage {
            print("🔄 Current Widget: 使用快取的天氣數據（\(Int(now.timeIntervalSince(lastCall.date)))秒前）")
            let nextUpdate = Calendar.current.date(byAdding: .minute, value: 10, to: Date())!
            let timeline = Timeline(entries: [lastCall.result], policy: .after(nextUpdate))
            completion(timeline)
            return
        } else if let lastCall = CurrentWeatherProvider.lastAPICall {
            if lastCall.locationId != id {
                print("🔄 Current Widget: 位置已變更，清除快取")
            }
            if lastCall.theme != themeRawValue {
                print("🔄 Current Widget: 主題已變更，清除快取")
            }
            if lastCall.weatherSource != currentWeatherSource {
                print("🔄 Current Widget: 天氣來源已變更，清除快取 (\(lastCall.weatherSource) -> \(currentWeatherSource))")
            }
            if lastCall.temperatureUnit != temperatureUnitRaw {
                print("🔄 Current Widget: 溫度單位已變更，清除快取 (\(lastCall.temperatureUnit) -> \(temperatureUnitRaw))")
            }
            if lastCall.language != currentLanguage {
                print("🔄 Current Widget: 語言已變更，清除快取 (\(lastCall.language) -> \(currentLanguage))")
            }
        }

        // 獲取天氣數據
        fetchWeatherData(for: location) { weatherData in
            let entry: CurrentWeatherEntry

            if let weather = weatherData {
                // 使用正確的溫度單位格式化溫度
                let formattedTemp = CurrentWeatherProvider.formatTemperature(Double(weather.temperature), unit: temperatureUnit)
                print("✅ Widget: 成功獲取天氣數據 - \(weather.temperature)°C -> \(formattedTemp)°\(temperatureUnit.symbol.dropFirst()), \(weather.condition)")

                entry = CurrentWeatherEntry(
                    date: Date(),
                    temperature: formattedTemp,
                    condition: weather.condition,
                    iconCode: weather.iconCode,
                    locationName: weather.location
                )
            } else {
                print("❌ Widget: 獲取天氣數據失敗")
                entry = CurrentWeatherEntry(
                    date: Date(),
                    temperature: "--",
                    condition: WidgetLocalizer.localized("load_failed"),
                    iconCode: "01d",
                    locationName: name
                )
            }

            // 更新快取
            CurrentWeatherProvider.lastAPICall = (Date(), entry, id, themeRawValue, currentWeatherSource, temperatureUnitRaw, currentLanguage)

            // 設定下次更新時間為10分鐘後
            let nextUpdate = Calendar.current.date(byAdding: .minute, value: 10, to: Date())!
            let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
            completion(timeline)
        }
    }

    // MARK: - 私有方法

    /// 創建未設定 Widget 的 Entry
    private func createNotConfiguredEntry() -> CurrentWeatherEntry {
        return CurrentWeatherEntry(
            date: Date(),
            temperature: "",
            condition: WidgetLocalizer.localized("please_set_location"),
            iconCode: "location",
            locationName: WidgetLocalizer.localized("not_set")
        )
    }

    /// 獲取天氣數據
    private func fetchWeatherData(for location: SavedLocation, completion: @escaping (WeatherData?) -> Void) {
        let weatherService = WidgetWeatherService.shared

        weatherService.getWeatherForCoordinates(
            location.lat,
            lon: location.lon,
            locationName: location.name,
            weatherSource: location.effectiveWeatherSource
        ) { result in
            switch result {
            case .success(let weatherData):
                completion(weatherData)
            case .failure(let error):
                print("🔍 Widget 天氣獲取失敗: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }
}
