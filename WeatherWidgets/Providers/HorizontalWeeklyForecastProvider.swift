//
//  HorizontalWeeklyForecastProvider.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import WidgetKit
import SwiftUI

// MARK: - Horizontal Weekly Forecast Timeline Provider

struct HorizontalWeeklyForecastProvider: TimelineProvider {
    /// 快取最後一次 API 請求的時間和結果
    private static var lastAPICall: (date: Date, result: HorizontalWeeklyForecastEntry, locationId: String, theme: String, weatherSource: String, temperatureUnit: String, timeFormat: String, language: String)?

    func placeholder(in context: Context) -> HorizontalWeeklyForecastEntry {
        HorizontalWeeklyForecastEntry(
            date: Date(),
            locationName: WidgetLocalizer.localized("select_location"),
            forecasts: Array(repeating: DailyForecast(
                dayName: WidgetLocalizer.localized("today"),
                condition: WidgetLocalizer.localized("loading"),
                iconCode: "01d",
                lowTemp: 0,
                currentTemp: 0
            ), count: 5)
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (HorizontalWeeklyForecastEntry) -> ()) {
        // 使用多語系的示例數據
        let localeIdentifier = WidgetLanguageDetector.getCurrentLocaleIdentifier()
        let dayFormatter = DateFormatter()
        dayFormatter.locale = Locale(identifier: localeIdentifier)
        dayFormatter.dateFormat = "EEE"
        
        let today = Date()
        let sampleForecasts = [
            DailyForecast(dayName: WidgetLocalizer.localized("today"), condition: "Sunny", iconCode: "01d", lowTemp: 18, currentTemp: 25),
            DailyForecast(dayName: WidgetLocalizer.localized("tomorrow"), condition: "Cloudy", iconCode: "02d", lowTemp: 16, currentTemp: 23),
            DailyForecast(dayName: dayFormatter.string(from: Calendar.current.date(byAdding: .day, value: 2, to: today)!), condition: "Rainy", iconCode: "09d", lowTemp: 14, currentTemp: 20),
            DailyForecast(dayName: dayFormatter.string(from: Calendar.current.date(byAdding: .day, value: 3, to: today)!), condition: "Sunny", iconCode: "01d", lowTemp: 17, currentTemp: 24),
            DailyForecast(dayName: dayFormatter.string(from: Calendar.current.date(byAdding: .day, value: 4, to: today)!), condition: "Cloudy", iconCode: "03d", lowTemp: 15, currentTemp: 22)
        ]

        let entry = HorizontalWeeklyForecastEntry(
            date: Date(),
            locationName: WidgetLocalizer.localized("snapshot"),
            forecasts: sampleForecasts
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<HorizontalWeeklyForecastEntry>) -> ()) {
        print("🔍 Horizontal Weekly Widget getTimeline 被呼叫 - Family: \(context.family), isPreview: \(context.isPreview)")

        // 從 App Group 讀取位置數據
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            print("❌ Horizontal Weekly Widget 錯誤: 無法初始化 App Group UserDefaults！")
            let entry = HorizontalWeeklyForecastEntry(date: Date(), locationName: "Error", forecasts: [])
            let timeline = Timeline(entries: [entry], policy: .never)
            completion(timeline)
            return
        }

        // 讀取主題設定並記錄
        let themeRawValue = groupDefaults.string(forKey: "themeMode") ?? "light"
        print("🎨 Horizontal Weekly Widget getTimeline: 讀取主題設定 = \(themeRawValue)")
        
        // 讀取溫度單位設定
        let temperatureUnitRaw = groupDefaults.string(forKey: "temperatureUnit") ?? "celsius"
        let temperatureUnit = TemperatureUnit(rawValue: temperatureUnitRaw) ?? .celsius
        print("🌡️ Horizontal Weekly Widget getTimeline: 讀取溫度單位設定 = \(temperatureUnitRaw)")

        // 讀取時間格式設定
        let timeFormatRaw = groupDefaults.string(forKey: "timeFormat") ?? "twentyFourHour"
        let timeFormat = TimeFormat(rawValue: timeFormatRaw) ?? .twentyFourHour
        print("🕐 Horizontal Weekly Widget getTimeline: 讀取時間格式設定 = \(timeFormatRaw), 解析結果 = \(timeFormat)")

        // 讀取當前語言設定
        let currentLanguage = WidgetLanguageDetector.getCurrentLanguageCode()
        print("🌐 Horizontal Weekly Widget getTimeline: 讀取語言設定 = \(currentLanguage)")

        // 讀取位置數據
        let locationName = groupDefaults.string(forKey: "widget_selected_location_name")
        let locationId = groupDefaults.string(forKey: "widget_selected_location_id")
        let locationLat = groupDefaults.double(forKey: "widget_selected_location_lat")
        let locationLon = groupDefaults.double(forKey: "widget_selected_location_lon")
        let locationCountry = groupDefaults.string(forKey: "widget_selected_location_country")
        let locationTimezone = groupDefaults.string(forKey: "widget_selected_location_timezone")
        let weatherSourceRaw = groupDefaults.string(forKey: "widget_selected_location_weather_source")

        // 檢查是否有完整的位置數據
        guard let name = locationName, !name.isEmpty,
              let id = locationId, !id.isEmpty,
              locationLat != 0.0, locationLon != 0.0 else {
            print("ℹ️ Horizontal Weekly Widget: 在 App Group 中未找到完整的位置數據。")
            let entry = createNotConfiguredEntry()
            let timeline = Timeline(entries: [entry], policy: .after(Date().addingTimeInterval(60 * 60)))
            completion(timeline)
            return
        }

        print("✅ Horizontal Weekly Widget: 找到位置數據 - \(name) (\(locationLat), \(locationLon)), 時區 = \(locationTimezone ?? "nil")")

        // 建立 SavedLocation 物件
        let weatherSource = weatherSourceRaw.flatMap { WeatherSource(rawValue: $0) } ?? .OW
        let location = SavedLocation(
            id: UUID(uuidString: id) ?? UUID(),
            name: name,
            formattedAddress: "\(name), \(locationCountry ?? "")",
            lat: locationLat,
            lon: locationLon,
            country: locationCountry ?? "",
            weatherSource: weatherSource,
            timezone: locationTimezone
        )

        print("📍 Horizontal Weekly Widget: 使用位置 = \(name) (\(locationLat), \(locationLon))")
        print("🔍 Horizontal Weekly Widget: 使用天氣來源 = \(location.effectiveWeatherSource.displayName)")

        // 檢查是否可以使用快取的結果（5分鐘內且所有設定未變更）
        let now = Date()
        let currentWeatherSource = location.effectiveWeatherSource.rawValue

        // 檢查快取是否有效
        var canUseCache = false
        if let lastCall = HorizontalWeeklyForecastProvider.lastAPICall,
           now.timeIntervalSince(lastCall.date) < 300 {

            // 檢查所有設定是否相同
            if lastCall.locationId == id &&
               lastCall.theme == themeRawValue &&
               lastCall.weatherSource == currentWeatherSource &&
               lastCall.temperatureUnit == temperatureUnitRaw &&
               lastCall.timeFormat == timeFormatRaw &&
               lastCall.language == currentLanguage {
                canUseCache = true
            }
        }

        // 如果可以使用快取，直接返回
        if canUseCache, let lastCall = HorizontalWeeklyForecastProvider.lastAPICall {
            print("🔄 Horizontal Weekly Widget: 使用快取的預報數據（\(Int(now.timeIntervalSince(lastCall.date)))秒前）")
            let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date())!
            let timeline = Timeline(entries: [lastCall.result], policy: .after(nextUpdate))
            completion(timeline)
            return
        }

        // 獲取每週預報數據
        fetchWeeklyForecastData(for: location, temperatureUnit: temperatureUnit, timeFormat: timeFormat) { forecasts in
            let entry = HorizontalWeeklyForecastEntry(
                date: Date(),
                locationName: name,
                forecasts: forecasts
            )

            // 更新快取
            HorizontalWeeklyForecastProvider.lastAPICall = (Date(), entry, id, themeRawValue, currentWeatherSource, temperatureUnitRaw, timeFormatRaw, currentLanguage)

            // 設定下次更新時間為1小時後
            let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date())!
            let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
            completion(timeline)
        }
    }

    // MARK: - 私有方法

    /// 創建未設定 Widget 的 Entry
    private func createNotConfiguredEntry() -> HorizontalWeeklyForecastEntry {
        let notConfiguredForecast = DailyForecast(
            dayName: "",
            condition: WidgetLocalizer.localized("please_set_location"),
            iconCode: "location",
            lowTemp: 0,
            currentTemp: 0
        )

        return HorizontalWeeklyForecastEntry(
            date: Date(),
            locationName: WidgetLocalizer.localized("not_set"),
            forecasts: [notConfiguredForecast]
        )
    }

    /// 獲取一週預報數據
    private func fetchWeeklyForecastData(for location: SavedLocation, temperatureUnit: TemperatureUnit, timeFormat: TimeFormat, completion: @escaping ([DailyForecast]) -> Void) {
        let weatherService = WidgetWeatherService.shared

        // 使用 weekly API
        weatherService.getWeeklyForecastForCoordinates(
            location.lat,
            lon: location.lon,
            locationName: location.name,
            weatherSource: location.effectiveWeatherSource
        ) { result in
            switch result {
            case .success(let forecastData):
                print("✅ Horizontal Weekly Widget: 成功獲取預報數據")
                let dailyForecasts = self.convertToWidgetFormat(forecastData, temperatureUnit: temperatureUnit, location: location)
                completion(dailyForecasts)
            case .failure(let error):
                print("❌ Horizontal Weekly Widget: 獲取預報數據失敗 - \(error)")
                completion([])
            }
        }
    }

    /// 轉換預報數據為 Widget 格式
    private func convertToWidgetFormat(_ forecastData: [WeeklyForecastData], temperatureUnit: TemperatureUnit, location: SavedLocation) -> [DailyForecast] {
        // 設定時區為位置的時區（如果有的話），否則使用系統時區
        var calendar = Calendar.current
        if let timezoneString = location.timezone, let locationTimeZone = TimeZone(identifier: timezoneString) {
            calendar.timeZone = locationTimeZone
            print("🌍 Horizontal Weekly Widget: 使用位置時區 = \(timezoneString)")
        } else {
            print("🌍 Horizontal Weekly Widget: 位置時區不可用，使用系統時區 = \(calendar.timeZone.identifier)")
        }

        return forecastData.enumerated().map { index, data in
            let dayName: String
            if index == 0 {
                dayName = WidgetLocalizer.localized("today")
            } else if index == 1 {
                dayName = WidgetLocalizer.localized("tomorrow")
            } else {
                // 使用多語系的星期文字
                let weekday = calendar.component(.weekday, from: data.date)
                dayName = WidgetLocalizer.getLocalizedWeekdayName(for: weekday)
            }

            // 直接使用 API 回傳的 temperature.current 值，根據溫度單位轉換
            let convertedTemp = CurrentWeatherProvider.formatTemperature(Double(data.temperature), unit: temperatureUnit)
            let currentTemp = Int(convertedTemp) ?? data.temperature

            // 由於 weekly API 只提供 current 溫度，我們直接使用它
            // lowTemp 設為比 current 溫度低 5-8 度的估算值（僅用於需要顯示高低溫的場合）
            let lowTemp = max(currentTemp - Int.random(in: 5...8), 0)

            print("🌡️ Horizontal Weekly Widget: 使用 API current 溫度 - \(data.temperature)°C -> \(currentTemp)°\(temperatureUnit.symbol.dropFirst())")

            return DailyForecast(
                dayName: dayName,
                condition: data.condition,
                iconCode: data.iconCode,
                lowTemp: lowTemp,
                currentTemp: currentTemp  // API 回傳的 temperature.current 值
            )
        }
    }
}
