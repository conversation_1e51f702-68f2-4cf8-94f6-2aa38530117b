//
//  WidgetAppIcons.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import Foundation

/// Widget 專用的自定義字體圖標映射
struct WidgetAppIcons {
    // MARK: - 天氣圖標
    static let clearDay: String = "\u{E900}" // 太陽
    static let fewCloudsDay: String = "\u{E901}" // 雲
    static let clearNight: String = "\u{E902}" // 雨
    static let fewCloudsNight: String = "\u{E903}" // 雪
    static let scatteredClouds: String = "\u{E904}" // 暴風雨
    static let brokenClouds: String = "\u{E905}" // 多雲帶晴
    static let showerRain: String = "\u{E906}" // 霧
    static let rain: String = "\u{E907}" // 風
    static let thunderstorm: String = "\u{E908}" // 風
    static let snow: String = "\u{E909}" // 風
    static let mist: String = "\u{E90A}" // 風
    
    // 日出日落圖標
    static let sunrise: String = "\u{E926}" // 日出
    static let sunset: String = "\u{E927}" // 日落
    
    // MARK: - 其他常用圖標
    static let temperature: String = "\u{E90C}" // 溫度
    static let humidity: String = "\u{E90B}" // 濕度
    static let appicon: String = "\u{E91E}" // App 圖標
}
