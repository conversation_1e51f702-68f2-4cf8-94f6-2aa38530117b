//
//  WidgetUtils.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import Foundation

// MARK: - 工具函數

/// 天氣圖標映射 - 支援自定義字體和系統圖標
func getWeatherIcon(for iconCode: String) -> String {
    // 嘗試加載自定義字體（如果尚未加載）
    if !WidgetFontLoader.isCustomFontAvailable() {
        _ = WidgetFontLoader.loadCustomFonts()
    }

    // 如果自定義字體可用，返回自定義字體的圖標字符
    if WidgetFontLoader.isCustomFontAvailable() {
        return WidgetAppIconsSymbol.getWeatherIconFromCode(iconCode)
    }

    // 如果自定義字體不可用，返回系統圖標名稱
    let customIcon = WidgetAppIconsSymbol.getWeatherIconFromCode(iconCode)
    return WidgetAppIconsSymbol.getSystemSymbolNameForIcon(customIcon)
}

/// 輔助函數 - 解析靈活的日期格式
func parseFlexibleDate(_ dateString: String) -> Date {
    let formatter = DateFormatter()
    
    // 嘗試多種格式
    let formats = [
        "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyy-MM-dd'T'HH:mm:ssZ",
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd"
    ]
    
    for format in formats {
        formatter.dateFormat = format
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        if let date = formatter.date(from: dateString) {
            return date
        }
    }
    
    // 如果都解析失敗，返回當前時間
    return Date()
}
