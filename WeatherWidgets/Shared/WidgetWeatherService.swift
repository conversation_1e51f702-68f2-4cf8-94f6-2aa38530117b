//
//  WidgetWeatherService.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import Foundation

/// Widget 專用的簡化天氣服務
class WidgetWeatherService {
    static let shared = WidgetWeatherService()
    private init() {} // 防止外部創建實例
    enum WeatherError: Error {
        case networkError
        case invalidResponse
        case invalidLocation
        case apiError(String)
        case decodingError
        case unknown
    }
    
    // private let baseURL = "https://weather-api.minlsm.com"
    // private let baseURL = "https://weather-api.minlsm-com.workers.dev"
    // private let baseURL = "https://0f3a54f4-weather-api.minlsm-com.workers.dev"
    private let baseURL = "http://localhost:8787"
    private let defaultDataSource = "AW"
    private let maxRetryAttempts = 5 // 最多重試 2 次（總共 3 次請求）
    private let retryDelay: TimeInterval = 3.0 // 重試間隔 1 秒

    /// 為 URLRequest 設置 API key
    /// - Parameter request: 要設置的 URLRequest
    private func setAPIKey(for request: inout URLRequest) {
        request.setValue("lUzaSyD97xHJnaaCsYMDKWqIR9vPCn9lwzb0qAI", forHTTPHeaderField: "x-api-key")
    }
    
    func getWeatherForCoordinates(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        getWeatherForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: 1, completion: completion)
    }

    private func getWeatherForCoordinatesWithRetry(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource, attemptNumber: Int, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        let dataSource = weatherSource.rawValue
        let languageCode = WidgetLanguageDetector.getCurrentLanguageCode()
        let urlString = "\(baseURL)/api/v1/current?data=\(dataSource)&lat=\(lat)&lon=\(lon)&plan=widget&lang=\(languageCode)"

        print("🌐 Widget Current API: 使用語言 = \(languageCode)")
        print("🔄 Widget Current API 嘗試 \(attemptNumber)/\(maxRetryAttempts + 1)")

        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidLocation))
            return
        }

        print("🔍 Widget API 請求: \(urlString)")

        var request = URLRequest(url: url)
        setAPIKey(for: &request)

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else {
                let fallbackData = WeatherData(location: locationName, temperature: 25, condition: "Sunny", humidity: 60, windSpeed: 5.0, precipitationProbability: 10, feelsLike: 27, iconCode: "01d", sunrise: nil, sunset: nil, timezoneOffset: 28800)
                completion(.success(fallbackData))
                return
            }

            // 檢查網路錯誤
            if let error = error {
                print("❌ Widget Current API 網路錯誤 (嘗試 \(attemptNumber)): \(error.localizedDescription)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Current API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeatherForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Current API 網路錯誤重試次數已用盡，使用降級數據")
                    let fallbackData = self.createFallbackWeatherData(locationName: locationName)
                    completion(.success(fallbackData))
                }
                return
            }

            // 檢查 HTTP 回應
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ Widget Current API 無效回應 (嘗試 \(attemptNumber))")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Current API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeatherForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Current API 無效回應重試次數已用盡，使用降級數據")
                    let fallbackData = self.createFallbackWeatherData(locationName: locationName)
                    completion(.success(fallbackData))
                }
                return
            }

            print("🔍 Widget HTTP 狀態碼 (嘗試 \(attemptNumber)): \(httpResponse.statusCode)")

            // 檢查狀態碼
            guard httpResponse.statusCode == 200 else {
                print("❌ Widget Current API HTTP 錯誤 (嘗試 \(attemptNumber)): \(httpResponse.statusCode)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Current API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeatherForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Current API HTTP 錯誤重試次數已用盡，使用降級數據")
                    let fallbackData = self.createFallbackWeatherData(locationName: locationName)
                    completion(.success(fallbackData))
                }
                return
            }

            // 檢查數據
            guard let data = data else {
                print("❌ Widget Current API 無數據 (嘗試 \(attemptNumber))")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Current API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeatherForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Current API 無數據重試次數已用盡，使用降級數據")
                    let fallbackData = self.createFallbackWeatherData(locationName: locationName)
                    completion(.success(fallbackData))
                }
                return
            }

            // 打印原始回應以便調試
            if let responseString = String(data: data, encoding: .utf8) {
                print("🔍 Widget API 回應: \(responseString)")
            }

            do {
                let response = try JSONDecoder().decode(MiddlewareCurrentResponse.self, from: data)
                print("✅ Widget Current API 成功 (嘗試 \(attemptNumber)): 數據筆數 \(response.current.count)")

                if let currentData = response.current.first {
                    let weatherData = self.mapToWeatherData(currentData: currentData, locationName: locationName)
                    completion(.success(weatherData))
                } else {
                    print("🔍 Widget: API 回應中沒有數據，使用降級數據")
                    let fallbackData = self.createFallbackWeatherData(locationName: locationName)
                    completion(.success(fallbackData))
                }
            } catch {
                print("❌ Widget Current API 解析錯誤 (嘗試 \(attemptNumber)): \(error)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Current API 解析錯誤，將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeatherForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Current API 解析錯誤重試次數已用盡，使用降級數據")
                    let fallbackData = self.createFallbackWeatherData(locationName: locationName)
                    completion(.success(fallbackData))
                }
            }
        }.resume()
    }
    
    /// 映射 API 數據到 WeatherData
    private func mapToWeatherData(currentData: MiddlewareCurrentItem, locationName: String) -> WeatherData {
        let sunrise = currentData.sunevents.sunrise.isValid ? currentData.sunevents.sunrise.dateValue : nil
        let sunset = currentData.sunevents.sunset.isValid ? currentData.sunevents.sunset.dateValue : nil
        
        return WeatherData(
            location: locationName,
            temperature: Int(round(currentData.temperature.current)),
            condition: currentData.weather.condition,
            humidity: currentData.atmosphere.humidity,
            windSpeed: currentData.wind.speed,
            precipitationProbability: currentData.precipitation.probability,
            feelsLike: Int(round(currentData.temperature.feels_like)),
            iconCode: currentData.weather.icon,
            sunrise: sunrise,
            sunset: sunset,
            timezoneOffset: 28800
        )
    }
    
    /// 建立降級天氣數據
    private func createFallbackWeatherData(locationName: String) -> WeatherData {
        return WeatherData(
            location: locationName,
            temperature: 25,
            condition: "Sunny",
            humidity: 60,
            windSpeed: 5.0,
            precipitationProbability: 10,
            feelsLike: 27,
            iconCode: "01d",
            sunrise: nil,
            sunset: nil,
            timezoneOffset: 28800
        )
    }

    /// 獲取每週預報數據
    func getWeeklyForecastForCoordinates(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource, completion: @escaping (Result<[WeeklyForecastData], WeatherError>) -> Void) {
        getWeeklyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: 1, completion: completion)
    }

    private func getWeeklyForecastForCoordinatesWithRetry(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource, attemptNumber: Int, completion: @escaping (Result<[WeeklyForecastData], WeatherError>) -> Void) {
        let dataSource = weatherSource.rawValue
        let languageCode = WidgetLanguageDetector.getCurrentLanguageCode()
        let urlString = "\(baseURL)/api/v1/weekly?data=\(dataSource)&lat=\(lat)&lon=\(lon)&lang=\(languageCode)"

        print("🌐 Widget Weekly API: 使用語言 = \(languageCode)")
        print("🔄 Widget Weekly API 嘗試 \(attemptNumber)/\(maxRetryAttempts + 1)")

        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidLocation))
            return
        }

        print("🔍 Widget Weekly API 請求: \(urlString)")

        var request = URLRequest(url: url)
        setAPIKey(for: &request)

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else {
                completion(.failure(.unknown))
                return
            }

            // 檢查網路錯誤
            if let error = error {
                print("❌ Widget Weekly API 網路錯誤 (嘗試 \(attemptNumber)): \(error.localizedDescription)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Weekly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeeklyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Weekly API 網路錯誤重試次數已用盡")
                    completion(.failure(.networkError))
                }
                return
            }

            // 檢查 HTTP 回應
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ Widget Weekly API 無效回應 (嘗試 \(attemptNumber))")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Weekly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeeklyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Weekly API 無效回應重試次數已用盡")
                    completion(.failure(.invalidResponse))
                }
                return
            }

            print("🔍 Widget Weekly HTTP 狀態碼 (嘗試 \(attemptNumber)): \(httpResponse.statusCode)")

            // 檢查狀態碼
            guard httpResponse.statusCode == 200 else {
                print("❌ Widget Weekly API HTTP 錯誤 (嘗試 \(attemptNumber)): \(httpResponse.statusCode)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Weekly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeeklyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Weekly API HTTP 錯誤重試次數已用盡")
                    completion(.failure(.apiError("HTTP \(httpResponse.statusCode)")))
                }
                return
            }

            // 檢查數據
            guard let data = data else {
                print("❌ Widget Weekly API 無數據 (嘗試 \(attemptNumber))")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Weekly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeeklyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Weekly API 無數據重試次數已用盡")
                    completion(.failure(.invalidResponse))
                }
                return
            }

            if let jsonString = String(data: data, encoding: .utf8) {
                print("🔍 Widget Weekly API 回應: \(jsonString)")
            }

            // 解析 JSON 回應
            do {
                let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
                let weeklyArray = jsonResponse?["weekly"] as? [[String: Any]] ?? []

                print("✅ Widget Weekly API 成功 (嘗試 \(attemptNumber)): 數據筆數 \(weeklyArray.count)")

                // Debug: 檢查第一筆數據的結構
                if let firstItem = weeklyArray.first {
                    print("🔍 Widget: 第一筆數據的 keys: \(firstItem.keys)")
                    if let weather = firstItem["weather"] as? [String: Any] {
                        print("🔍 Widget: weather keys: \(weather.keys)")
                    }
                    if let temperature = firstItem["temperature"] as? [String: Any] {
                        print("🔍 Widget: temperature keys: \(temperature.keys)")
                    }
                }

                let weeklyData = WidgetWeatherService.parseWeeklyForecastData(weeklyArray)
                completion(.success(weeklyData))
            } catch {
                print("❌ Widget Weekly API 解析錯誤 (嘗試 \(attemptNumber)): \(error)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Weekly API 解析錯誤，將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getWeeklyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Weekly API 解析錯誤重試次數已用盡")
                    completion(.failure(.decodingError))
                }
            }
        }.resume()
    }

    /// 獲取每小時預報數據
    func getHourlyForecastForCoordinates(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource, completion: @escaping (Result<[HourlyForecastData], WeatherError>) -> Void) {
        getHourlyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: 1, completion: completion)
    }

    private func getHourlyForecastForCoordinatesWithRetry(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource, attemptNumber: Int, completion: @escaping (Result<[HourlyForecastData], WeatherError>) -> Void) {
        let dataSource = weatherSource.rawValue
        let languageCode = WidgetLanguageDetector.getCurrentLanguageCode()
        let urlString = "\(baseURL)/api/v1/hourly?data=\(dataSource)&lat=\(lat)&lon=\(lon)&lang=\(languageCode)&plan=widget"

        print("🌐 Widget Hourly API: 使用語言 = \(languageCode)")
        print("🔄 Widget Hourly API 嘗試 \(attemptNumber)/\(maxRetryAttempts + 1)")

        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidLocation))
            return
        }

        print("🔍 Widget Hourly API 請求: \(urlString)")

        var request = URLRequest(url: url)
        setAPIKey(for: &request)

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else {
                completion(.failure(.unknown))
                return
            }

            // 檢查網路錯誤
            if let error = error {
                print("❌ Widget Hourly API 網路錯誤 (嘗試 \(attemptNumber)): \(error.localizedDescription)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Hourly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getHourlyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Hourly API 網路錯誤重試次數已用盡")
                    completion(.failure(.networkError))
                }
                return
            }

            // 檢查 HTTP 回應
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ Widget Hourly API 無效回應 (嘗試 \(attemptNumber))")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Hourly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getHourlyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Hourly API 無效回應重試次數已用盡")
                    completion(.failure(.invalidResponse))
                }
                return
            }

            print("🔍 Widget Hourly HTTP 狀態碼 (嘗試 \(attemptNumber)): \(httpResponse.statusCode)")

            // 檢查狀態碼
            guard httpResponse.statusCode == 200 else {
                print("❌ Widget Hourly API HTTP 錯誤 (嘗試 \(attemptNumber)): \(httpResponse.statusCode)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Hourly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getHourlyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Hourly API HTTP 錯誤重試次數已用盡")
                    completion(.failure(.apiError("HTTP \(httpResponse.statusCode)")))
                }
                return
            }

            // 檢查數據
            guard let data = data else {
                print("❌ Widget Hourly API 無數據 (嘗試 \(attemptNumber))")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Hourly API 將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getHourlyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Hourly API 無數據重試次數已用盡")
                    completion(.failure(.invalidResponse))
                }
                return
            }

            if let jsonString = String(data: data, encoding: .utf8) {
                print("🔍 Widget Hourly API 回應: \(jsonString)")
            }

            // 解析 JSON 回應
            do {
                let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
                let hourlyArray = jsonResponse?["hourly"] as? [[String: Any]] ?? []

                print("✅ Widget Hourly API 成功 (嘗試 \(attemptNumber)): 數據筆數 \(hourlyArray.count)")

                // Debug: 檢查第一筆數據的結構
                if let firstItem = hourlyArray.first {
                    print("🔍 Widget: 第一筆數據的 keys: \(firstItem.keys)")
                    if let weather = firstItem["weather"] as? [String: Any] {
                        print("🔍 Widget: weather keys: \(weather.keys)")
                    }
                    if let temperature = firstItem["temperature"] as? [String: Any] {
                        print("🔍 Widget: temperature keys: \(temperature.keys)")
                    }
                }

                let hourlyData = self.parseHourlyForecastData(hourlyArray)
                completion(.success(hourlyData))
            } catch {
                print("❌ Widget Hourly API 解析錯誤 (嘗試 \(attemptNumber)): \(error)")

                if attemptNumber <= self.maxRetryAttempts {
                    print("⏳ Widget Hourly API 解析錯誤，將在 \(self.retryDelay) 秒後重試")
                    DispatchQueue.global().asyncAfter(deadline: .now() + self.retryDelay) {
                        self.getHourlyForecastForCoordinatesWithRetry(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, attemptNumber: attemptNumber + 1, completion: completion)
                    }
                } else {
                    print("💥 Widget Hourly API 解析錯誤重試次數已用盡")
                    completion(.failure(.decodingError))
                }
            }
        }.resume()
    }

    /// 解析 weekly API 回應數據
    private static func parseWeeklyForecastData(_ weeklyArray: [[String: Any]]) -> [WeeklyForecastData] {
        let dateFormatter = ISO8601DateFormatter()
        dateFormatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        return weeklyArray.compactMap { dayData in
            print("🔍 Widget: 解析數據項目: \(dayData.keys)")

            // 檢查 timestamp
            guard let timestampString = dayData["timestamp"] as? String else {
                print("❌ Widget: 缺少 timestamp 欄位")
                return nil
            }

            guard let timestamp = dateFormatter.date(from: timestampString) else {
                print("❌ Widget: 無法解析 timestamp: \(timestampString)")
                // 嘗試備用的日期格式
                let backupFormatter = DateFormatter()
                backupFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
                backupFormatter.timeZone = TimeZone(abbreviation: "UTC")

                guard let backupTimestamp = backupFormatter.date(from: timestampString) else {
                    print("❌ Widget: 備用格式也無法解析 timestamp: \(timestampString)")
                    return nil
                }

                print("✅ Widget: 使用備用格式解析 timestamp 成功")
                return WidgetWeatherService.createWeeklyForecastData(dayData: dayData, timestamp: backupTimestamp)
            }

            return WidgetWeatherService.createWeeklyForecastData(dayData: dayData, timestamp: timestamp)
        }
    }

    /// 建立 WeeklyForecastData 物件
    private static func createWeeklyForecastData(dayData: [String: Any], timestamp: Date) -> WeeklyForecastData? {
        // 檢查 weather
        guard let weather = dayData["weather"] as? [String: Any] else {
            print("❌ Widget: 缺少 weather 欄位")
            return nil
        }

        guard let condition = weather["condition"] as? String else {
            print("❌ Widget: 缺少 weather.condition 欄位")
            return nil
        }

        guard let iconCode = weather["icon"] as? String else {
            print("❌ Widget: 缺少 weather.icon 欄位")
            return nil
        }

        // 檢查 temperature
        guard let temperature = dayData["temperature"] as? [String: Any] else {
            print("❌ Widget: 缺少 temperature 欄位")
            return nil
        }

        // 處理溫度可能是 Double 或 Int 的情況
        let currentTemp: Int
        if let tempDouble = temperature["current"] as? Double {
            currentTemp = Int(round(tempDouble))
        } else if let tempInt = temperature["current"] as? Int {
            currentTemp = tempInt
        } else {
            print("❌ Widget: 無法解析溫度數據 - current: \(temperature["current"] ?? "nil")")
            return nil
        }

        print("✅ Widget: 解析成功 - \(condition), \(currentTemp)°C")

        return WeeklyForecastData(
            date: timestamp,
            condition: condition,
            iconCode: iconCode,
            temperature: currentTemp
        )
    }

    /// 解析 hourly API 回應數據
    private func parseHourlyForecastData(_ hourlyArray: [[String: Any]]) -> [HourlyForecastData] {
        let dateFormatter = ISO8601DateFormatter()
        dateFormatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        return hourlyArray.compactMap { hourData in
            print("🔍 Widget: 解析數據項目: \(hourData.keys)")

            // 檢查 timestamp
            guard let timestampString = hourData["timestamp"] as? String else {
                print("❌ Widget: 缺少 timestamp 欄位")
                return nil
            }

            guard let timestamp = dateFormatter.date(from: timestampString) else {
                print("❌ Widget: 無法解析 timestamp: \(timestampString)")
                // 嘗試備用的日期格式
                let backupFormatter = DateFormatter()
                backupFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
                backupFormatter.timeZone = TimeZone(abbreviation: "UTC")

                guard let backupTimestamp = backupFormatter.date(from: timestampString) else {
                    print("❌ Widget: 備用格式也無法解析 timestamp: \(timestampString)")
                    return nil
                }

                print("✅ Widget: 使用備用格式解析 timestamp 成功")
                return self.createHourlyForecastData(hourData: hourData, timestamp: backupTimestamp)
            }

            return self.createHourlyForecastData(hourData: hourData, timestamp: timestamp)
        }
    }

    /// 建立 HourlyForecastData 物件
    private func createHourlyForecastData(hourData: [String: Any], timestamp: Date) -> HourlyForecastData? {
        // 檢查 weather
        guard let weather = hourData["weather"] as? [String: Any] else {
            print("❌ Widget: 缺少 weather 欄位")
            return nil
        }

        guard let condition = weather["condition"] as? String else {
            print("❌ Widget: 缺少 weather.condition 欄位")
            return nil
        }

        guard let iconCode = weather["icon"] as? String else {
            print("❌ Widget: 缺少 weather.icon 欄位")
            return nil
        }

        // 檢查 temperature
        guard let temperature = hourData["temperature"] as? [String: Any] else {
            print("❌ Widget: 缺少 temperature 欄位")
            return nil
        }

        // 處理溫度可能是 Double 或 Int 的情況
        let currentTemp: Int
        if let tempDouble = temperature["current"] as? Double {
            currentTemp = Int(round(tempDouble))
        } else if let tempInt = temperature["current"] as? Int {
            currentTemp = tempInt
        } else {
            print("❌ Widget: 無法解析溫度數據 - current: \(temperature["current"] ?? "nil")")
            return nil
        }

        // 檢查 precipitation
        guard let precipitation = hourData["precipitation"] as? [String: Any] else {
            print("❌ Widget: 缺少 precipitation 欄位")
            return nil
        }

        // 處理降雨機率可能是 Double 或 Int 的情況
        let precipitationProbability: Int
        if let probDouble = precipitation["probability"] as? Double {
            precipitationProbability = Int(round(probDouble))
        } else if let probInt = precipitation["probability"] as? Int {
            precipitationProbability = probInt
        } else {
            print("❌ Widget: 無法解析降雨機率數據 - probability: \(precipitation["probability"] ?? "nil")")
            return nil
        }

        print("✅ Widget: 解析成功 - \(condition), \(currentTemp)°C, 降雨機率: \(precipitationProbability)%")

        return HourlyForecastData(
            date: timestamp,
            condition: condition,
            iconCode: iconCode,
            temperature: currentTemp,
            precipitationProbability: precipitationProbability
        )
    }
}
