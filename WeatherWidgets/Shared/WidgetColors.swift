//
//  WidgetColors.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import SwiftUI

// MARK: - Widget 顏色管理

/// Widget 專用的顏色管理
struct WidgetColors {
    /// 主題模式
    enum ThemeMode: String {
        case light = "light"
        case dark = "dark"
        case system = "system"
    }

    /// 顏色主題
    enum ColorTheme {
        case primaryBackground
        case primaryText
        case secondaryText
    }

    /// 淺色主題顏色
    private static let lightColors: [ColorTheme: Color] = [
        .primaryBackground: Color(red: 0.976, green: 0.976, blue: 0.976), // F9F9F9
        .primaryText: Color(red: 0.133, green: 0.133, blue: 0.133),       // 222222
        .secondaryText: Color(red: 0.533, green: 0.533, blue: 0.533)      // 888888
    ]

    /// 深色主題顏色
    private static let darkColors: [ColorTheme: Color] = [
        .primaryBackground: Color(red: 0.133, green: 0.133, blue: 0.133), // 222222
        .primaryText: Color(red: 0.976, green: 0.976, blue: 0.976),       // F9F9F9
        .secondaryText: Color(red: 0.682, green: 0.682, blue: 0.714)      // AEAEB2
    ]

    /// 快取的主題狀態
    private static var cachedTheme: (theme: String, isDark: Bool, timestamp: Date)?

    /// 獲取當前主題狀態（帶快取）
    private static func getCurrentThemeState() -> (isDark: Bool, theme: String) {
        let now = Date()

        // 檢查快取是否有效（1秒內）
        if let cached = cachedTheme,
           now.timeIntervalSince(cached.timestamp) < 1.0 {
            return (cached.isDark, cached.theme)
        }

        // 重新讀取主題設定
        let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather")
        let themeRawValue = groupDefaults?.string(forKey: "themeMode") ?? "light"
        let themeMode = ThemeMode(rawValue: themeRawValue) ?? .light

        let isDarkMode: Bool
        switch themeMode {
        case .dark:
            isDarkMode = true
        case .light:
            isDarkMode = false
        case .system:
            if #available(iOS 13.0, *) {
                isDarkMode = UITraitCollection.current.userInterfaceStyle == .dark
            } else {
                isDarkMode = false
            }
        }

        // 只在主題變更時輸出 log
        if cachedTheme?.theme != themeRawValue || cachedTheme?.isDark != isDarkMode {
            print("🎨 Widget 主題變更: \(themeRawValue) -> \(isDarkMode ? "深色" : "淺色")模式")
        }

        // 更新快取
        cachedTheme = (themeRawValue, isDarkMode, now)

        return (isDarkMode, themeRawValue)
    }

    /// 獲取主題化顏色
    static func themed(_ colorTheme: ColorTheme) -> Color {
        let (isDarkMode, _) = getCurrentThemeState()
        let colorMap = isDarkMode ? darkColors : lightColors
        return colorMap[colorTheme] ?? Color.primary
    }
}
