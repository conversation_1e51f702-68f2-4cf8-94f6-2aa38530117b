//
//  WidgetFoundation.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import WidgetKit
import SwiftUI
import Intents

// MARK: - 共享類型定義

/// 溫度單位枚舉
enum TemperatureUnit: String, Codable, CaseIterable {
    /// 攝氏度
    case celsius
    /// 華氏度
    case fahrenheit

    /// 單位符號
    var symbol: String {
        switch self {
        case .celsius:
            return "°C"
        case .fahrenheit:
            return "°F"
        }
    }

    /// 溫度轉換 (從攝氏轉換到當前單位)
    /// - Parameter celsius: 攝氏度溫度
    /// - Returns: 轉換後的溫度
    func convert(fromCelsius celsius: Double) -> Double {
        switch self {
        case .celsius:
            return celsius
        case .fahrenheit:
            return celsius * 9 / 5 + 32
        }
    }
}

/// 時間格式枚舉
enum TimeFormat: String, Codable, CaseIterable {
    /// 12小時制
    case twelveHour
    /// 24小時制
    case twentyFourHour

    /// 是否為12小時制
    var is12Hour: Bool {
        return self == .twelveHour
    }

    /// 是否為24小時制
    var is24Hour: Bool {
        return self == .twentyFourHour
    }
}

/// Widget 語言檢測工具
struct WidgetLanguageDetector {
    /// 獲取當前系統語言對應的 API 語言參數
    /// - Returns: API 語言參數字串
    static func getCurrentLanguageCode() -> String {
        let systemLanguages = Locale.preferredLanguages

        for languageCode in systemLanguages {
            // 完整語言代碼匹配（包含地區）
            if languageCode.hasPrefix("en-AU") {
                return "en-AU"
            } else if languageCode.hasPrefix("en-GB") {
                return "en-GB"
            } else if languageCode.hasPrefix("fr-CA") {
                return "fr-CA"
            } else if languageCode.hasPrefix("da") {
                return "da-DK"
            } else if languageCode.hasPrefix("sv") {
                return "sv-SE"
            } else if languageCode.hasPrefix("fr") {
                return "fr-FR"
            } else if languageCode.hasPrefix("de") {
                return "de-DE"
            } else if languageCode.hasPrefix("no") || languageCode.hasPrefix("nb") || languageCode.hasPrefix("nn") {
                return "nb-NO"
            } else if languageCode.hasPrefix("fi") {
                return "fi-FI"
            } else if languageCode.hasPrefix("nl") {
                return "nl-NL"
            } else if languageCode.hasPrefix("it") {
                return "it-IT"
            } else if languageCode.hasPrefix("es") {
                return "es-ES"
            }

            // 提取語言代碼的主要部分（去掉地區代碼）
            let mainLanguageCode = String(languageCode.prefix(2))

            // 檢查是否為英文（預設美式英文）
            if mainLanguageCode == "en" {
                return "en-US"
            }
            // 檢查是否為中文（繁體或簡體）
            else if mainLanguageCode == "zh" {
                return "zh-TW"
            }
            // 檢查是否為日文
            else if mainLanguageCode == "ja" {
                return "ja-JP"
            }
        }

        // 預設使用英文
        return "en-US"
    }

    /// 獲取當前系統語言對應的 Locale 識別符（對應 .lproj 資料夾名稱）
    /// - Returns: Locale 識別符字串
    static func getCurrentLocaleIdentifier() -> String {
        let systemLanguages = Locale.preferredLanguages
        
        for languageCode in systemLanguages {
            // 提取語言代碼的主要部分（去掉地區代碼）
            let mainLanguageCode = String(languageCode.prefix(2))
            
            // 根據主要語言代碼返回對應的 .lproj 資料夾名稱
            switch mainLanguageCode {
            case "en":
                return "en"
            case "zh":
                return "zh-Hant"
            case "ja":
                return "ja"
            case "fr":
                return "fr"
            case "de":
                return "de"
            case "es":
                return "es"
            case "it":
                return "it"
            case "nl":
                return "nl"
            case "da":
                return "da"
            case "sv":
                return "sv"
            case "no", "nb", "nn":
                return "nb"
            case "fi":
                return "fi"
            default:
                return "en"  // 預設使用英文
            }
        }
        
        // 預設使用英文
        return "en"
    }
}

/// Widget 多語系管理器
struct WidgetLocalizer {
    /// 獲取本地化文字
    /// - Parameter key: 文字鍵值
    /// - Returns: 本地化後的文字
    static func localized(_ key: String) -> String {
        // 直接使用 Widget Extension 的 Bundle
        let widgetBundle = Bundle(for: WidgetBundleClass.self)
        
        // 使用標準的 NSLocalizedString，它會自動根據系統語言選擇對應的 .lproj 資料夾
        let localizedString = NSLocalizedString(key, bundle: widgetBundle, comment: "")
        
        // 如果找到翻譯（localizedString != key），直接返回
        if localizedString != key {
            return localizedString
        }
        
        // 如果沒找到，嘗試手動載入對應語言的 Bundle
        let currentLanguage = WidgetLanguageDetector.getCurrentLocaleIdentifier()
        
        if let languagePath = widgetBundle.path(forResource: currentLanguage, ofType: "lproj"),
           let languageBundle = Bundle(path: languagePath) {
            
            let manualString = languageBundle.localizedString(forKey: key, value: nil, table: nil)
            if manualString != key {
                return manualString
            }
        }
        
        // 最後嘗試英文後備
        if currentLanguage != "en" {
            if let englishPath = widgetBundle.path(forResource: "en", ofType: "lproj"),
               let englishBundle = Bundle(path: englishPath) {
                
                let englishString = englishBundle.localizedString(forKey: key, value: nil, table: nil)
                if englishString != key {
                    return englishString
                }
            }
        }
        
        // 如果都找不到，返回 key 本身
        return key
    }

    /// 獲取本地化的星期名稱
    /// - Parameter weekday: 星期數字 (1=Sunday, 2=Monday, ..., 7=Saturday)
    /// - Returns: 本地化的星期名稱
    static func getLocalizedWeekdayName(for weekday: Int) -> String {
        switch weekday {
        case 1: return WidgetLocalizer.localized("sunday")
        case 2: return WidgetLocalizer.localized("monday")
        case 3: return WidgetLocalizer.localized("tuesday")
        case 4: return WidgetLocalizer.localized("wednesday")
        case 5: return WidgetLocalizer.localized("thursday")
        case 6: return WidgetLocalizer.localized("friday")
        case 7: return WidgetLocalizer.localized("saturday")
        default: return WidgetLocalizer.localized("monday") // 預設值
        }
    }
}

/// 用於獲取 Widget Bundle 的類別
private class WidgetBundleClass: NSObject {}
