//
//  WidgetTypes.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import Foundation
import WidgetKit

// MARK: - 類型定義

/// 天氣來源枚舉
enum WeatherSource: String, Codable, CaseIterable {
    /// Apple Weather
    case AW
    /// OpenWeather
    case OW
    /// Google Weather
    case GW
    /// Central Weather (Taiwan)
    case CW

    /// 天氣來源顯示名稱
    var displayName: String {
        switch self {
        case .AW:
            return "Apple Weather"
        case .OW:
            return "OpenWeather"
        case .GW:
            return "Google Weather"
        case .CW:
            return WidgetLocalizer.localized("central_weather_administration")
        }
    }

    /// 檢查該來源是否支持指定的國家代碼
    func isSupported(in countryCode: String) -> Bool {
        switch self {
        case .AW, .OW:
            return true
        case .CW:
            return countryCode == "TW"
        case .GW:
            return countryCode != "JP" && countryCode != "KR"
        }
    }

    /// 為指定國家提供預設天氣來源
    static func defaultSource(for countryCode: String) -> WeatherSource {
        switch countryCode {
        case "TW":
            return .OW
        case "JP", "KR":
            return .OW
        default:
            return .OW
        }
    }
}

/// 已儲存位置模型
struct SavedLocation: Identifiable, Codable, Hashable {
    let id: UUID
    var name: String
    var formattedAddress: String
    let lat: Double
    let lon: Double
    let country: String
    var weatherSource: WeatherSource?
    var timezone: String?
    
    /// 有效的天氣來源
    var effectiveWeatherSource: WeatherSource {
        return weatherSource ?? WeatherSource.defaultSource(for: country)
    }
    
    init(id: UUID = UUID(), name: String, formattedAddress: String = "", lat: Double, lon: Double, country: String = "", weatherSource: WeatherSource? = nil, timezone: String? = nil) {
        self.id = id
        self.name = name
        self.formattedAddress = formattedAddress
        self.lat = lat
        self.lon = lon
        self.country = country
        self.weatherSource = weatherSource
        self.timezone = timezone
    }
}

/// 天氣數據模型
struct WeatherData: Codable {
    let location: String
    let temperature: Int
    let condition: String
    let humidity: Int
    let windSpeed: Double
    let precipitationProbability: Int
    let feelsLike: Int
    let iconCode: String
    let sunrise: Date?
    let sunset: Date?
    let timezoneOffset: Int
}

/// 每日預報數據模型
struct DailyForecast {
    let dayName: String
    let condition: String
    let iconCode: String
    let lowTemp: Int
    let currentTemp: Int  // API 回傳的 temperature.current 值
}

/// 一週預報數據模型
struct WeeklyForecastData {
    let date: Date
    let condition: String
    let iconCode: String
    let temperature: Int
}

/// 每小時預報數據模型
struct HourlyForecastData {
    let date: Date
    let condition: String
    let iconCode: String
    let temperature: Int
    let precipitationProbability: Int
}

/// 每小時預報項目（用於 Widget 顯示）
struct HourlyForecast {
    let timeString: String
    let condition: String
    let iconCode: String
    let precipitationProbability: Int
}

// MARK: - Widget Entry Models

struct CurrentWeatherEntry: TimelineEntry {
    let date: Date
    let temperature: String
    let condition: String
    let iconCode: String
    let locationName: String
}

struct WeeklyForecastEntry: TimelineEntry {
    let date: Date
    let locationName: String
    let forecasts: [DailyForecast]
}

struct HorizontalWeeklyForecastEntry: TimelineEntry {
    let date: Date
    let locationName: String
    let forecasts: [DailyForecast]
}

struct HourlyForecastEntry: TimelineEntry {
    let date: Date
    let locationName: String
    let forecasts: [HourlyForecast]
}

// MARK: - API 回應結構

/// 中繼 API 即時天氣回應
struct MiddlewareCurrentResponse: Codable {
    let current: [MiddlewareCurrentItem]
    let metadata: MiddlewareMetadata?
}

struct MiddlewareCurrentItem: Codable {
    let timestamp: String
    let weather: MiddlewareWeather
    let temperature: MiddlewareTemperature
    let atmosphere: MiddlewareAtmosphere
    let wind: MiddlewareWind
    let precipitation: MiddlewarePrecipitation
    let clouds: MiddlewareClouds
    let uv: MiddlewareUV
    let sunevents: MiddlewareSunEvents
}

struct MiddlewareMetadata: Codable {
    let source: String?
    let timestamp: String?
}

struct MiddlewareWeather: Codable {
    let condition: String
    let icon: String
}

struct MiddlewareTemperature: Codable {
    let current: Double
    let feels_like: Double
    let min: Double
    let max: Double
}

struct MiddlewareAtmosphere: Codable {
    let humidity: Int
    let sea_level: Double
    let ground_level: Double
    let visibility: Double
}

struct MiddlewareWind: Codable {
    let speed: Double
    let direction: Double
    let gust: Double
}

struct MiddlewarePrecipitation: Codable {
    let rain_amount: Double
    let snow_amount: Double
    let probability: Int
}

struct MiddlewareClouds: Codable {
    let coverage: Int
}

struct MiddlewareUV: Codable {
    let index: Int
}

struct MiddlewareSunEvents: Codable {
    let sunrise: SunTime
    let sunset: SunTime

    enum SunTime: Codable {
        case string(String)
        case number(Double)

        init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            if let stringValue = try? container.decode(String.self) {
                self = .string(stringValue)
            } else if let doubleValue = try? container.decode(Double.self) {
                self = .number(doubleValue)
            } else {
                throw DecodingError.typeMismatch(SunTime.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Expected String or Double"))
            }
        }

        func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            switch self {
            case .string(let value):
                try container.encode(value)
            case .number(let value):
                try container.encode(value)
            }
        }

        var isValid: Bool {
            switch self {
            case .string(let value):
                return !value.isEmpty
            case .number(let value):
                return value != -999 && value != -99
            }
        }

        var dateValue: Date? {
            switch self {
            case .string(let value):
                return parseFlexibleDateString(value)
            case .number(let value):
                return value != -999 && value != -99 ? Date(timeIntervalSince1970: value) : nil
            }
        }
    }
}

// MARK: - 輔助函數

private func parseFlexibleDateString(_ dateString: String) -> Date {
    let formatter = DateFormatter()

    // 嘗試多種格式
    let formats = [
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd'T'HH:mm:ss",
        "yyyy-MM-dd'T'HH:mm:ssZ",
        "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
        "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        "yyyy-MM-dd HH:mm:ss Z"
    ]

    for format in formats {
        formatter.dateFormat = format
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        if let date = formatter.date(from: dateString) {
            return date
        }
    }

    // 如果都解析失敗，返回當前時間
    return Date()
}


