//
//  WidgetAppIconsSymbol.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import SwiftUI

/// Widget 專用的 AppIcons 符號顯示工具類
struct WidgetAppIconsSymbol {
    
    // MARK: - 公開方法：創建視圖
    
    /// 創建一個顯示自定義圖標的 Text 視圖
    /// - Parameters:
    ///   - iconString: 符號字符串，來自 WidgetAppIcons
    ///   - fontSize: 字體大小
    ///   - color: 文本顏色
    /// - Returns: 配置好的 Text 視圖
    static func createView(for iconString: String, fontSize: CGFloat, color: Color = .primary) -> some View {
        Group {
            if isCustomFontAvailable() {
                // 使用自定義字體
                Text(iconString)
                    .font(.custom(WidgetFontLoader.customFontName, size: fontSize))
                    .foregroundColor(color)
                    .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
                    .scaledToFit() // 確保圖標適應容器大小
            } else {
                // 使用SF Symbols作為備用
                getSymbolForIcon(iconString)
                    .font(.system(size: fontSize))
                    .foregroundColor(color)
                    .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
                    .imageScale(.medium) // 固定圖標大小
            }
        }
    }
    
    /// 創建天氣圖標視圖，自動選擇自定義字體或系統圖標
    /// - Parameters:
    ///   - condition: 天氣狀況或圖標代碼
    ///   - fontSize: 字體大小
    ///   - color: 顏色
    /// - Returns: 配置好的視圖
    static func createWeatherIconView(for condition: String, fontSize: CGFloat, color: Color = .primary) -> some View {
        Group {
            if isCustomFontAvailable() {
                // 使用自定義字體
                Text(getWeatherIconFromCode(condition))
                    .font(.custom(WidgetFontLoader.customFontName, size: fontSize))
                    .foregroundColor(color)
            } else {
                // 使用系統圖標
                Image(systemName: getSystemSymbolNameForIcon(getWeatherIconFromCode(condition)))
                    .font(.system(size: fontSize))
                    .foregroundColor(color)
            }
        }
    }
    
    // MARK: - 輔助方法
    
    /// 判斷自定義字體是否可用
    /// - Returns: 是否可用
    private static func isCustomFontAvailable() -> Bool {
        return WidgetFontLoader.isCustomFontAvailable()
    }
    
    /// 根據圖標字符獲取對應的系統SF Symbols
    /// - Parameter iconString: 圖標字符串
    /// - Returns: 對應的系統圖標
    static func getSymbolForIcon(_ iconString: String) -> Image {
        Image(systemName: getSystemSymbolNameForIcon(iconString))
    }
    
    /// 獲取與 WidgetAppIcons 圖標對應的系統圖標名稱
    /// - Parameter iconString: 圖標字符串
    /// - Returns: 系統圖標名稱
    static func getSystemSymbolNameForIcon(_ iconString: String) -> String {
        switch iconString {
        case WidgetAppIcons.clearDay:
            return "sun.max.fill"
        case WidgetAppIcons.clearNight:
            return "moon.stars.fill"
        case WidgetAppIcons.fewCloudsDay:
            return "cloud.sun.fill"
        case WidgetAppIcons.fewCloudsNight:
            return "cloud.moon.fill"
        case WidgetAppIcons.scatteredClouds:
            return "cloud.fill"
        case WidgetAppIcons.brokenClouds:
            return "smoke.fill"
        case WidgetAppIcons.showerRain:
            return "cloud.drizzle.fill"
        case WidgetAppIcons.rain:
            return "cloud.rain.fill"
        case WidgetAppIcons.thunderstorm:
            return "cloud.bolt.fill"
        case WidgetAppIcons.snow:
            return "snow"
        case WidgetAppIcons.mist:
            return "cloud.fog.fill"
        case WidgetAppIcons.sunrise:
            return "sunrise.fill"
        case WidgetAppIcons.sunset:
            return "sunset.fill"
        case WidgetAppIcons.temperature:
            return "thermometer"
        case WidgetAppIcons.humidity:
            return "humidity"
        default:
            return "questionmark.circle"
        }
    }
    
    // MARK: - 天氣圖標轉換方法
    
    /// 根據OpenWeatherMap的icon代碼獲取WidgetAppIcons字符
    /// - Parameter iconCode: OpenWeatherMap API的icon代碼 (如："01d"、"02n"等)
    /// - Returns: 對應的WidgetAppIcons字符
    static func getWeatherIconFromCode(_ iconCode: String) -> String {
        switch iconCode {
        case "01d": // 晴天 (白天)
            return WidgetAppIcons.clearDay
        case "01n": // 晴天 (夜間)
            return WidgetAppIcons.clearNight
        case "02d": // 少雲 (白天)
            return WidgetAppIcons.fewCloudsDay
        case "02n": // 少雲 (夜間)
            return WidgetAppIcons.fewCloudsNight
        case "03d", "03n": // 多雲 (散雲)
            return WidgetAppIcons.scatteredClouds
        case "04d", "04n": // 多雲 (碎雲)
            return WidgetAppIcons.brokenClouds
        case "09d", "09n": // 小雨
            return WidgetAppIcons.showerRain
        case "10d", "10n": // 雨
            return WidgetAppIcons.rain
        case "11d", "11n": // 雷雨
            return WidgetAppIcons.thunderstorm
        case "13d", "13n": // 雪
            return WidgetAppIcons.snow
        case "50d", "50n": // 霧
            return WidgetAppIcons.mist
        default:
            return WidgetAppIcons.fewCloudsDay
        }
    }
    
    /// 根據天氣圖標代碼返回對應的天氣狀況描述
    /// - Parameter iconCode: 天氣圖標代碼
    /// - Returns: 對應的天氣狀況描述
    static func getWeatherConditionFromCode(_ iconCode: String) -> String {
        // 新的 API 已直接提供本地化描述，此方法僅用於備用情況
        switch iconCode {
        case "01d", "01n":
            return "晴朗"
        case "02d", "02n":
            return "少雲"
        case "03d", "03n":
            return "多雲"
        case "04d", "04n":
            return "陰天"
        case "09d", "09n":
            return "陣雨"
        case "10d", "10n":
            return "小雨"
        case "11d", "11n":
            return "雷雨"
        case "13d", "13n":
            return "雪"
        case "50d", "50n":
            return "霧"
        default:
            return "未知天氣"
        }
    }
}
