//
//  WeatherWidgetsBundle.swift
//  WeatherWidgets
//
//  Created by <PERSON><PERSON> on 2025/7/17.
//

import WidgetKit
import SwiftUI

// MARK: - Widget Bundle

@main
struct WeatherWidgetsBundle: WidgetBundle {
    init() {
        // 在 Widget Bundle 初始化時加載自定義字體
        loadCustomFonts()
    }

    var body: some Widget {
        CurrentWeatherWidget()
        WeeklyForecastWidget()
        HourlyForecastWidget()
        HorizontalWeeklyForecastWidget()
    }

    // MARK: - 私有方法

    /// 加載自定義字體
    private func loadCustomFonts() {
        Logger.debug("🔍 Widget Bundle: 開始加載自定義字體")

        // 嘗試使用主要方法加載字體
        if WidgetFontLoader.loadCustomFonts() {
            Logger.success("Widget Bundle: 使用主要方法成功加載字體")
            return
        }

        // 如果主要方法失敗，嘗試備用方法
        if WidgetFontLoader.loadCustomFontsUsingData() {
            Logger.success("Widget Bundle: 使用備用方法成功加載字體")
            return
        }

        Logger.error("Widget Bundle: 字體加載失敗，將使用系統圖標作為備用")

        // 調試：列出可用字體（僅在調試模式下）
        #if DEBUG
        WidgetFontLoader.listAllAvailableFonts()
        #endif
    }
}

// MARK: - Preview

struct WeatherWidgets_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Current Weather Widget Preview
            CurrentWeatherWidgetEntryView(entry: CurrentWeatherEntry(
                date: Date(),
                temperature: "25",
                condition: "Sunny",
                iconCode: "01d",
                locationName: "Taipei"
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .previewDisplayName(WidgetLocalizer.localized("current_weather"))

            // Weekly Forecast Widget Preview (Medium)
            WeeklyForecastWidgetEntryView(entry: WeeklyForecastEntry(
                date: Date(),
                locationName: "Taipei",
                forecasts: [
                    DailyForecast(dayName: WidgetLocalizer.localized("today"), condition: "Sunny", iconCode: "01d", lowTemp: 18, currentTemp: 25),
                    DailyForecast(dayName: WidgetLocalizer.localized("tomorrow"), condition: "Cloudy", iconCode: "02d", lowTemp: 16, currentTemp: 23),
                    DailyForecast(dayName: "Wed", condition: "Rainy", iconCode: "09d", lowTemp: 14, currentTemp: 20),
                    DailyForecast(dayName: "Thu", condition: "Sunny", iconCode: "01d", lowTemp: 17, currentTemp: 24),
                    DailyForecast(dayName: "Fri", condition: "Cloudy", iconCode: "03d", lowTemp: 15, currentTemp: 22)
                ]
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            .previewDisplayName(WidgetLocalizer.localized("weekly_forecast") + " (Medium)")

            // Weekly Forecast Widget Preview (Small)
            WeeklyForecastWidgetEntryView(entry: WeeklyForecastEntry(
                date: Date(),
                locationName: "Taipei",
                forecasts: [
                    DailyForecast(dayName: WidgetLocalizer.localized("today"), condition: "Sunny", iconCode: "01d", lowTemp: 18, currentTemp: 25),
                    DailyForecast(dayName: WidgetLocalizer.localized("tomorrow"), condition: "Cloudy", iconCode: "02d", lowTemp: 16, currentTemp: 23)
                ]
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .previewDisplayName(WidgetLocalizer.localized("weekly_forecast") + " (Small)")

            // Hourly Forecast Widget Preview (Medium)
            HourlyForecastWidgetEntryView(entry: HourlyForecastEntry(
                date: Date(),
                locationName: "Taipei",
                forecasts: [
                    HourlyForecast(timeString: "14:00", condition: "Sunny", iconCode: "01d", precipitationProbability: 10),
                    HourlyForecast(timeString: "15:00", condition: "Cloudy", iconCode: "02d", precipitationProbability: 30),
                    HourlyForecast(timeString: "16:00", condition: "Rainy", iconCode: "09d", precipitationProbability: 80),
                    HourlyForecast(timeString: "17:00", condition: "Partly Cloudy", iconCode: "03d", precipitationProbability: 20),
                    HourlyForecast(timeString: "18:00", condition: "Clear", iconCode: "01n", precipitationProbability: 5)
                ]
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            .previewDisplayName(WidgetLocalizer.localized("hourly_forecast") + " (Medium)")

            // Hourly Forecast Widget Preview (Small)
            HourlyForecastWidgetEntryView(entry: HourlyForecastEntry(
                date: Date(),
                locationName: "Taipei",
                forecasts: [
                    HourlyForecast(timeString: "14:00", condition: "Sunny", iconCode: "01d", precipitationProbability: 10),
                    HourlyForecast(timeString: "15:00", condition: "Cloudy", iconCode: "02d", precipitationProbability: 30)
                ]
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .previewDisplayName(WidgetLocalizer.localized("hourly_forecast") + " (Small)")

            // Horizontal Weekly Forecast Widget Preview
            HorizontalWeeklyForecastWidgetEntryView(entry: HorizontalWeeklyForecastEntry(
                date: Date(),
                locationName: "Taipei",
                forecasts: [
                    DailyForecast(dayName: WidgetLocalizer.localized("today"), condition: "Sunny", iconCode: "01d", lowTemp: 18, currentTemp: 25),
                    DailyForecast(dayName: WidgetLocalizer.localized("tomorrow"), condition: "Cloudy", iconCode: "02d", lowTemp: 16, currentTemp: 23),
                    DailyForecast(dayName: "Wed", condition: "Rainy", iconCode: "09d", lowTemp: 14, currentTemp: 20),
                    DailyForecast(dayName: "Thu", condition: "Sunny", iconCode: "01d", lowTemp: 17, currentTemp: 24),
                    DailyForecast(dayName: "Fri", condition: "Cloudy", iconCode: "03d", lowTemp: 15, currentTemp: 22)
                ]
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            .previewDisplayName(WidgetLocalizer.localized("weekly_forecast") + " " + WidgetLocalizer.localized("horizontal"))
        }
    }
}
