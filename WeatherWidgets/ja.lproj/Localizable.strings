/* 
  Localizable.strings
  WeatherWidgets (Japanese)
*/

// Day names (short form for widget)
"today" = "今日";
"tomorrow" = "明";
"monday" = "月";
"tuesday" = "火";
"wednesday" = "水";
"thursday" = "木";
"friday" = "金";
"saturday" = "土";
"sunday" = "日";

// Widget states
"loading" = "読み込み中...";
"select_location" = "地点を選択";
"config_error" = "設定エラー";
"load_failed" = "読み込めません";
"not_set" = "未設定";
"no_weather_data" = "気象情報を読み込めません";
"please_set_location" = "地点を設定してください";

// Widget titles
"5_day_forecast" = "5日間予報";
"current_weather" = "現在の天気";
"weekly_forecast" = "5日間予報";
"hourly_forecast" = "時間別予報";

// Widget descriptions
"current_weather_desc" = "現在の気象情報を表示します";
"weekly_forecast_desc" = "5日間の天気予報を表示します";
"hourly_forecast_desc" = "今後5時間の天気と降水確率を表示します";
"horizontal_weekly_forecast_desc" = "5日間の天気予報を横向きに表示します";

// Widget layout
"horizontal" = "横向き";

// Preview and test
"snapshot" = "東京";
"test" = "晴れ時々曇り";

// Weather sources
"central_weather_administration" = "中央気象署";
