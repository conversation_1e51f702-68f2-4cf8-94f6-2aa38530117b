---
type: "manual"
---

# MinimalistWeather UI/UX 設計指南

本文件詳細說明 MinimalistWeather 專案的 UI 設計風格、元件使用規範與視覺一致性要求。

## 整體風格

- **極簡主義**：簡潔、留白、無多餘裝飾元素
- **黑白主色調**：主要使用黑色文字、白/灰色背景，避免過多彩色
- **專注內容**：將天氣數據作為視覺焦點，其他元素弱化處理
- **動畫增強**：使用Lottie動畫提升天氣狀況的視覺表現

## 主題系統

### 顏色管理
所有顏色必須使用 `HexColor.themed()` 方法，支援主題切換：

```swift
// 正確用法 - 支援主題切換
Text("天氣")
    .foregroundColor(HexColor.themed(.primaryText))

// 舊版用法 - 僅支援固定顏色
Text("天氣")
    .foregroundColor(HexColor.color("222222"))

// 錯誤用法
Text("天氣")
    .foregroundColor(.black)
```

### 主題模式
- **淺色主題**：所有用戶可用
- **深色主題**：Pro功能，免費用戶顯示Paywall
- **主題切換**：即時生效，無需重啟應用

### 標準顏色定義
```swift
// 主題化顏色
HexColor.themed(.primaryText)      // 主要文字
HexColor.themed(.secondaryText)    // 次要文字
HexColor.themed(.primaryBackground) // 主背景
HexColor.themed(.errorText)        // 錯誤提示
```

## 字型系統

### 自訂字體
字型加載由 `FontLoader.swift` 管理，支援兩種自訂字體：

- **Lim.ttf**：主要用於標題和重要數字
- **系統字體**：用於一般文字內容

### 字體使用規範
```swift
// 標題文字 (使用 Lim)
Text("設定")
    .font(.custom("Lim", size: CGFloat(24).auto()))
    .foregroundColor(HexColor.themed(.primaryText))

// 一般文字 (系統字體)
Text("描述文字")
    .font(.system(size: CGFloat(16).auto()))
    .foregroundColor(HexColor.themed(.secondaryText))
```

### 多語系字體支援
- 自動偵測系統語言，載入對應字體
- 支援13種語言的字體顯示
- 字體載入失敗時自動降級到系統字體

## 圖示與動畫系統

### 圖示系統
所有圖示必須使用 `AppIconsSymbol.swift` 創建，支援主題化顏色：

```swift
// 正確用法 - 主題化圖示
AppIconsSymbol.createView(
    for: AppIcons.humidity,
    fontSize: CGFloat(40).auto(),
    color: HexColor.themed(.primaryText)
)

// 錯誤用法
Image(systemName: "humidity")
```

### Lottie動畫系統
天氣狀況使用Lottie動畫增強視覺效果：

```swift
// 天氣動畫
CustomLottieView(
    animationName: "clearDay",
    loopMode: .loop,
    speed: 1.0
)
.frame(width: CGFloat(100).auto(), height: CGFloat(100).auto())
```

#### 可用動畫檔案
- `clearDay.json` - 晴天
- `clearNight.json` - 晴夜
- `rain.json` - 雨天
- `snow.json` - 雪天
- `thunderstorm.json` - 雷雨
- `mist.json` - 霧天
- `fewCloudsDay.json` - 少雲（白天）
- `fewCloudsNight.json` - 少雲（夜晚）
- `scatteredClouds.json` - 散雲
- `brokenClouds.json` - 多雲
- `showerRain.json` - 陣雨

## 頁面導航與引導流程

### 主導航系統
- **垂直分頁**：使用 UIPageViewController 實現天氣/設定頁面切換
- **模態視圖**：所有子頁面使用 .sheet() 或 .fullScreenCover() 呈現
- **無導航欄**：保持極簡設計，不使用 NavigationView

```swift
// 主分頁結構 - 支援主題
struct MainPagingView: View {
    var body: some View {
        VerticalPageView(viewModel: viewModel, isShowingLocationSearch: $isShowingLocationSearch)
            .background(HexColor.themed(.primaryBackground))
            .edgesIgnoringSafeArea(.all)
    }
}

// 全螢幕模態頁面
.fullScreenCover(isPresented: $isShowingLocationSearch) {
    LocationSearchView(viewModel: viewModel, scrollToTopAction: {
        NotificationCenter.default.post(name: Notification.Name("ScrollToPage1"), object: nil)
    })
}
```

### 引導流程 (Onboarding)
4步驟引導流程，支援智能跳過：

1. **歡迎頁面** (`OnboardingWelcomeView`)
2. **方案選擇** (`OnboardingFeaturePlanView`) - IAP整合
3. **位置設定** (`OnboardingLocationView`) - 可智能跳過
4. **完成頁面** (`OnboardingCompleteView`)

```swift
// 引導流程協調器
OnboardingCoordinator(
    isOnboardingCompleted: $isOnboardingCompleted,
    shouldShowOnboarding: $shouldShowOnboarding
)
```

## 自適應佈局（AutoInch）

所有視圖元素的尺寸、間距必須使用 AutoInch 框架的擴展方法實現自適應：

```swift
// 引入 AutoInch
import AutoInch

// 自適應字體尺寸
.font(.system(size: CGFloat(16).auto())) // 自適應不同螢幕

// 自適應間距
.padding(.horizontal, CGFloat(20).auto())
.padding(.top, CGFloat(15).auto())

// 自適應寬高
.frame(width: CGFloat(100).auto(), height: CGFloat(50).auto())

// 自適應圓角
.cornerRadius(CGFloat(10).auto())
```

## UIPageViewController 導航實現

垂直分頁導航使用 UIViewControllerRepresentable 包裝 UIPageViewController：

```swift
// 垂直分頁視圖包裝器
struct VerticalPageView: UIViewControllerRepresentable {
    @ObservedObject var viewModel: WeatherViewModel
    @Binding var isShowingLocationSearch: Bool
    
    func makeUIViewController(context: Context) -> UIPageViewController {
        let pageViewController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .vertical) // 垂直方向導航
        
        pageViewController.dataSource = context.coordinator
        pageViewController.delegate = context.coordinator
        
        // 設置初始頁面
        if let firstVC = context.coordinator.viewControllers.first {
            pageViewController.setViewControllers([firstVC], direction: .forward, animated: false)
        }
        
        return pageViewController
    }
    
    // 其他實現...
}
```

## 布局規範

- **水平邊距**：統一使用 CGFloat(60).auto() (變數名 `horizontalPadding` 或 `displayAreaPadding`)
- **元素間距**：大區塊間 CGFloat(20-30).auto()，小元素間 CGFloat(10).auto()
- **對齊方式**：主要內容居中，次要內容左對齊

## 互動反饋

- **按鈕**：無背景色，僅使用文字或圖示，點擊時無明顯視覺反饋
- **列表項**：整行可點擊，使用 `.contentShape(Rectangle())`
- **加載狀態**：使用 [LoadingView.swift](mdc:MinimalistWeather/Utilities/LoadingView.swift) 顯示加載動畫

## 特殊狀態處理

### 網路狀態
- **離線檢測**：`NetworkMonitor.swift` 即時監控
- **離線UI**：全螢幕遮罩，支援多語系提示
- **重連處理**：網路恢復時自動重試API請求

```swift
// 網路離線覆蓋層 - 支援主題和多語系
if !networkMonitor.isConnected {
    ZStack {
        HexColor.themed(.primaryBackground)
            .ignoresSafeArea(.all)

        VStack(spacing: CGFloat(20).auto()) {
            AppIconsSymbol.createView(
                for: AppIcons.wifi,
                fontSize: CGFloat(60).auto(),
                color: HexColor.themed(.secondaryText)
            )

            Text("no_connection_alert".localized)
                .font(.custom("Lim", size: CGFloat(24).auto()))
                .foregroundColor(HexColor.themed(.primaryText))

            Text("connect_internet_message".localized)
                .font(.system(size: CGFloat(16).auto()))
                .foregroundColor(HexColor.themed(.secondaryText))
                .multilineTextAlignment(.center)
        }
        .padding(CGFloat(40).auto())
    }
    .transition(.opacity)
    .animation(.easeInOut(duration: 0.3), value: networkMonitor.isConnected)
    .zIndex(100)
}
```

### Pro功能限制
- **免費用戶限制**：顯示Paywall而非功能
- **Pro檢查**：使用 `iapService.isPro` 或 `isEntitlementActive("pro")`
- **優雅降級**：免費用戶看到簡化版功能

```swift
// Pro功能檢查
if iapService.isPro {
    // 顯示完整功能
    AdvancedWeatherChart()
} else {
    // 顯示Paywall
    Button("升級到Pro") {
        showPaywall = true
    }
}
```

### 載入狀態
- **統一載入動畫**：使用 `LoadingView.swift`
- **支援主題**：載入動畫顏色跟隨主題
- **適當大小**：根據內容區域調整動畫尺寸

```swift
// 載入狀態顯示
if viewModel.isLoading {
    LoadingView(
        color: HexColor.themed(.primaryText),
        size: CGFloat(40).auto(),
        speed: 0.8
    )
}
```

## 動畫效果

- **過渡動畫**：使用 `.animation(.easeInOut)`，持續時間 0.2-0.3 秒
- **頁面切換**：使用 `withAnimation` 包裝，避免生硬切換
- **數據更新**：使用淡入淡出效果，避免跳變

## 無障礙設計

- **最小點擊區域**：至少 44x44pt
- **文字大小**：主要內容不小於 CGFloat(16).auto()，次要內容不小於 CGFloat(12).auto()
- **顏色對比度**：確保文字與背景對比足夠

## 多語系UI指南

### 文字本地化
- **統一使用**：`"key".localized` 方式獲取本地化字串
- **動態更新**：語言切換時UI即時更新
- **字串管理**：所有字串定義在各語言的 `Localizable.strings`

```swift
// 正確的本地化用法
Text("temperature_unit".localized)
    .font(.system(size: CGFloat(16).auto()))
    .foregroundColor(HexColor.themed(.primaryText))

// 天氣描述本地化
let localizedCondition = WeatherDescriptionLocalizer.shared
    .localizeWeatherDescription(weather.condition)
```

### 語言選擇器
- **設計一致**：遵循其他選擇器的Radio Button設計
- **即時預覽**：選擇語言時即時顯示效果
- **智能排序**：當前系統語言優先顯示

## Pro功能UI設計

### Paywall設計
- **簡潔明瞭**：突出Pro功能價值
- **訂閱選項**：月費/年費清楚標示
- **免費試用**：突出顯示試用期
- **關閉選項**：提供明確的關閉按鈕

### Pro功能標識
- **視覺區分**：Pro功能使用特殊標識
- **優雅降級**：免費用戶看到功能預覽
- **升級引導**：點擊Pro功能時顯示Paywall

## 擴充建議

### 新功能開發
- **主題支援**：所有新UI元件必須支援主題切換
- **多語系**：新增文字內容必須支援13種語言
- **Pro整合**：考慮功能是否為Pro限定
- **響應式設計**：使用AutoInch確保設備適配

### 動畫與互動
- **統一時長**：動畫時長統一為0.3秒
- **Lottie整合**：複雜動畫優先使用Lottie
- **性能考量**：避免過度動畫影響性能
- **無障礙**：支援減少動畫的系統設定

### 測試與品質
- **主題測試**：確保淺色/深色主題都正常顯示
- **語言測試**：測試所有支援語言的UI佈局
- **設備測試**：確保在不同尺寸設備上的顯示效果
- **Pro狀態測試**：測試免費/Pro用戶的不同體驗
