---
type: "always_apply"
---

# MinimalistWeather 資料流與狀態管理

本文件詳細說明 MinimalistWeather 專案中的資料流向、狀態管理與元件通訊方式。

## 核心資料流

### 天氣資料流
1. **用戶選擇位置** → View ([LocationSearchView.swift](mdc:MinimalistWeather/View/LocationSearchView.swift))
2. **選擇位置傳遞** → ViewModel ([WeatherViewModel.swift](mdc:MinimalistWeather/ViewModel/WeatherViewModel.swift))
3. **發起API請求** → Service ([WeatherService.swift](mdc:MinimalistWeather/Services/WeatherService.swift))
4. **API回應解析** → Model ([WeatherData.swift](mdc:MinimalistWeather/Model/WeatherData.swift))
5. **更新狀態** → ViewModel (更新 @Published 屬性)
6. **UI自動更新** → View ([MainPagingView.swift](mdc:MinimalistWeather/View/MainPagingView.swift))

### 每三小時預報資料流
1. **用戶點擊天氣圖標** → View ([WeatherContentView.swift](mdc:MinimalistWeather/View/WeatherContentView.swift))
2. **切換預報視圖** → ViewModel (`toggleHourlyForecast()`)
3. **顯示預報** → HourlyForecastView → HourlyForecastRow

### 位置資料流
1. **用戶儲存位置** → ViewModel (`saveCurrentLocation()`)
2. **持久化儲存** → Repository ([LocationRepository.swift](mdc:MinimalistWeather/Services/LocationRepository.swift))
3. **寫入UserDefaults** → 本地儲存
4. **應用啟動** → 從本地載入儲存的位置
5. **更新UI** → 位置列表更新

## 狀態管理

### 全域狀態
- **網路狀態**：由 [NetworkMonitor.swift](mdc:MinimalistWeather/Utilities/NetworkMonitor.swift) 管理，以 @EnvironmentObject 注入
  ```swift
  @main
  struct MinimalistWeatherApp: App {
      @StateObject private var networkMonitor = NetworkMonitor()
      // ...
      var body: some Scene {
          WindowGroup {
              // ...
              .environmentObject(networkMonitor)
          }
      }
  }
  ```

### 用戶設定
- **溫度單位**：由 [AppSettings.swift](mdc:MinimalistWeather/Model/AppSettings.swift) 管理，以單例模式實現
  ```swift
  class AppSettings {
      static let shared = AppSettings()
      @Published var temperatureUnit: TemperatureUnit = .celsius
      // ...
  }
  ```

### ViewModel 狀態
- **所有 UI 綁定數據**：必須標記為 @Published
- **狀態更新**：透過 Combine 框架的 sink 和 store 方法訂閱

## 元件間通訊

### 父子元件通訊
- **向下傳遞**：通過參數傳遞 (例如 `viewModel: WeatherViewModel`)
- **向上回調**：通過 closure 回調 (例如 `onNextPage: () -> Void`)

### 非直接關聯元件通訊
- **NotificationCenter**：用於跨頁面通知，特別是在頁面切換時
  ```swift
  // 發送通知
  NotificationCenter.default.post(name: Notification.Name("ScrollToPage1"), object: nil)
  
  // 接收通知
  NotificationCenter.default.addObserver(
      self,
      selector: #selector(scrollToPage1),
      name: Notification.Name("ScrollToPage1"),
      object: nil
  )
  ```

### 資料持久化
- **位置資料**：使用 UserDefaults 儲存，由 LocationRepository 封裝
- **設定資料**：使用 UserDefaults 儲存，由 AppSettings 封裝

## 畫面導航流程

### 主畫面分頁實現
- **從ScrollView切換為UIPageViewController**：使用原生UIKit分頁控制器實現垂直分頁導航
  ```swift
  // 垂直分頁視圖包裝器
  struct VerticalPageView: UIViewControllerRepresentable {
      // UIPageViewController設定垂直導航
      func makeUIViewController(context: Context) -> UIPageViewController {
          let pageViewController = UIPageViewController(
              transitionStyle: .scroll,
              navigationOrientation: .vertical) // 垂直方向導航
          // ...
      }
      // ...
  }
  ```

### 頁面切換
- 使用 NotificationCenter 實現頁面間切換通信
- 通過 UIPageViewController 的 setViewControllers 方法實現頁面切換

## 響應式佈局

### AutoInch框架整合
- 所有View文件已整合 AutoInch 框架，提供設備尺寸自適應功能
  ```swift
  import AutoInch
  
  // 使用方式範例（在View中）
  .padding(.horizontal, CGFloat(20).auto()) // 自適應水平邊距
  .font(.system(size: CGFloat(16).auto())) // 自適應字體大小
  ```

## 最佳實踐

1. **單向資料流**：View → ViewModel → Service → Model → ViewModel → View
2. **狀態集中管理**：所有狀態由 ViewModel 或專用狀態管理器管理
3. **避免跨層存取**：View 不應直接存取 Service 或 Model
4. **解耦合**：使用協議（protocol）定義介面，便於測試與替換實現
5. **響應式更新**：使用 @Published 和 Combine 實現 UI 自動更新

## 注意事項

- 新增全域狀態時，優先考慮使用 @EnvironmentObject 注入
- 狀態更新必須在主線程 (DispatchQueue.main) 執行
- 避免在 View 中儲存狀態，除非是 UI 專用的臨時狀態
- 使用 AutoInch 框架時，確保所有尺寸數值都使用 CGFloat(值).auto() 方法
