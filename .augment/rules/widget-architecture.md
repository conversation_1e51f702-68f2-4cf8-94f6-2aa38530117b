# WeatherWidgets Architecture Rules

## 📁 File Organization

### Directory Structure
```
WeatherWidgets/
├── WeatherWidgetsBundle.swift          # Main bundle entry point
├── Shared/                             # Shared components
├── Widgets/                           # Widget implementations
├── Providers/                         # Timeline providers
└── [Language].lproj/                  # Localization resources
```

### File Naming Conventions
- **Widget Files**: `[WidgetName]Widget.swift` (e.g., `CurrentWeatherWidget.swift`)
- **Provider Files**: `[WidgetName]Provider.swift` (e.g., `WeeklyForecastProvider.swift`)
- **Shared Components**: `Widget[ComponentName].swift` (e.g., `WidgetColors.swift`)
- **Localization**: `[language].lproj/Localizable.strings`

## 🏗️ Modular Architecture

### Shared Components (`Shared/`)
1. **WidgetFoundation.swift**
   - Core types: `TemperatureUnit`, `ThemeMode`
   - Language detection: `WidgetLanguageDetector`
   - Localization: `WidgetLocalizer`

2. **WidgetColors.swift**
   - Theme management: `WidgetColors`
   - Color definitions for light/dark themes
   - Dynamic color adaptation

3. **WidgetTypes.swift**
   - Data models: `WeatherData`, `SavedLocation`, `DailyForecast`
   - Timeline entries: `CurrentWeatherEntry`, `WeeklyForecastEntry`
   - API structures: `MiddlewareCurrentResponse`, etc.

4. **WidgetWeatherService.swift**
   - API client: `WidgetWeatherService`
   - Network requests and JSON parsing
   - Error handling and fallback data

5. **WidgetUtils.swift**
   - Utility functions: `getWeatherIcon()`, `parseFlexibleDate()`
   - Helper methods for data processing

### Widget Implementations (`Widgets/`)
- Each widget must have its own file
- Include both view and widget configuration
- Support all required widget sizes
- Implement proper preview support

### Timeline Providers (`Providers/`)
- Separate provider files for complex widgets
- Implement `TimelineProvider` protocol
- Include caching logic and error handling
- Follow naming convention: `[WidgetName]Provider.swift`

## 🔧 Implementation Rules

### Widget Structure
```swift
// Widget View
struct [WidgetName]WidgetEntryView: View {
    var entry: [WidgetName]Entry
    // Implementation
}

// Widget Configuration
struct [WidgetName]Widget: Widget {
    let kind: String = "[WidgetName]Widget"
    var body: some WidgetConfiguration {
        // Configuration
    }
}
```

### Timeline Provider Structure
```swift
struct [WidgetName]Provider: TimelineProvider {
    // Cache management
    private static var lastAPICall: (date: Date, result: Entry, ...)
    
    // Required methods
    func placeholder(in context: Context) -> Entry
    func getSnapshot(in context: Context, completion: @escaping (Entry) -> ())
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ())
    
    // Private helper methods
    private func fetchData(...)
    private func convertToWidgetFormat(...)
}
```

### Data Models
```swift
struct [ModelName]Entry: TimelineEntry {
    let date: Date
    // Additional properties
}
```

## 🌐 Localization Rules

### Language Support
- Must support all 12 languages: en, zh-Hant, ja, fr, de, es, it, nl, da, sv, nb, fi
- Use `WidgetLocalizer.localized(_:)` for all user-facing strings
- Provide fallback to English for missing translations

### String Keys
- Use descriptive, lowercase keys with underscores
- Group related strings (e.g., `today`, `tomorrow`, `monday`, etc.)
- Include context in key names (e.g., `current_weather_desc`)

### Localization Access
```swift
// Correct usage
Text(WidgetLocalizer.localized("today"))

// Incorrect - direct string usage
Text("Today") // ❌ Not localized
```

## 🎨 Theme and Styling Rules

### Color Usage
```swift
// Correct - theme-aware colors
.foregroundColor(WidgetColors.themed(.primaryText))

// Incorrect - hardcoded colors
.foregroundColor(.black) // ❌ Not theme-aware
```

### Supported Themes
- Light theme (default)
- Dark theme
- System theme (follows device setting)

### Font Guidelines
- Use system fonts with design variants: `.rounded`
- Specify weight and size explicitly
- Maintain consistency across widgets

## 📊 Data Management Rules

### App Group Integration
- Use `group.com.minlsm.weather` for shared data
- Always check for UserDefaults initialization success
- Handle missing or invalid data gracefully

### Caching Strategy
- **Current Weather**: 2-minute cache, 10-minute updates
- **Weekly Forecast**: 10-minute cache, 1-hour updates
- Cache invalidation on: location, theme, source, language changes

### Error Handling
- Provide fallback data for all error scenarios
- Log errors with descriptive messages
- Never crash on data parsing failures

## 🚀 Performance Rules

### API Efficiency
- Implement smart caching to prevent unnecessary requests
- Use appropriate cache durations for different data types
- Batch multiple parameter changes before cache invalidation

### Memory Management
- Use lightweight data structures
- Avoid retaining large objects in static variables
- Clean up resources properly

### Update Scheduling
- Set appropriate timeline policies
- Use `.after(date)` for scheduled updates
- Use `.never` only for error states

## 🧪 Testing and Development Rules

### Preview Support
- Every widget must include preview implementations
- Use realistic sample data
- Test multiple languages and themes
- Include all supported widget sizes

### Debug Logging
- Use consistent logging format: `🔍 [Component]: [Message]`
- Include relevant context (location, API calls, cache status)
- Use appropriate log levels (info, warning, error)

### Code Quality
- Follow Swift naming conventions
- Use meaningful variable and function names
- Include inline documentation for complex logic
- Maintain consistent code formatting

## 🔒 Security and Privacy Rules

### Data Handling
- Only store necessary data in App Group
- Use coordinates only, never store personal information
- Implement secure HTTPS connections for API calls
- Respect user privacy preferences

### API Security
- Use secure endpoints only
- Implement proper error handling for network failures
- Don't log sensitive information (API keys, user data)

## 📋 Maintenance Rules

### Version Compatibility
- Target iOS 15+ for widget compatibility
- Avoid using iOS 16+ only APIs
- Test on multiple iOS versions

### Code Updates
- Update all affected widgets when modifying shared components
- Maintain backward compatibility when possible
- Update documentation with any architectural changes

### Localization Maintenance
- Keep all language files synchronized
- Test new features in multiple languages
- Update translations when adding new strings

## 🤝 Contribution Guidelines

### Adding New Widgets
1. Create widget file in `Widgets/` directory
2. Implement timeline provider in `Providers/` (if complex)
3. Add to `WeatherWidgetsBundle.swift`
4. Update all localization files
5. Add comprehensive preview support
6. Update documentation

### Modifying Shared Components
1. Assess impact on all existing widgets
2. Maintain backward compatibility
3. Update dependent components
4. Test all widget types thoroughly
5. Update documentation and rules

### Code Review Checklist
- [ ] Follows modular architecture
- [ ] Implements proper localization
- [ ] Includes theme support
- [ ] Has appropriate caching
- [ ] Includes error handling
- [ ] Has preview support
- [ ] Updates documentation
- [ ] Tests all widget sizes
- [ ] Verifies multiple languages
- [ ] Checks performance impact
