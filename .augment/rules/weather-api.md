---
type: "manual"
---

# 🌤️ 天氣中繼 API 整合指南

> 專為開發者設計的統一天氣 API 串接文件

## 📋 目錄

- [快速開始](#快速開始)
- [API 概覽](#api-概覽)
- [統一資料格式](#統一資料格式)
- [端點詳細說明](#端點詳細說明)
- [提供者特色](#提供者特色)
- [錯誤處理](#錯誤處理)
- [最佳實踐](#最佳實踐)
- [SDK 範例](#sdk-範例)
- [常見問題](#常見問題)

## 🚀 快速開始

### 基本資訊
- **API 基礎 URL**: `https://your-worker-domain.workers.dev`
- **當前版本**: v1
- **協議**: HTTPS
- **格式**: JSON
- **快取**: 10 分鐘
- **認證**: 無需 API 金鑰

### 第一個請求
```bash
curl "https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654"
```

### 快速測試
```javascript
// 現代 JavaScript
const weather = await fetch('https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654')
  .then(res => res.json());

console.log(weather.data.current[0].temperature.current); // 溫度
```

## 🌐 API 概覽

### 支援的天氣提供者

| 代碼 | 提供者 | 覆蓋範圍 | 特色 | 建議場景 |
|------|--------|----------|------|----------|
| `OW` | OpenWeather | 全球 | 資料豐富、穩定 | 國際應用、開發測試 |
| `AW` | Apple WeatherKit | 全球 | 高精度、Apple 生態 | iOS 應用、精確預報 |
| `CW` | 中央氣象署 | 台灣 | 官方資料、在地化 | 台灣地區專用 |
| `GW` | Google Weather | 全球 | 多語言、整合性佳 | 多語言應用、Android |

### API 端點

| 端點 | 功能 | 資料範圍 | 快取時間 |
|------|------|----------|----------|
| `/api/v1/current` | 即時天氣 | 當前時刻 | 10 分鐘 |
| `/api/v1/hourly` | 每小時預報 | 24-120 小時 | 10 分鐘 |
| `/api/v1/daily` | 每日預報 | 5-7 天 | 10 分鐘 |
| `/api/v1/weekly` | 每週預報 | 5 天 | 10 分鐘 |

## � 統一資料格式

### 設計理念
所有提供者的資料都經過轉換，提供一致的 JSON 結構，讓開發者無需處理不同 API 的差異。

### 標準回應結構
```json
{
  "success": true,
  "cache_status": "HIT",
  "data": {
    "current": [...],     // 即時天氣陣列
    "hourly": [...],      // 每小時預報陣列
    "daily": [...],       // 每日預報陣列
    "metadata": {
      "provider": "OpenWeather",
      "api_type": "current",
      "response_code": 200
    }
  }
}
```

### 天氣資料物件
```json
{
  "timestamp": "2024-01-15T08:00:00.000Z",
  "weather": {
    "condition": "多雲",
    "icon": "04d"
  },
  "temperature": {
    "current": 18.5,
    "feels_like": 17.8,
    "min": 16.2,
    "max": 21.3
  },
  "atmosphere": {
    "humidity": 65,
    "sea_level": 1013,
    "ground_level": 1011,
    "visibility": 10.0
  },
  "wind": {
    "speed": 3.2,
    "direction": 230,
    "gust": 5.1
  },
  "precipitation": {
    "rain_amount": 0.0,
    "snow_amount": 0.0,
    "probability": 15
  },
  "clouds": {
    "coverage": 75
  },
  "uv": {
    "index": 3
  },
  "sunevents": {
    "sunrise": "2024-01-15T22:42:00.000Z",
    "sunset": "2024-01-15T09:18:00.000Z"
  }
}
```

### 單位標準化
- **時間**: UTC ISO8601 格式
- **溫度**: 攝氏度 (°C)
- **風速**: 公尺/秒 (m/s)
- **降水**: 毫米 (mm)
- **氣壓**: 百帕 (hPa)
- **能見度**: 公里 (km)
- **濕度**: 百分比 (%)

### 特殊值處理
- **`-99`**: 資料無法取得
- **`-999`**: 紫外線指數無法取得

## 📡 端點詳細說明

### 1. 即時天氣 `/api/v1/current`

#### 參數
| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 提供者代碼 | `OW`, `AW`, `CW`, `GW` |
| `lat` | number | ✅ | 緯度 (-90 到 90) | `25.0330` |
| `lon` | number | ✅ | 經度 (-180 到 180) | `121.5654` |
| `lang` | string | ❌ | 語言代碼 | `zh`, `en`, `zh-TW` |

#### 範例
```bash
# 台北即時天氣 (OpenWeather)
curl "https://your-domain.workers.dev/api/v1/current?data=OW&lat=25.0330&lon=121.5654"

# 東京即時天氣 (Google Weather, 英文)
curl "https://your-domain.workers.dev/api/v1/current?data=GW&lat=35.6762&lon=139.6503&lang=en"
```

### 2. 每小時預報 `/api/v1/hourly`

#### 參數
| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 提供者代碼 | `OW`, `AW`, `CW`, `GW` |
| `lat` | number | ✅ | 緯度 | `25.0330` |
| `lon` | number | ✅ | 經度 | `121.5654` |
| `plan` | string | ✅ | 資料計劃 | `limit`, `full` |
| `lang` | string | ❌ | 語言代碼 | `zh`, `en` |

#### 資料計劃
| 計劃 | OpenWeather | Apple Weather | 中央氣象署 | Google Weather |
|------|-------------|---------------|-------------|----------------|
| `limit` | 24 小時 | 24 小時 | 24 小時 | 24 小時 |
| `full` | 120 小時 | 120 小時 | 72 小時 | 120 小時 |

#### 範例
```bash
# 台北 24 小時預報
curl "https://your-domain.workers.dev/api/v1/hourly?data=CW&lat=25.0330&lon=121.5654&plan=limit"

# 紐約 5 天預報
curl "https://your-domain.workers.dev/api/v1/hourly?data=OW&lat=40.7128&lon=-74.0060&plan=full"
```

### 3. 每日預報 `/api/v1/daily`

#### 參數
| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 提供者代碼 | `OW`, `AW`, `CW`, `GW` |
| `lat` | number | ✅ | 緯度 | `25.0330` |
| `lon` | number | ✅ | 經度 | `121.5654` |
| `lang` | string | ❌ | 語言代碼 | `zh`, `en` |

#### 範例
```bash
# 台北一週預報
curl "https://your-domain.workers.dev/api/v1/daily?data=CW&lat=25.0330&lon=121.5654"

# 倫敦一週預報 (英文)
curl "https://your-domain.workers.dev/api/v1/daily?data=GW&lat=51.5074&lon=-0.1278&lang=en"
```

### 4. 每週預報 `/api/v1/weekly`

#### 參數
| 參數 | 類型 | 必要 | 說明 | 範例 |
|------|------|------|------|------|
| `data` | string | ✅ | 提供者代碼 | `OW`, `AW`, `GW`, `CW` (支援 OpenWeather、Apple Weather、Google Weather 和中央氣象署) |
| `lat` | number | ✅ | 緯度 | `25.0330` |
| `lon` | number | ✅ | 經度 | `121.5654` |
| `lang` | string | ❌ | 語言代碼 | `zh`, `en` |

#### 範例
```bash
# 台北五日預報
curl "https://your-domain.workers.dev/api/v1/weekly?data=OW&lat=25.0330&lon=121.5654"

# 東京五日預報 (英文)
curl "https://your-domain.workers.dev/api/v1/weekly?data=OW&lat=35.6762&lon=139.6503&lang=en"

# Apple Weather 五日預報
curl "https://your-domain.workers.dev/api/v1/weekly?data=AW&lat=25.0330&lon=121.5654&lang=zh"

# Google Weather 五日預報
curl "https://your-domain.workers.dev/api/v1/weekly?data=GW&lat=25.0330&lon=121.5654&lang=zh"

# 中央氣象署 五日預報
curl "https://your-domain.workers.dev/api/v1/weekly?data=CW&lat=25.0330&lon=121.5654&lang=zh"
```

## 🔧 提供者特色

### OpenWeather (OW)
- **優勢**: 全球覆蓋、資料穩定、開發友善
- **適用**: 國際應用、原型開發
- **特色**: 完整的氣象資料、歷史資料支援

### Apple WeatherKit (AW)
- **優勢**: 高精度、Apple 生態整合
- **適用**: iOS 應用、需要高精度的場景
- **特色**: 機器學習預測、分鐘級降雨預報

### 中央氣象署 (CW)
- **優勢**: 台灣官方資料、在地化程度高
- **適用**: 台灣地區專用應用
- **特色**: 政府官方資料、災害預警整合

### Google Weather (GW)
- **優勢**: 多語言支援、整合性佳
- **適用**: 多語言應用、Android 生態
- **特色**: 智慧預測、語言本地化

## ⚠️ 錯誤處理

### 錯誤回應格式
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Missing required \"lat\" and \"lon\" parameters"
  }
}
```

### 常見錯誤
| 代碼 | 原因 | 解決方法 |
|------|------|----------|
| `400` | 參數錯誤 | 檢查必要參數 |
| `404` | 路徑錯誤 | 確認端點路徑 |
| `500` | 伺服器錯誤 | 重試或聯絡支援 |

### 錯誤處理範例
```javascript
async function getWeather(lat, lon, provider = 'OW') {
  try {
    const response = await fetch(
      `https://your-domain.workers.dev/api/v1/current?data=${provider}&lat=${lat}&lon=${lon}`
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(`API Error: ${data.error.message}`);
    }
    
    return data.data;
  } catch (error) {
    console.error('Weather fetch failed:', error);
    throw error;
  }
}
```

## 🎯 最佳實踐

## 📱 SDK 範例


### iOS (Swift)
```swift
import Foundation

class WeatherService {
    private let baseURL = "https://your-domain.workers.dev/api/v1"
    
    func getCurrentWeather(
        lat: Double,
        lon: Double,
        provider: String = "AW"
    ) async throws -> WeatherData {
        let url = URL(string: "\(baseURL)/current?data=\(provider)&lat=\(lat)&lon=\(lon)")!
        
        let (data, _) = try await URLSession.shared.data(from: url)
        let response = try JSONDecoder().decode(WeatherResponse.self, from: data)
        
        guard response.success, let weatherData = response.data else {
            throw WeatherError.apiError(response.error?.message ?? "Unknown error")
        }
        
        return weatherData
    }
}

struct WeatherResponse: Codable {
    let success: Bool
    let data: WeatherData?
    let error: APIError?
}

struct WeatherData: Codable {
    let current: [WeatherItem]
    let metadata: Metadata
}

struct WeatherItem: Codable {
    let timestamp: String
    let weather: Weather
    let temperature: Temperature
    let atmosphere: Atmosphere
    // ... 其他欄位
}
```
