---
type: "manual"
---

# WeatherWidgets Development Rules

## 🎯 Target Platform
- **iOS Version**: iOS 15+ (avoid iOS 16+ only APIs)
- **Widget Framework**: WidgetKit
- **UI Framework**: SwiftUI
- **Language**: Swift 5.5+

## 📱 Widget Specifications

### Current Weather Widget
- **Sizes**: Small (2x2) only
- **Content**: Location name, weather icon, temperature, condition
- **Update**: Every 10 minutes
- **Cache**: 2 minutes
- **Provider**: Embedded in widget file

### Weekly Forecast Widget
- **Sizes**: Small (2x2), Medium (4x2)
- **Content**: 5-day forecast with varying detail levels
- **Update**: Every 1 hour
- **Cache**: 10 minutes
- **Provider**: Separate `WeeklyForecastProvider.swift`

### Horizontal Weekly Forecast Widget
- **Sizes**: Medium (4x2) only
- **Content**: 5-day horizontal layout
- **Update**: Every 1 hour
- **Cache**: 10 minutes
- **Provider**: Separate `HorizontalWeeklyForecastProvider.swift`

## 🏗️ Code Architecture

### Dependency Hierarchy
```
WeatherWidgetsBundle
    ↓
Individual Widgets
    ↓
Timeline Providers (if separate)
    ↓
Shared Components
    ↓
Foundation Types
```

### Import Requirements
```swift
// Required imports for widgets
import WidgetKit
import SwiftUI

// Additional imports for shared components
import Foundation  // For data models and utilities
```

### File Dependencies
- **Widgets** depend on: Shared components, Foundation types
- **Providers** depend on: Shared components, API service, Foundation types
- **Shared components** depend on: Foundation types only
- **Foundation types** depend on: System frameworks only

## 🔧 Implementation Patterns

### Widget View Pattern
```swift
struct [Name]WidgetEntryView: View {
    var entry: [Name]Entry
    @Environment(\.widgetFamily) var family  // For multi-size widgets
    
    var body: some View {
        ZStack {
            // Widget content based on family size
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}
```

### Timeline Provider Pattern
```swift
struct [Name]Provider: TimelineProvider {
    // Static cache for performance
    private static var lastAPICall: (date: Date, result: Entry, locationId: String, theme: String, weatherSource: String, temperatureUnit: String, language: String)?
    
    func placeholder(in context: Context) -> Entry { /* */ }
    func getSnapshot(in context: Context, completion: @escaping (Entry) -> ()) { /* */ }
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) { /* */ }
}
```

### Widget Configuration Pattern
```swift
struct [Name]Widget: Widget {
    let kind: String = "[Name]Widget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: [Name]Provider()) { entry in
            if #available(iOS 17.0, *) {
                [Name]WidgetEntryView(entry: entry)
                    .containerBackground(WidgetColors.themed(.primaryBackground), for: .widget)
            } else {
                [Name]WidgetEntryView(entry: entry)
            }
        }
        .configurationDisplayName(WidgetLocalizer.localized("[name]"))
        .description(WidgetLocalizer.localized("[name]_desc"))
        .supportedFamilies([.systemSmall, .systemMedium])  // Adjust as needed
    }
}
```

## 🌐 Localization Implementation

### String Access Pattern
```swift
// Always use WidgetLocalizer
Text(WidgetLocalizer.localized("key_name"))

// Never use direct strings
Text("Direct String")  // ❌ Wrong
```

### Language Detection
```swift
// Get current language code
let languageCode = WidgetLanguageDetector.getCurrentLanguageCode()

// Get current locale identifier
let localeIdentifier = WidgetLanguageDetector.getCurrentLocaleIdentifier()
```

### Localization Keys
- **Day names**: `today`, `tomorrow`, `monday`, `tuesday`, etc.
- **Widget states**: `loading`, `select_location`, `config_error`, etc.
- **Widget titles**: `current_weather`, `weekly_forecast`, `5_day_forecast`
- **Descriptions**: `current_weather_desc`, `weekly_forecast_desc`

## 🎨 Theme Implementation

### Color Usage
```swift
// Theme-aware colors
.foregroundColor(WidgetColors.themed(.primaryText))
.foregroundColor(WidgetColors.themed(.secondaryText))

// Background colors (iOS 17+)
.containerBackground(WidgetColors.themed(.primaryBackground), for: .widget)
```

### Theme Types
```swift
enum ThemeMode: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
}
```

## 📊 Data Management

### App Group Access
```swift
// Always check for successful initialization
guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
    // Handle error gracefully
    return
}
```

### Data Keys
- `widget_selected_location_name`: String
- `widget_selected_location_id`: String
- `widget_selected_location_lat`: Double
- `widget_selected_location_lon`: Double
- `widget_selected_location_country`: String
- `widget_selected_location_weather_source`: String
- `themeMode`: String
- `temperatureUnit`: String

### Cache Management
```swift
// Cache structure for timeline providers
private static var lastAPICall: (
    date: Date,
    result: EntryType,
    locationId: String,
    theme: String,
    weatherSource: String,
    temperatureUnit: String,
    language: String
)?

// Cache validation
if let lastCall = Provider.lastAPICall,
   now.timeIntervalSince(lastCall.date) < cacheTimeout,
   lastCall.locationId == currentLocationId,
   lastCall.theme == currentTheme,
   lastCall.weatherSource == currentWeatherSource,
   lastCall.temperatureUnit == currentTemperatureUnit,
   lastCall.language == currentLanguage {
    // Use cached data
}
```

## 🌡️ Temperature Handling

### Unit Conversion
```swift
// Use CurrentWeatherProvider.formatTemperature for consistency
let formattedTemp = CurrentWeatherProvider.formatTemperature(Double(celsius), unit: temperatureUnit)
```

### Temperature Units
```swift
enum TemperatureUnit: String, CaseIterable {
    case celsius = "celsius"
    case fahrenheit = "fahrenheit"
    
    var symbol: String {
        switch self {
        case .celsius: return "°C"
        case .fahrenheit: return "°F"
        }
    }
}
```

## 🌤️ Weather API Integration

### Weather Sources
```swift
enum WeatherSource: String, Codable, CaseIterable {
    case AW = "AW"      // Apple Weather
    case OW = "OW"      // OpenWeather
    case GW = "GW"      // Google Weather
    case CW = "CW"      // Central Weather (Taiwan)
}
```

### API Service Usage
```swift
let weatherService = WidgetWeatherService()

// Current weather
weatherService.getWeatherForCoordinates(lat, lon: lon, locationName: name, weatherSource: source) { result in
    // Handle result
}

// Weekly forecast
weatherService.getWeeklyForecastForCoordinates(lat, lon: lon, locationName: name, weatherSource: source) { result in
    // Handle result
}
```

## 🚀 Performance Guidelines

### Timeline Policies
```swift
// Current weather: 10-minute updates
let nextUpdate = Calendar.current.date(byAdding: .minute, value: 10, to: Date())!
let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

// Weekly forecast: 1-hour updates
let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date())!
let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

// Error states: Never update automatically
let timeline = Timeline(entries: [entry], policy: .never)
```

### Memory Management
- Use lightweight data structures
- Avoid retaining large objects in static variables
- Clean up network requests properly

### API Efficiency
- Implement smart caching
- Avoid duplicate requests
- Use appropriate timeout values

## 🧪 Testing and Debugging

### Preview Implementation
```swift
struct [Widget]_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Test different sizes
            [Widget]EntryView(entry: sampleEntry)
                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("Small")
            
            [Widget]EntryView(entry: sampleEntry)
                .previewContext(WidgetPreviewContext(family: .systemMedium))
                .previewDisplayName("Medium")
        }
    }
}
```

### Debug Logging
```swift
// Use consistent logging format
print("🔍 [ComponentName]: [Message]")
print("✅ [ComponentName]: [Success message]")
print("❌ [ComponentName]: [Error message]")
print("🔄 [ComponentName]: [Cache/Update message]")
```

### Error Handling
```swift
// Always provide fallback data
switch result {
case .success(let data):
    // Use real data
case .failure(let error):
    print("❌ Error: \(error)")
    // Provide fallback data, never crash
}
```

## 🔒 Security Guidelines

### Data Privacy
- Only store necessary location coordinates
- Never store personal information
- Use secure HTTPS connections
- Respect user privacy settings

### Error Information
- Don't log sensitive data (API keys, personal info)
- Provide user-friendly error messages
- Handle network failures gracefully

## 📋 Quality Assurance

### Code Review Checklist
- [ ] Follows modular architecture
- [ ] Uses proper localization
- [ ] Implements theme support
- [ ] Has appropriate caching
- [ ] Includes error handling
- [ ] Supports all required widget sizes
- [ ] Has preview implementations
- [ ] Uses consistent logging
- [ ] Handles edge cases
- [ ] Maintains performance standards

### Testing Requirements
- Test all widget sizes
- Test multiple languages
- Test theme switching
- Test network failures
- Test cache invalidation
- Test temperature unit conversion
- Test different weather sources
