# 自定義 What's New 介面

## 概述

基於 `OnboardingCompleteView` 的設計風格，創建了一個完全自定義的 What's New 介面，完美整合應用的設計語言和主題系統。

## 設計特色

### 🎨 視覺設計
- **佈局風格**：採用與 `OnboardingCompleteView` 相同的佈局結構
- **主題整合**：完美支援淺色/深色主題切換
- **字體系統**：使用應用統一的字體規範
- **圖標系統**：使用 `AppIconsSymbol` 和自定義字體圖標

### 📱 用戶體驗
- **載入狀態**：優雅的載入動畫
- **版本導航**：支援多版本間的前後切換
- **頁面指示器**：清楚顯示當前頁面位置
- **響應式設計**：使用 AutoInch 框架確保設備適配

## 檔案結構

### 主要元件

1. **CustomWhatsNewView** - 主要容器視圖
   - 管理資料載入和狀態
   - 處理版本切換邏輯
   - 整合主題和多語系

2. **WhatsNewFeatureRow** - 功能展示行
   - 圖標 + 標題 + 副標題 + 描述
   - 自適應佈局
   - 主題化顏色

3. **WhatsNewBottomButtons** - 底部按鈕區域
   - 關閉按鈕
   - 頁面指示器
   - 導航按鈕（前一個/下一個版本）

### 資料模型

```swift
struct WhatsNewVersion: Codable {
    let version: String
    let new: [WhatsNewFeature]
}

struct WhatsNewFeature: Codable {
    let icon: String        // SF Symbol 名稱
    let title: String       // 功能標題
    let subtitle: String    // 功能副標題
    let body: String        // 詳細描述
}
```

## 資料來源

### 載入優先順序
1. **遠端 JSON API**（優先）
   - URL：`https://weather.minlsm.com/release/{langcode}/release.json`
   - 10秒超時機制
   - 網路狀態檢查

2. **本地 JSON 檔案**（後備）
   - 語言特定檔案：`whats_new_{langcode}.json`
   - 預設檔案：`whats_new_data.json`

3. **內建預設資料**（最後回退）
   - 確保在任何情況下都能正常顯示
   - 硬編碼的基本版本資訊

## 圖標映射

| SF Symbol | AppIcon | 用途 |
|-----------|---------|------|
| sparkles | appicon | 新功能 |
| globe | language | 多語系 |
| paintbrush | themeday | 主題 |
| cloud.fill | cloud | 天氣相關 |
| location.fill | search | 位置相關 |

## 多語系支援

### 新增字串
- `version` - 版本
- `no_updates_available` - 目前沒有可用的更新
- `close` - 關閉

### 支援語言
- 繁體中文 (zh-Hant)
- 英文 (en)
- 日文 (ja)
- 其他語言可輕鬆擴展

## 整合位置

### 自動顯示
- `MainPagingView.swift` - 應用啟動時自動檢查並顯示

### 手動觸發
- `SettingsPageView.swift` - 設定頁面的 "最新功能" 選項

## 使用方式

### 基本使用
```swift
CustomWhatsNewView(showWhatsNew: $showWhatsNew)
    .environmentObject(languageService)
```

### 資料載入流程
1. 檢查網路連線狀態
2. 有網路：嘗試載入遠端 JSON（10秒超時）
3. 遠端失敗或無網路：載入語言特定的本地檔案
4. 本地檔案不存在：回退到預設本地檔案
5. 最後使用內建預設資料

### 版本導航
- 支援多版本間的前後切換
- 頁面指示器顯示當前位置
- 平滑的切換動畫

## 自定義選項

### 修改預設資料
在 `loadDefaultData()` 方法中修改內建的版本資料。

### 添加新圖標
1. 在 `AppIcons.swift` 中添加新圖標
2. 在 `getAppIcon(for:)` 方法中添加映射

### 調整佈局
- 修改 `WhatsNewFeatureRow` 的佈局
- 調整間距和字體大小
- 自定義顏色和主題

## 優勢

1. **完全自主控制**：不依賴第三方套件
2. **設計一致性**：與應用整體設計完美融合
3. **效能優化**：輕量級實現，載入快速
4. **易於維護**：清晰的程式碼結構和註釋
5. **擴展性強**：可輕鬆添加新功能和自定義
6. **智能回退**：遠端 → 本地 → 預設的多層回退機制
7. **網路感知**：根據網路狀態智能選擇載入策略

## 與 SwiftNEW 的比較

| 特性 | CustomWhatsNewView | SwiftNEW |
|------|-------------------|----------|
| 設計一致性 | ✅ 完美整合 | ⚠️ 需要調整 |
| 自定義程度 | ✅ 完全控制 | ❌ 有限制 |
| 檔案大小 | ✅ 輕量級 | ❌ 較大 |
| 學習成本 | ✅ 低 | ⚠️ 中等 |
| 維護成本 | ✅ 低 | ⚠️ 依賴更新 |

這個自定義實現提供了與 `OnboardingCompleteView` 完全一致的設計風格，同時保持了高度的靈活性和可維護性。
