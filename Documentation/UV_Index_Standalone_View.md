# UV 指數獨立視圖實現

## 概述
將 UV 指數從 WeatherMetricView 網格中獨立出來，創建專用的 UVIndexView，並添加基於 UV 指數等級的顏色變化。

## 實現內容

### 1. 顏色系統更新

#### HexColor.swift
添加了四個新的 UV 指數顏色主題：

```swift
// 新增的顏色主題
case uvOne    // UV 0-2: #98DDCA (淺綠色)
case uvTwo    // UV 3-5: #D5ECC2 (黃綠色)  
case uvThree  // UV 6-7: #FFD3B4 (橙色)
case uvFour   // UV 8-11: #FFAAA7 (紅色)
```

### 2. 獨立 UV 指數視圖

#### UVIndexView
創建了全新的獨立視圖組件：

**特點：**
- 獨立於 WeatherMetricView 網格
- 包含圖標、分隔線和數值的水平佈局
- 支援 Pro 功能鎖定
- 數值變化動畫效果
- 響應式設計（使用 AutoInch）

**佈局結構：**
```
[標題: "紫外線指數"]
[圖標] - [彩色分隔線] - [數值]
```

**分隔線規格：**
- 長度：88.auto()
- 高度：2.auto()
- 顏色：根據 UV 指數動態變化

### 3. UV 指數顏色邏輯

#### 顏色分級系統
```swift
switch uvIndex {
case 0...2:  return HexColor.themed(.uvOne)   // 低風險
case 3...5:  return HexColor.themed(.uvTwo)   // 中等風險
case 6...7:  return HexColor.themed(.uvThree) // 高風險
case 8...11: return HexColor.themed(.uvFour)  // 極高風險
}
```

### 4. 佈局調整

#### WeatherDetailView.swift
- 從天氣指標網格中移除 UV 指數項目
- 將 UVIndexView 放置在第 226 行位置（網格和日出日落信息之間）
- 更新網格列數計算邏輯（移除 UV 指數的計算）
- 添加 `getUVIndexColor()` 方法來獲取對應顏色

### 5. 功能保持

**保留的功能：**
- ✅ -999 值隱藏邏輯
- ✅ 時間軸切換支援
- ✅ Pro 功能鎖定
- ✅ 刷新觸發器 (refreshTrigger)
- ✅ 多語系支援
- ✅ 響應式佈局

## 視覺效果

### UV 指數等級與顏色對應

| UV 指數 | 等級 | 顏色 | Hex 代碼 |
|---------|------|------|----------|
| 0-2 | 低 | 淺綠色 | #98DDCA |
| 3-5 | 中等 | 黃綠色 | #D5ECC2 |
| 6-7 | 高 | 橙色 | #FFD3B4 |
| 8-11 | 極高 | 紅色 | #FFAAA7 |

### 動畫效果
- 數值出現時有 0.8 秒的緩入緩出動畫
- 數值變化時有 0.3 秒的過渡動畫
- 分隔線顏色會即時更新

## 程式碼結構

### 主要方法
```swift
// 獲取 UV 指數顏色
private func getUVIndexColor() -> Color

// UV 指數視圖組件
struct UVIndexView: View
```

### 佈局位置
```
天氣指標網格
    ↓
UV 指數視圖 (新增)
    ↓
日出日落信息
    ↓
詳細指標網格
```

## 測試要點

1. **顏色變化測試**
   - 測試不同 UV 指數值的顏色顯示
   - 確認顏色分級邏輯正確

2. **隱藏邏輯測試**
   - 測試 -999 值時視圖是否隱藏
   - 確認網格佈局不受影響

3. **動畫測試**
   - 測試數值變化動畫
   - 測試時間軸切換時的更新

4. **響應式測試**
   - 測試不同設備尺寸的顯示效果
   - 確認 AutoInch 自適應正常

5. **Pro 功能測試**
   - 測試免費用戶的鎖定狀態
   - 測試付費用戶的正常顯示
