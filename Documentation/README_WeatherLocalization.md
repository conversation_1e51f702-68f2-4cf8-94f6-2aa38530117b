# 天氣描述多語系功能

## 概述

本功能實現了將 OpenWeatherMap API 回傳的英文天氣描述自動轉換為對應的多語系文字，支援繁體中文、英文和日文。

## 實現方式

### 1. 核心組件

- **WeatherDescriptionLocalizer**: 天氣描述多語系管理器
  - 使用單例模式
  - 負責將英文天氣描述轉換為本地化文字
  - 支援文字匹配和模糊匹配

### 2. 支援的天氣描述

涵蓋 OpenWeatherMap API 的所有天氣類型：

#### 雷暴類 (2xx)
- thunderstorm with light rain
- thunderstorm with rain
- thunderstorm with heavy rain
- light thunderstorm
- thunderstorm
- heavy thunderstorm
- ragged thunderstorm
- thunderstorm with light drizzle
- thunderstorm with drizzle
- thunderstorm with heavy drizzle

#### 毛毛雨類 (3xx)
- light intensity drizzle
- drizzle
- heavy intensity drizzle
- light intensity drizzle rain
- drizzle rain
- heavy intensity drizzle rain
- shower rain and drizzle
- heavy shower rain and drizzle
- shower drizzle

#### 雨類 (5xx)
- light rain
- moderate rain
- heavy intensity rain
- very heavy rain
- extreme rain
- freezing rain
- light intensity shower rain
- shower rain
- heavy intensity shower rain
- ragged shower rain

#### 雪類 (6xx)
- light snow
- snow
- heavy snow
- sleet
- light shower sleet
- shower sleet
- light rain and snow
- rain and snow
- light shower snow
- shower snow
- heavy shower snow

#### 大氣現象類 (7xx)
- mist
- smoke
- haze
- sand/dust whirls
- fog
- sand
- dust
- volcanic ash
- squalls
- tornado

#### 晴天類 (800)
- clear sky

#### 雲類 (80x)
- few clouds
- scattered clouds
- broken clouds
- overcast clouds

### 3. 多語系檔案

翻譯內容已添加到以下檔案：
- `MinimalistWeather/Resources/zh-Hant.lproj/Localizable.strings` (繁體中文)
- `MinimalistWeather/Resources/en.lproj/Localizable.strings` (英文)
- `MinimalistWeather/Resources/ja.lproj/Localizable.strings` (日文)

### 4. 整合位置

多語系轉換已整合到以下位置：
- `WeatherViewModel.updateWeatherDisplay()` - 主要天氣描述
- `WeatherViewModel.updateForecastsData()` - 預報天氣描述
- `WeatherDetailView.getSelectedWeatherCondition()` - 詳細頁面天氣描述
- `AppIconsSymbol.getWeatherConditionFromCode()` - 圖標對應天氣描述

## 使用方式

### 基本使用

```swift
let localizer = WeatherDescriptionLocalizer.shared
let localizedDescription = localizer.localizeWeatherDescription("light rain")
// 繁體中文: "小雨"
// 英文: "Light Rain"
// 日文: "小雨"
```

### 自動整合

系統會自動在以下情況應用多語系轉換：
1. 從 API 獲取天氣數據時
2. 顯示當前天氣描述時
3. 顯示預報天氣描述時
4. 在詳細頁面切換時間點時

## 錯誤處理

- 如果找不到對應的翻譯，會返回原始英文描述
- 會在控制台輸出警告訊息，便於調試
- 未知天氣狀況會顯示為 "未知天氣" / "Unknown Weather" / "不明な天気"

## 擴展支援

### 添加新語言

1. 在對應的 `.lproj` 目錄中添加翻譯
2. 使用相同的 key 格式 (如 `weather_light_rain`)
3. 無需修改程式碼

### 添加新天氣描述

1. 在 `WeatherDescriptionLocalizer.getLocalizationKey()` 中添加新的 case
2. 在所有多語系檔案中添加對應翻譯
3. 使用 `weather_` 前綴的 key 格式

## 測試

可以通過修改系統語言設定來測試多語系功能：
1. 設定 -> 一般 -> 語言與地區
2. 切換語言後重新啟動 App
3. 觀察天氣描述是否正確顯示對應語言 