# 天氣來源選擇功能實作摘要

## 功能概述
實作了天氣來源選擇功能，允許用戶為每個儲存的地區選擇不同的天氣資料來源。

## 已實作的組件

### 1. WeatherSource 枚舉 (AppSettings.swift)
- 定義了四個天氣來源：AW (Apple Weather)、OW (OpenWeather)、GW (Google Weather)、CW (Central Weather)
- 包含區域限制邏輯：
  - AW、OW：支援全球
  - CW：只支援台灣 (TW)
  - GW：不支援日本 (JP) 和韓國 (KR)
- 預設值邏輯：
  - TW → CW
  - JP/KR → AW
  - 其他國家 → GW

### 2. SavedLocation 模型擴展 (SavedLocation.swift)
- 新增 `weatherSource: WeatherSource?` 屬性
- 新增 `effectiveWeatherSource` 計算屬性
- 支援向後相容的資料遷移
- 自動為舊資料設定預設天氣來源

### 3. WeatherService 修改 (WeatherService.swift)
- 所有 API 方法新增 `weatherSource` 參數
- 動態構建 API URL 使用指定的天氣來源
- 保持向後相容性（未指定來源時使用預設值）

### 4. WeatherViewModel 更新 (WeatherViewModel.swift)
- `useLocation` 方法整合天氣來源
- `fetchWeatherForCoordinates` 支援天氣來源參數
- 記錄天氣來源選擇到日誌

### 5. LocationRepository 擴展 (LocationRepository.swift)
- 新增 `updateLocationWeatherSource` 方法
- 支援更新已儲存位置的天氣來源
- 保持資料一致性

### 6. WeatherSourcePickerView (WeatherSourcePickerView.swift)
- 參考 TemperatureUnitPickerView 的設計
- 動態顯示該國家可用的天氣來源
- 使用 Radio Button 介面
- 支援確認和取消操作

### 7. LocationSearchView 整合 (LocationSearchView.swift)
- 在儲存位置列表中顯示當前天氣來源
- 新增天氣來源選擇按鈕（使用 AppIcons.next）
- 實作 Sheet 顯示 WeatherSourcePickerView
- 處理天氣來源更新邏輯

### 8. 本地化支援
- 新增 "weather_source_setting" = "天氣來源" 到 zh-Hant.lproj/Localizable.strings
- 各天氣來源的顯示名稱已本地化

## 功能流程

1. **查看天氣來源**：在位置搜尋頁面，每個儲存的位置下方顯示當前使用的天氣來源
2. **選擇天氣來源**：點擊位置旁的箭頭按鈕開啟天氣來源選擇器
3. **過濾可用來源**：選擇器根據位置的國家代碼自動過濾可用的天氣來源
4. **更新設定**：選擇新來源後自動更新儲存的位置設定
5. **重新載入天氣**：如果更新的是當前位置，自動使用新來源重新載入天氣資料

## 區域限制實作

```swift
// 範例：台灣位置可選 AW、OW、CW、GW
// 日本位置只能選 AW、OW（不支援 GW、CW）
// 美國位置可選 AW、OW、GW（不支援 CW）
```

## 資料遷移

- 舊版本的 SavedLocation 資料會自動遷移
- 未設定天氣來源的位置會根據國家代碼自動設定預設來源
- 保持向後相容性，不會破壞現有資料

## 測試建議

1. 新增不同國家的位置，驗證預設天氣來源是否正確
2. 測試天氣來源選擇器是否正確過濾可用選項
3. 驗證選擇不同天氣來源後是否正確載入資料
4. 檢查舊資料是否正確遷移並設定預設來源

## 技術債務

- WeatherService 中的 `defaultDataSource` 常數可能需要根據位置動態調整
- 可考慮新增天氣來源的品質指標或用戶評分
- 未來可新增更多天氣來源選項 