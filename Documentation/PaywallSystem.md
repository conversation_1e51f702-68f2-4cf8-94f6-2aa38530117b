# MinimalistWeather Paywall 系統

## 概述

本專案實作了一個智能的 paywall 顯示系統，根據用戶的訂閱狀態、上次顯示時間和其他條件來決定是否顯示 paywall。

## 核心組件

### 1. PaywallManager (`Services/PaywallManager.swift`)

負責管理 paywall 顯示邏輯的核心類別。

**主要功能：**
- 檢查訂閱狀態
- 管理上次顯示時間
- 決定是否顯示 paywall
- 支援 debug 模式

**關鍵方法：**
- `shouldShowPaywallOnAppLaunch()`: 檢查是否應該顯示 paywall
- `recordPaywallShown()`: 記錄 paywall 已顯示
- `checkAndShowPaywallIfNeeded()`: 檢查並顯示 paywall（如果需要）

### 2. AppSettings 擴展 (`Model/AppSettings.swift`)

新增了 `paywallDebugMode` 屬性，讓開發者可以強制顯示 paywall。

### 3. App 啟動整合 (`MinimalistWeatherApp.swift`)

在 `initializeAppState()` 方法中整合了 paywall 檢查邏輯。

## 顯示邏輯

### 基本規則

1. **已訂閱用戶**：不顯示 paywall
2. **Debug 模式**：強制顯示 paywall
3. **首次安裝**：不顯示 paywall，但記錄當前時間
4. **同一天內**：不重複顯示 paywall
5. **距離上次顯示 ≥ 1 天**：顯示 paywall

### 流程圖

```
開始
  ↓
Debug 模式？ → 是 → 顯示 paywall
  ↓ 否
已訂閱？ → 是 → 不顯示 paywall
  ↓ 否
有顯示記錄？ → 否 → 記錄時間 → 不顯示 paywall
  ↓ 是
是今天？ → 是 → 不顯示 paywall
  ↓ 否
距離 ≥ 1 天？ → 是 → 顯示 paywall
  ↓ 否
不顯示 paywall
```

## 使用方式

### 在 App 啟動時檢查

```swift
// 在 initializeAppState() 中
if hasCompletedOnboarding {
    checkPaywallDisplay()
}

private func checkPaywallDisplay() {
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        paywallManager.checkAndShowPaywallIfNeeded { didShow in
            if didShow {
                print("🎯 PaywallManager: 已顯示 paywall")
            }
        }
    }
}
```

### 手動顯示 paywall

```swift
PaywallManager.shared.showPaywall()
```

### 檢查是否應該顯示

```swift
let shouldShow = PaywallManager.shared.shouldShowPaywallOnAppLaunch()
```

## Debug 功能

### 開發者選項（僅在 DEBUG 模式下可見）

在設定頁面中提供以下 debug 選項：

1. **DEBUG PAYWALL**: 切換 debug 模式
2. **CLEAR PAYWALL HISTORY**: 清除顯示記錄
3. **PAYWALL DEBUG INFO**: 顯示當前狀態資訊
4. **SHOW PAYWALL NOW**: 立即顯示 paywall
5. **RUN PAYWALL TESTS**: 執行自動化測試

### Debug 資訊

```swift
print(PaywallManager.shared.getDebugInfo())
```

輸出範例：
```
PaywallManager Debug Info:
- 是否已訂閱: false
- Debug 模式: false
- 上次顯示時間: 2025年7月23日 下午2:30:00
- 應該顯示 paywall: true
- 距離上次顯示: 2 天
- 是否為今天: false
```

## 測試

### 自動化測試

使用 `PaywallManagerTests` 類別進行測試：

```swift
PaywallManagerTests.runAllTests()
```

測試場景包括：
- 首次安裝
- 同一天重複開啟
- 距離上次顯示超過1天
- Debug 模式
- 已訂閱用戶

### 手動測試

1. **測試首次安裝**：
   ```swift
   PaywallManagerTests.resetTestState()
   ```

2. **測試不同時間場景**：
   ```swift
   PaywallManagerTests.simulateTimeScenario(daysAgo: 2)
   ```

3. **測試 Debug 模式**：
   ```swift
   AppSettings.shared.paywallDebugMode = true
   ```

## 配置

### UserDefaults Keys

- `lastPaywallShownDate`: 上次顯示 paywall 的時間
- `paywallDebugMode`: Debug 模式開關

### 時間間隔

目前設定為 1 天，可在 `PaywallManager.shouldShowPaywallOnAppLaunch()` 中修改：

```swift
if daysSinceLastShown >= 1 { // 修改這裡的數值
    return true
}
```

## 注意事項

1. **IAP 服務依賴**：PaywallManager 依賴 IAPService 來檢查訂閱狀態
2. **時間延遲**：App 啟動時會延遲 1 秒檢查 paywall，確保 IAP 服務完全初始化
3. **Onboarding 優先**：只有完成 onboarding 的用戶才會檢查 paywall
4. **Debug 模式**：僅在 DEBUG 編譯模式下顯示開發者選項

## 未來擴展

可考慮的功能擴展：

1. **更複雜的顯示邏輯**：基於用戶行為、使用頻率等
2. **A/B 測試**：不同的顯示策略
3. **本地化**：不同地區的顯示策略
4. **分析追蹤**：記錄 paywall 顯示和轉換率
5. **動態配置**：從服務器獲取顯示規則
