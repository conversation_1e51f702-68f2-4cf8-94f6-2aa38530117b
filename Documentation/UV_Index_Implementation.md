# 紫外線指數功能實現文件

## 概述
本文件記錄了在 MinimalistWeather 專案中實現紫外線指數顯示功能的詳細過程。

## 實現內容

### 1. 資料模型更新

#### WeatherData.swift
- 在 `WeatherData` 結構中添加了 `uvIndex: Int?` 屬性
- 在 `HourlyForecast` 結構中添加了 `uvIndex: Int?` 屬性
- 更新了相應的初始化方法以支援 UV 指數參數

### 2. API 數據解析

#### WeatherService.swift
- 在解析當前天氣數據時添加了 UV 指數的處理：
  ```swift
  let uvIndex = currentData.uv.index == -999 ? nil : currentData.uv.index
  ```
- 在創建 `HourlyForecast` 和 `WeatherData` 實例時傳遞 UV 指數
- 在每小時預報數據處理中也添加了 UV 指數解析
- 更新了 mock 數據以包含 UV 指數（設為 6）

### 3. UI 顯示實現

#### WeatherDetailView.swift
- 添加了 `getSelectedUVIndex()` 方法來獲取選中時間點的 UV 指數
- 添加了 `getGridColumnCount()` 方法來動態計算網格列數
- 在天氣指標網格中添加了 UV 指數顯示項目
- 實現了 -999 值的隱藏邏輯（當 API 回應 -999 時不顯示該項目）

### 4. 圖標支援

#### AppIcons.swift
- 添加了 `uv` 圖標常數：`static let uv: String = "\u{E900}"`（使用太陽圖標）

#### AppIconsSymbol.swift
- 在系統圖標映射中添加了 UV 圖標對應的 SF Symbol：`"sun.max"`

### 5. 多語系支援

為以下 12 種語言添加了 "uv_index" 翻譯：

| 語言 | 翻譯 |
|------|------|
| 🇺🇸 English (en) | UV Index |
| 🇬🇧 English (en-GB) | UV Index |
| 🇦🇺 English (en-AU) | UV Index |
| 🇹🇼 Traditional Chinese (zh-Hant) | 紫外線指數 |
| 🇯🇵 Japanese (ja) | 紫外線指数 |
| 🇫🇷 French (fr) | Indice UV |
| 🇨🇦 French (fr-CA) | Indice UV |
| 🇩🇪 German (de) | UV-Index |
| 🇪🇸 Spanish (es) | Índice UV |
| 🇮🇹 Italian (it) | Indice UV |
| 🇳🇱 Dutch (nl) | UV-index |
| 🇩🇰 Danish (da) | UV-indeks |
| 🇸🇪 Swedish (sv) | UV-index |
| 🇳🇴 Norwegian (nb) | UV-indeks |
| 🇫🇮 Finnish (fi) | UV-indeksi |

## API 數據格式

系統支援的 API 回應格式：
```json
{
  "current": [
    {
      "uv": {
        "index": 6
      }
    }
  ]
}
```

## 特殊處理

### -999 值處理
- 當 API 回應中 UV 指數為 -999 時，該項目會被隱藏不顯示
- 網格佈局會動態調整列數以適應可顯示的項目數量

### UV 指數範圍
- 支援 0-11 的標準 UV 指數範圍
- 無單位顯示（不像溫度有 °C 或濕度有 %）

### Pro 功能
- UV 指數顯示被設定為 Pro 功能
- 免費用戶會看到鎖定狀態，點擊後會顯示付費牆

## 網格佈局邏輯

網格列數計算邏輯：
- 基本項目：體感溫度、濕度、降雨機率（3 列）
- 如果雲量不是 -999：+1 列
- 如果 UV 指數不是 -999：+1 列
- 最多 5 列，最少 3 列

## 測試建議

1. 測試正常 UV 指數顯示（0-11 範圍）
2. 測試 -999 值的隱藏功能
3. 測試多語系切換
4. 測試網格佈局在不同項目組合下的表現
5. 測試 Pro 功能鎖定狀態

## 注意事項

- UV 指數使用太陽圖標，與晴天天氣圖標相同但語意不同
- 所有尺寸使用 AutoInch 框架的 `.auto()` 方法進行自適應
- 遵循專案的 MVVM 架構模式
- 符合專案的多語系管理規範
