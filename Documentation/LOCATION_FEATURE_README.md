# Location功能實作說明

## 專案架構分析

### Location儲存結構
- **SavedLocation模型**：儲存經緯度(lat, lon)、名稱(name)、國家代碼(country)
- **LocationRepository**：負責location的CRUD操作，已有舊版數據遷移機制
- **WeatherService**：原本使用OpenWeatherMap的geocoding，現在新增Google Geocoding支援

### 新增的檔案和功能

#### 1. GoogleGeocodingService.swift
- 實作Google Geocoding API服務
- 支援多語系搜尋 (預設zh-TW)
- 包含完整的錯誤處理
- 自動轉換為SavedLocation格式

#### 2. LocalizationManager.swift
- 多語系管理器
- 支援Google Maps API的25種語言
- 自動偵測系統語言
- 可動態切換語言設定

#### 3. 更新的OnboardingLocationView.swift
- 完全重新設計的UI流程
- 支援四種狀態切換：
  - 初始新增按鈕
  - 搜尋模式 (輸入框 + 搜尋結果)
  - 確認選擇
  - 已儲存地區清單

#### 4. 更新的OnboardingBottomButtons.swift
- 支援動態禁用/啟用
- 當沒有儲存地區時，無法進入下一步

#### 5. 新增的AppIcons
- `edit`: 編輯圖標
- `delete`: 刪除圖標  
- `location`: 位置圖標

## UI/UX流程

### 1. 初始狀態
- 顯示「Add forecasts city」按鈕
- 底部按鈕為禁用狀態

### 2. 搜尋模式
- 點擊新增按鈕後，淡入搜尋輸入框
- 包含搜尋按鈕和取消按鈕
- 即時顯示載入狀態和錯誤訊息

### 3. 搜尋結果
- Radio button單選清單
- 顯示城市名稱和完整地址
- 選擇後出現確認按鈕

### 4. 已儲存地區
- 顯示已儲存的地區清單
- 每個地區有刪除按鈕
- 新增更多城市按鈕
- 底部按鈕變為可用狀態

## 技術特點

### 1. 向後兼容
- LocationRepository包含舊版數據遷移邏輯
- 自動將舊的LocationEntry轉換為新的SavedLocation格式

### 2. 多語系支援
- 基於Google Maps API支援的語言清單
- 自動偵測系統語言
- 可擴展至25種語言

### 3. 動畫效果
- 所有狀態切換都有淡入淡出動畫
- 統一的動畫時長(0.3秒)
- 流暢的用戶體驗

### 4. 錯誤處理
- 網路錯誤處理
- API錯誤顯示
- 空搜尋結果提示

## 設定需求

### Info.plist配置
```xml
<key>GoogleGeocodingAPIKey</key>
<string>YOUR_GOOGLE_GEOCODING_API_KEY</string>
```

### Google Geocoding API
- 需要在Google Cloud Console啟用Geocoding API
- 取得API Key並設定在Info.plist中
- 建議設定API使用限制和配額

## 使用方式

1. 在Google Cloud Console建立專案
2. 啟用Geocoding API
3. 建立API Key
4. 在Info.plist中設定`GoogleGeocodingAPIKey`
5. 編譯並執行專案

## 支援的語言

基於Google Maps API支援的語言：
- zh-TW (繁體中文) - 預設
- zh-CN (简体中文)
- en (English)
- ja (日本語)
- ko (한국어)
- 以及其他20種語言...

## 資料結構

### SavedLocation
```swift
struct SavedLocation {
    let id: UUID
    var name: String        // 可編輯的城市名稱
    let lat: Double        // 緯度
    let lon: Double        // 經度
    let country: String    // 國家代碼
}
```

### GoogleGeocodingResult
```swift
struct GoogleGeocodingResult {
    let place_id: String
    let formatted_address: String
    let geometry: Geometry
    let address_components: [AddressComponent]
    let types: [String]
}
```

這個實作完全符合你的需求：
- ✅ 只儲存經緯度
- ✅ 地區名稱可編輯
- ✅ 使用Google Geocoding API
- ✅ 支援多語系
- ✅ 向後兼容舊版數據
- ✅ 統一的UI設計和動畫效果
- ✅ 完整的錯誤處理 