# WeatherWidgets Extension

A modular and professional iOS Widget Extension for the MinimalistWeather app, providing real-time weather information and forecasts in multiple widget sizes and layouts.

## 📱 Widget Types

### 1. Current Weather Widget
- **Size**: Small (2x2)
- **Content**: Location, weather icon, temperature, condition
- **Update Frequency**: 10 minutes
- **Cache Duration**: 2 minutes

### 2. Weekly Forecast Widget
- **Sizes**: Small (2x2), Medium (4x2)
- **Content**: 
  - Small: 5-day simplified forecast
  - Medium: 5-day detailed forecast with descriptions
- **Update Frequency**: 1 hour
- **Cache Duration**: 10 minutes

### 3. Horizontal Weekly Forecast Widget
- **Size**: Medium (4x2)
- **Content**: 5-day horizontal layout with high/low temperatures
- **Layout**: Horizontal arrangement without weather descriptions
- **Update Frequency**: 1 hour
- **Cache Duration**: 10 minutes

## 🏗️ Architecture

### Modular Structure
```
WeatherWidgets/
├── WeatherWidgetsBundle.swift          # Main bundle and previews
├── Shared/                             # Shared components
│   ├── WidgetFoundation.swift         # Core types and localization
│   ├── WidgetColors.swift             # Theme and color management
│   ├── WidgetTypes.swift              # Data models and API structures
│   ├── WidgetWeatherService.swift     # Weather API service
│   └── WidgetUtils.swift              # Utility functions
├── Widgets/                           # Widget implementations
│   ├── CurrentWeatherWidget.swift     # Current weather widget
│   ├── WeeklyForecastWidget.swift     # Weekly forecast widget
│   └── HorizontalWeeklyForecastWidget.swift # Horizontal weekly widget
├── Providers/                         # Timeline providers
│   ├── WeeklyForecastProvider.swift   # Weekly forecast data provider
│   └── HorizontalWeeklyForecastProvider.swift # Horizontal weekly provider
└── [Language].lproj/                  # Localization resources
    └── Localizable.strings
```

### Design Principles
1. **Modular Architecture**: Each component has a single responsibility
2. **Shared Components**: Common functionality is centralized
3. **Type Safety**: Strong typing throughout the codebase
4. **Localization First**: Full internationalization support
5. **Performance Optimized**: Efficient caching and API usage

## 🌐 Internationalization

### Supported Languages (12)
- 🇺🇸 English (en)
- 🇹🇼 Traditional Chinese (zh-Hant)
- 🇯🇵 Japanese (ja)
- 🇫🇷 French (fr)
- 🇩🇪 German (de)
- 🇪🇸 Spanish (es)
- 🇮🇹 Italian (it)
- 🇳🇱 Dutch (nl)
- 🇩🇰 Danish (da)
- 🇸🇪 Swedish (sv)
- 🇳🇴 Norwegian (nb)
- 🇫🇮 Finnish (fi)

### Localized Content
- **Day Names**: Shortened forms (Today→Tdy, Tomorrow→Tmr, Mon, Tue, etc.)
- **Widget States**: Loading, error messages, placeholders
- **Widget Titles**: Current weather, weekly forecast descriptions
- **Cultural Adaptation**: Language-specific abbreviations and formats

## 🎨 Theming

### Theme Support
- **Light Theme**: Clean, bright interface
- **Dark Theme**: Dark mode optimized
- **System Theme**: Follows system appearance settings
- **Dynamic Colors**: Automatically adapts to theme changes

### Color Palette
```swift
// Light Theme
primaryBackground: #F9F9F9
primaryText: #222222
secondaryText: #888888

// Dark Theme  
primaryBackground: #222222
primaryText: #F9F9F9
secondaryText: #AEAEB2
```

## 🌡️ Temperature Units

### Supported Units
- **Celsius (°C)**: Default metric unit
- **Fahrenheit (°F)**: Imperial unit
- **Dynamic Conversion**: Real-time unit switching
- **Precision**: Rounded to nearest integer for display

## 🌤️ Weather Sources

### Supported APIs
- **Apple Weather (AW)**: iOS native weather service
- **OpenWeather (OW)**: Third-party weather API
- **Google Weather (GW)**: Google's weather service
- **Central Weather (CW)**: Taiwan's official weather service

### Source Selection Logic
- **Taiwan**: Defaults to Central Weather (CW)
- **Japan/Korea**: Defaults to Apple Weather (AW)
- **Other regions**: Defaults to Google Weather (GW)
- **User Override**: Manual source selection available

## 📊 Data Flow

### App Group Integration
```
Main App ←→ App Group UserDefaults ←→ Widget Extension
```

### Shared Data Keys
- `widget_selected_location_name`: Location display name
- `widget_selected_location_id`: Unique location identifier
- `widget_selected_location_lat`: Latitude coordinate
- `widget_selected_location_lon`: Longitude coordinate
- `widget_selected_location_country`: Country code
- `widget_selected_location_weather_source`: Weather API source
- `themeMode`: Current theme setting
- `temperatureUnit`: Temperature unit preference

### Caching Strategy
- **Current Weather**: 2-minute cache, 10-minute updates
- **Weekly Forecast**: 10-minute cache, 1-hour updates
- **Cache Invalidation**: Location, theme, source, or language changes
- **Fallback Data**: Graceful degradation on API failures

## 🔧 Technical Implementation

### Core Components

#### WidgetFoundation.swift
- `TemperatureUnit`: Temperature conversion and formatting
- `WidgetLanguageDetector`: System language detection
- `WidgetLocalizer`: Localization management

#### WidgetColors.swift
- `WidgetColors`: Theme-aware color management
- `ThemeMode`: Light/dark/system theme handling
- Dynamic color adaptation

#### WidgetTypes.swift
- Data models for weather data and forecasts
- API response structures
- Timeline entry definitions

#### WidgetWeatherService.swift
- HTTP API client for weather services
- JSON parsing and error handling
- Fallback data generation

### Widget Lifecycle
1. **Timeline Request**: iOS requests widget timeline
2. **Cache Check**: Verify if cached data is valid
3. **API Call**: Fetch fresh data if cache expired
4. **Data Processing**: Parse and format weather data
5. **UI Rendering**: Generate widget view
6. **Schedule Update**: Set next refresh time

### Error Handling
- **Network Errors**: Graceful fallback to cached data
- **API Errors**: Default weather data with error indicators
- **Configuration Errors**: Clear user guidance messages
- **Localization Fallback**: English as backup language

## 🚀 Performance Optimizations

### Efficient Updates
- **Smart Caching**: Prevents unnecessary API calls
- **Conditional Refresh**: Only updates when data changes
- **Background Processing**: Non-blocking data fetching
- **Memory Management**: Lightweight data structures

### Battery Optimization
- **Minimal Network Usage**: Cached responses reduce API calls
- **Efficient Rendering**: Optimized SwiftUI views
- **Smart Scheduling**: Appropriate update intervals
- **Background Limits**: Respects iOS widget constraints

## 🧪 Testing & Development

### Preview Support
- Complete widget previews for all sizes
- Sample data for development
- Multiple language testing
- Theme variation testing

### Debug Features
- Comprehensive logging throughout the codebase
- API request/response tracking
- Cache status monitoring
- Error state visualization

## 📝 Usage Guidelines

### Adding New Widgets
1. Create widget view in `Widgets/` directory
2. Implement timeline provider in `Providers/` (if needed)
3. Add to `WeatherWidgetsBundle.swift`
4. Update localization files
5. Add preview support

### Modifying Shared Components
1. Update relevant file in `Shared/` directory
2. Ensure backward compatibility
3. Update dependent widgets
4. Test all widget types
5. Verify localization

### Adding New Languages
1. Create new `.lproj` directory
2. Copy `en.lproj/Localizable.strings` as template
3. Translate all strings
4. Add language to `WidgetLanguageDetector`
5. Test widget display

## 🔒 Security & Privacy

### Data Handling
- **Location Privacy**: Uses coordinates only, no personal data
- **API Security**: Secure HTTPS connections
- **Local Storage**: App Group sandboxing
- **No Tracking**: No user behavior analytics

### Permissions
- **Location Access**: Inherited from main app
- **Network Access**: Weather API requests only
- **Background Refresh**: iOS-managed widget updates

## 📋 Maintenance

### Regular Tasks
- **API Compatibility**: Monitor weather service changes
- **iOS Updates**: Test with new iOS versions
- **Localization Updates**: Keep translations current
- **Performance Monitoring**: Track widget performance

### Code Quality
- **Modular Design**: Easy to maintain and extend
- **Type Safety**: Compile-time error prevention
- **Documentation**: Comprehensive inline documentation
- **Testing**: Preview-based validation

---

## 📄 License

This widget extension is part of the MinimalistWeather app project.

## 🤝 Contributing

When contributing to the widget extension:
1. Follow the established modular architecture
2. Maintain localization for all supported languages
3. Ensure theme compatibility
4. Test all widget sizes and configurations
5. Update documentation as needed
