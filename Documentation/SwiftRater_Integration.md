# SwiftRater 整合文檔

## 概述
本專案已成功整合 SwiftRater 套件，用於提示用戶對應用程式進行評分。

## 設定參數
在 `MinimalistWeatherApp.swift` 的 `AppDelegate` 中設定了以下參數：

```swift
SwiftRater.daysUntilPrompt = 7          // 7天後顯示評分提示
SwiftRater.usesUntilPrompt = 5          // 使用5次後顯示評分提示
SwiftRater.daysBeforeReminding = 5      // 如果用戶選擇稍後，5天後再次提醒
SwiftRater.showLaterButton = true       // 顯示「稍後」按鈕
SwiftRater.debugMode = true             // 開發模式（正式版需設為 false）
SwiftRater.appID = "1104775712"         // App Store ID（請替換為實際ID）
```

## 觸發條件
評分提示會在以下情況下顯示：
- 應用程式啟動超過 7 天 **且**
- 用戶使用應用程式超過 5 次

## 觸發位置
- 主要觸發點：`WeatherContentView.swift` 的 `onAppear` 方法
- 每次進入天氣主頁面時會檢查是否符合顯示條件

## 測試功能
在 Debug 模式下，設定頁面會顯示「TEST RATING PROMPT」按鈕，可以手動觸發評分提示進行測試。

## 重要注意事項

### 1. App Store ID
目前使用的是範例 App ID (`1104775712`)，**正式發布前必須替換為實際的 App Store ID**。

### 2. Debug 模式
- 開發階段：`SwiftRater.debugMode = true`（每次都會顯示提示）
- 正式發布：**必須設為 `false`**

### 3. 自訂文字（可選）
如果需要自訂評分對話框的文字，可以在 AppDelegate 中添加：

```swift
SwiftRater.alertTitle = "評分應用程式"
SwiftRater.alertMessage = "如果您喜歡這個應用程式，請花一點時間評分。謝謝您的支持！"
SwiftRater.alertCancelTitle = "不，謝謝"
SwiftRater.alertRateTitle = "立即評分"
SwiftRater.alertRateLaterTitle = "稍後提醒我"
SwiftRater.appName = "極簡天氣"
```

## 檔案修改清單
1. `MinimalistWeatherApp.swift` - 添加 AppDelegate 和 SwiftRater 設定
2. `WeatherContentView.swift` - 添加 SwiftRater.check() 調用
3. `SettingsPageView.swift` - 添加測試按鈕（僅 Debug 模式）

## 發布前檢查清單
- [ ] 將 `SwiftRater.debugMode` 設為 `false`
- [ ] 替換為實際的 App Store ID
- [ ] 測試評分功能是否正常運作
- [ ] 確認評分提示的時機和頻率符合預期
