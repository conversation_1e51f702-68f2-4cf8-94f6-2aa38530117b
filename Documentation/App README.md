# MinimalistWeather - 極簡天氣應用

基於SwiftUI開發的極簡風格天氣應用，採用MVVM架構設計，支援多語系、多天氣來源、應用內購買等進階功能。

## 🌟 主要特色

### 核心功能
- **極簡設計**：簡潔優雅的UI界面，專注於天氣資訊呈現
- **垂直分頁**：流暢的垂直滑動切換天氣與設定頁面
- **多位置管理**：搜尋並儲存全球城市，快速切換查看天氣
- **智能預報**：每小時預報與5日預報，支援時間軸預覽
- **溫度圖表**：視覺化溫度變化趨勢
- **完整天氣資訊**：溫度、濕度、風速、降雨機率、日出日落等

### 進階功能
- **多天氣來源**：支援Apple Weather、OpenWeather、Google Weather、中央氣象局
- **多語系支援**：支援繁體中文、英文、日文等13種語言
- **個人化設定**：溫度單位、時間格式、測量系統、主題模式
- **應用內購買**：Pro版本解鎖進階功能
- **網路監控**：完整的離線狀態處理
- **自訂字體**：內建Lim字體
- **動畫效果**：Lottie動畫增強視覺體驗

## 📱 系統需求

- iOS 15.0+
- Xcode 13.0+
- Swift 5.5+

## 🚀 安裝與設置

### 1. 基本設置
1. 克隆或下載此專案
2. 使用Xcode打開`MinimalistWeather.xcodeproj`

### 2. API密鑰設置
在`Info.plist`中設置以下API密鑰：

```xml
<key>OpenWeatherAPIKey</key>
<string>YOUR_OPENWEATHER_API_KEY</string>

<key>GoogleGeocodingAPIKey</key>
<string>YOUR_GOOGLE_GEOCODING_API_KEY</string>
```

### 3. 獲取API密鑰

#### OpenWeather API
1. 前往 [OpenWeather官網](https://openweathermap.org/)
2. 註冊免費帳號並獲取API密鑰

#### Google Geocoding API
1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 建立專案並啟用Geocoding API
3. 建立API密鑰並設定使用限制

## 🏗️ 專案架構

本專案採用MVVM (Model-View-ViewModel) 架構，清晰分離關注點：

### 📁 目錄結構

#### Model 層 - 數據模型
- **`WeatherData.swift`** - 完整天氣數據模型，包含溫度、濕度、風速、預報等
- **`SavedLocation.swift`** - 儲存位置模型，支援多天氣來源設定
- **`LocationEntry.swift`** - 位置記錄模型（向後相容）
- **`AppSettings.swift`** - 應用設定模型，包含溫度單位、主題等
- **`MainPageData.swift`** - 主頁面專用數據結構

#### View 層 - 用戶界面
- **主要頁面**
  - `MainPagingView.swift` - 垂直分頁主視圖
  - `WeatherPageView.swift` - 天氣頁面
  - `WeatherContentView.swift` - 天氣內容視圖
  - `SettingsPageView.swift` - 設定頁面

- **引導流程**
  - `OnboardingCoordinator.swift` - 引導流程協調器
  - `OnboardingWelcomeView.swift` - 歡迎頁面
  - `OnboardingLocationView.swift` - 位置設定頁面
  - `OnboardingFeaturePlanView.swift` - 功能方案頁面
  - `OnboardingCompleteView.swift` - 完成頁面

- **功能組件**
  - `LocationSearchView.swift` - 位置搜尋界面
  - `WeatherDetailView.swift` - 天氣詳細資訊
  - `TemperatureChartView.swift` - 溫度圖表
  - `TimelineView.swift` - 時間軸預覽
  - `PaywallView.swift` - 付費牆界面

- **設定選擇器**
  - `TemperatureUnitPickerView.swift` - 溫度單位選擇
  - `LanguageSelectionView.swift` - 語言選擇
  - `ThemePickerView.swift` - 主題選擇
  - `WeatherSourcePickerView.swift` - 天氣來源選擇

#### ViewModel 層 - 業務邏輯
- **`WeatherViewModel.swift`** - 核心天氣視圖模型
  - 天氣數據管理
  - 位置切換邏輯
  - 設定管理
  - 自動刷新機制

#### Services 層 - 服務與API
- **天氣服務**
  - `WeatherService.swift` - 多來源天氣API服務
  - `GoogleTimezoneService.swift` - Google時區服務

- **位置服務**
  - `GoogleGeocodingService.swift` - Google地理編碼服務
  - `LocationRepository.swift` - 位置數據儲存

- **應用服務**
  - `IAPService.swift` - 應用內購買服務（RevenueCat整合）
  - `LanguageService.swift` - 多語系管理服務

#### Utilities 層 - 工具與輔助
- **UI工具**
  - `HexColor.swift` - 十六進制顏色支援
  - `AppIcons.swift` / `AppIconsSymbol.swift` - 圖標系統
  - `LoadingView.swift` - 載入動畫
  - `CustomLottieView.swift` - Lottie動畫組件

- **系統工具**
  - `NetworkMonitor.swift` - 網路狀態監控
  - `FontLoader.swift` - 自訂字體載入
  - `DateTimeFormatter.swift` - 日期時間格式化
  - `LocalizationManager.swift` - 本地化管理
  - `CountryCodeConverter.swift` - 國家代碼轉換

#### Resources 層 - 資源文件
- **字體文件**：`Lim.ttf`
- **動畫文件**：各種天氣狀況的Lottie動畫
- **多語系文件**：13種語言的本地化字串

## 🔧 核心功能實現

### 多天氣來源支援
- **Apple Weather (AW)** - 全球支援
- **OpenWeather (OW)** - 全球支援
- **Google Weather (GW)** - 除日韓外全球支援
- **中央氣象局 (CW)** - 僅台灣地區

### 多語系系統
支援13種語言，包括：
- 繁體中文 (zh-Hant)
- 英文 (en)
- 日文 (ja)
- 法文 (fr)
- 德文 (de)
- 西班牙文 (es)
- 義大利文 (it)
- 荷蘭文 (nl)
- 瑞典文 (sv)
- 丹麥文 (da)
- 挪威文 (no)
- 芬蘭文 (fi)

### 應用內購買 (IAP)
- 使用RevenueCat進行訂閱管理
- 月費方案：$0.99/月
- 年費方案：$4.99/年
- Pro功能：無限位置、主題選擇、進階圖表

## ⚙️ 開發設置

### 模擬數據
開發階段可在`WeatherService.swift`中設置：
```swift
private let useMockData = true  // 開發時使用
private let useMockData = false // 正式版本使用
```

### StoreKit測試
專案包含`Weather Minimal.storekit`配置文件，支援本地IAP測試。

## 🎯 使用指南

### 首次啟動
1. **歡迎頁面** - 介紹應用功能
2. **位置設定** - 搜尋並新增城市
3. **方案選擇** - 選擇免費或Pro版本
4. **完成設定** - 開始使用應用

### 主要操作
- **垂直滑動** - 在天氣頁面和設定頁面間切換
- **位置管理** - 在設定頁面點擊"LOCATION"管理城市
- **天氣來源** - 為每個城市選擇不同的天氣來源
- **個人化設定** - 調整溫度單位、語言、主題等

### Pro功能
- 無限制新增位置
- 主題模式選擇
- 進階天氣圖表
- 優先客服支援

## 🔍 技術特點

### 性能優化
- **智能快取** - 減少不必要的API請求
- **自動刷新** - 定時更新天氣數據
- **網路監控** - 離線狀態優雅處理
- **記憶體管理** - 高效的數據結構設計

### 用戶體驗
- **流暢動畫** - 0.3秒統一動畫時長
- **響應式設計** - 適配不同螢幕尺寸
- **無障礙支援** - 支援VoiceOver等輔助功能
- **錯誤處理** - 友善的錯誤提示訊息

### 數據安全
- **本地儲存** - 位置數據僅存於設備
- **API安全** - 密鑰加密存儲
- **隱私保護** - 不收集個人資訊

## 🛠️ 開發指南

### 新增天氣來源
1. 在`AppSettings.swift`中新增`WeatherSource`枚舉值
2. 在`WeatherService.swift`中實現對應API邏輯
3. 更新`WeatherSourcePickerView.swift`顯示選項
4. 新增本地化字串

### 新增語言支援
1. 在`Resources`目錄新增對應的`.lproj`資料夾
2. 複製`Localizable.strings`並翻譯內容
3. 在`LanguageService.swift`中新增語言選項
4. 測試所有界面的本地化效果

### 自訂主題
1. 在`HexColor.swift`中定義新的顏色組合
2. 在`AppSettings.swift`中新增`ThemeMode`選項
3. 更新`ThemePickerView.swift`顯示新主題
4. 確保所有UI元件支援新主題

## � 相關文檔

- **[位置功能實作說明](MinimalistWeather/LOCATION_FEATURE_README.md)** - Google Geocoding整合
- **[天氣來源實作摘要](WEATHER_SOURCE_IMPLEMENTATION_SUMMARY.md)** - 多天氣來源架構
- **[天氣本地化說明](MinimalistWeather/README_WeatherLocalization.md)** - 多語系實作細節

## 🐛 已知問題

- 部分地區的Google Weather API可能有延遲
- 中央氣象局API僅支援台灣地區
- 某些Lottie動畫在舊設備上可能較慢

## 🔮 未來規劃

- [ ] 支援Apple Watch應用
- [ ] 天氣警報推送通知
- [ ] 社群分享功能（生成美觀的天氣卡片）
- [ ] 天氣歷史記錄與趨勢分析
- [ ] 小工具(Widget)支援
- [ ] 更多Lottie動畫效果
- [ ] 天氣雷達圖整合
- [ ] 空氣品質指數顯示
- [ ] 多城市天氣對比功能

## 📞 支援與回饋

- **功能建議** - [Featurebase](https://minlsm.featurebase.app/)
- **技術問題** - 請提交GitHub Issue
- **商業合作** - 請聯繫開發團隊

## �📄 授權

本專案僅供學習和研究使用。

---

**MinimalistWeather** - 讓天氣資訊回歸簡約本質 🌤️