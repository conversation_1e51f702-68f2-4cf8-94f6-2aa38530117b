//
//  CustomWhatsNewView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/23.
//

import SwiftUI
import AutoInch

// MARK: - 資料模型
struct WhatsNewVersion: Codable {
    let version: String
    let new: [WhatsNewFeature]
}

struct WhatsNewFeature: Codable {
    let icon: String
    let title: String
    let subtitle: String
    let body: String
}

// MARK: - 主要視圖
struct CustomWhatsNewView: View {
    @Binding var showWhatsNew: Bool
    @EnvironmentObject var languageService: LanguageService
    @EnvironmentObject var networkMonitor: NetworkMonitor
    @StateObject private var whatsNewService = WhatsNewService.shared
    @State private var whatsNewData: [WhatsNewVersion] = []
    @State private var isLoading = true
    @State private var currentVersionIndex = 0
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("whats_new".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            if isLoading {
                // 載入狀態
                VStack {
                    Spacer()
                    LoadingView(color: HexColor.themed(.primaryText), size: CGFloat(40).auto(), speed: 0.8)
                    Spacer()
                }
            } else if whatsNewData.isEmpty {
                // 無資料狀態
                VStack {
                    Spacer()
                    Text("no_updates_available".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .regular))
                        .foregroundColor(HexColor.themed(.secondaryText))
                        .multilineTextAlignment(.center)
                    Spacer()
                }
            } else {
                // 顯示版本內容
                let currentVersion = whatsNewData[currentVersionIndex]
                
                // 版本號
                Text("version".localized + " " + currentVersion.version)
                    .font(.system(size: CGFloat(18).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.accent))
                    .padding(.top, CGFloat(12).auto())
                
                // 功能列表
                ScrollView {
                    VStack(alignment: .leading, spacing: CGFloat(16).auto()) {
                        ForEach(currentVersion.new, id: \.title) { feature in
                            WhatsNewFeatureRow(feature: feature)
                        }
                    }
                    .padding(.top, CGFloat(20).auto())
                }
                
                Spacer()
                
                // 底部按鈕區域
                WhatsNewBottomButtons(
                    onClose: {
                        showWhatsNew = false
                        whatsNewService.markWhatsNewAsShown()
                    },
                    onPrevious: currentVersionIndex < whatsNewData.count - 1 ? {
                        currentVersionIndex += 1
                    } : nil,
                    onNext: currentVersionIndex > 0 ? {
                        currentVersionIndex -= 1
                    } : nil,
                    currentPage: currentVersionIndex + 1,
                    totalPages: whatsNewData.count
                )
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
        .onAppear {
            loadWhatsNewData()
        }
    }
    
    /// 載入 What's New 資料
    private func loadWhatsNewData() {
        isLoading = true

        let languageCode = languageService.currentLanguage.code

        // 檢查網路狀態
        if networkMonitor.isConnected {
            // 有網路，優先嘗試載入遠端資料
            loadRemoteData(for: languageCode) { remoteData in
                DispatchQueue.main.async {
                    if let data = remoteData {
                        whatsNewData = data
                        isLoading = false
                        return
                    }

                    // 遠端失敗，嘗試載入本地檔案
                    fallbackToLocalData(for: languageCode)
                }
            }
        } else {
            // 沒有網路，直接使用本地檔案
            print("CustomWhatsNewView: 無網路連線，直接使用本地檔案")
            fallbackToLocalData(for: languageCode)
        }
    }

    /// 回退到本地資料
    private func fallbackToLocalData(for languageCode: String) {
        if let data = loadLocalData(for: languageCode) {
            whatsNewData = data
            isLoading = false
            return
        }

        // 本地檔案也不存在，載入預設資料
        loadDefaultData()
        isLoading = false
    }
    
    /// 載入遠端 JSON 資料
    private func loadRemoteData(for languageCode: String, completion: @escaping ([WhatsNewVersion]?) -> Void) {
        let remoteURL = whatsNewService.getRemoteDataURL(for: languageCode)

        guard let url = URL(string: remoteURL) else {
            print("CustomWhatsNewView: 無效的遠端 URL \(remoteURL)")
            completion(nil)
            return
        }

        print("CustomWhatsNewView: 嘗試載入遠端資料 \(remoteURL)")

        // 設定請求超時時間
        var request = URLRequest(url: url)
        request.timeoutInterval = 10.0 // 10秒超時

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("CustomWhatsNewView: 遠端載入失敗 - \(error.localizedDescription)")
                completion(nil)
                return
            }

            guard let data = data else {
                print("CustomWhatsNewView: 遠端回應無資料")
                completion(nil)
                return
            }

            do {
                let versions = try JSONDecoder().decode([WhatsNewVersion].self, from: data)
                print("CustomWhatsNewView: 成功載入遠端資料，共 \(versions.count) 個版本")
                completion(versions)
            } catch {
                print("CustomWhatsNewView: 遠端資料解析失敗 - \(error.localizedDescription)")
                completion(nil)
            }
        }.resume()
    }

    /// 載入本地 JSON 檔案
    private func loadLocalData(for languageCode: String) -> [WhatsNewVersion]? {
        let fileName = whatsNewService.getLocalDataFileName(for: languageCode)

        guard let path = Bundle.main.path(forResource: fileName, ofType: "json"),
              let data = try? Data(contentsOf: URL(fileURLWithPath: path)),
              let versions = try? JSONDecoder().decode([WhatsNewVersion].self, from: data) else {
            print("CustomWhatsNewView: 無法載入本地檔案 \(fileName).json")
            return nil
        }

        print("CustomWhatsNewView: 成功載入本地檔案 \(fileName).json")
        return versions
    }
    
    /// 載入預設資料
    private func loadDefaultData() {
        whatsNewData = [
            WhatsNewVersion(
                version: "1.3.0",
                new: [
                    WhatsNewFeature(
                        icon: "sparkles",
                        title: "新功能展示",
                        subtitle: "SwiftNEW 整合",
                        body: "全新的版本更新展示功能，讓您輕鬆了解最新功能和改進。"
                    ),
                    WhatsNewFeature(
                        icon: "globe",
                        title: "多語系支援",
                        subtitle: "國際化體驗",
                        body: "根據您的語言設定自動載入對應的版本說明。"
                    ),
                    WhatsNewFeature(
                        icon: "paintbrush",
                        title: "主題整合",
                        subtitle: "視覺一致性",
                        body: "完美整合應用的主題系統，在淺色和深色主題下都有絕佳的視覺效果。"
                    )
                ]
            )
        ]
    }
}

// MARK: - 功能行視圖
struct WhatsNewFeatureRow: View {
    let feature: WhatsNewFeature
    
    var body: some View {
        HStack(alignment: .top, spacing: CGFloat(16).auto()) {
            // 圖標
            AppIconsSymbol.createView(
                for: AppIcons.check,
                fontSize: CGFloat(24).auto(),
                color: HexColor.themed(.accent)
            )
            .frame(width: CGFloat(24).auto(), height: CGFloat(24).auto())
            
            // 內容
            VStack(alignment: .leading, spacing: CGFloat(4).auto()) {
                Text(feature.title)
                    .font(.system(size: CGFloat(18).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                
                // Text(feature.subtitle)
                //     .font(.system(size: CGFloat(14).auto(), weight: .regular))
                //     .foregroundColor(HexColor.themed(.accent))
                
                Text(feature.body)
                    .font(.system(size: CGFloat(14).auto(), weight: .regular))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .multilineTextAlignment(.leading)
                    .padding(.top, CGFloat(4).auto())
            }
            
            Spacer()
        }
        .padding(.vertical, CGFloat(8).auto())
    }
    

}

// MARK: - 底部按鈕
struct WhatsNewBottomButtons: View {
    let onClose: () -> Void
    let onPrevious: (() -> Void)?
    let onNext: (() -> Void)?
    let currentPage: Int
    let totalPages: Int
    
    var body: some View {
        HStack {
            // 左側：關閉按鈕
            Button(action: onClose) {
                Text("close".localized)
                    .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
            }
            
            Spacer()
            
            // 中間：頁面指示器（如果有多個版本）
            // if totalPages > 1 {
            //     Text("\(currentPage) / \(totalPages)")
            //         .font(.system(size: CGFloat(14).auto(), weight: .regular))
            //         .foregroundColor(HexColor.themed(.secondaryText))
            // }
            
            Spacer()

            Button(action: onClose) {
                AppIconsSymbol.createView(
                    for: AppIcons.check,
                    fontSize: CGFloat(44).auto(),
                    color: HexColor.themed(.secondaryBackground)
                )
                .background(
                    Circle()
                        .fill(HexColor.themed(.primaryText))
                )
            }
            
            // 右側：導航按鈕
            // HStack(spacing: CGFloat(16).auto()) {
            //     if let onPrevious = onPrevious {
            //         Button(action: onPrevious) {
            //             AppIconsSymbol.createView(
            //                 for: AppIcons.scrollUp,
            //                 fontSize: CGFloat(44).auto(),
            //                 color: HexColor.themed(.secondaryBackground)
            //             )
            //             .background(
            //                 Circle()
            //                     .fill(HexColor.themed(.primaryText))
            //             )
            //         }
            //     }

            //     if let onNext = onNext {
            //         Button(action: onNext) {
            //             AppIconsSymbol.createView(
            //                 for: AppIcons.next,
            //                 fontSize: CGFloat(44).auto(),
            //                 color: HexColor.themed(.secondaryBackground)
            //             )
            //             .background(
            //                 Circle()
            //                     .fill(HexColor.themed(.primaryText))
            //             )
            //         }
            //     }
            // }
        }
    }
}

// MARK: - 預覽
#Preview {
    CustomWhatsNewView(showWhatsNew: .constant(true))
        .environmentObject(LanguageService.shared)
}
