//
//  WeatherPageView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct WeatherPageView: View {
    @ObservedObject var viewModel: WeatherViewModel
    var horizontalPadding: CGFloat = CGFloat(60).auto()
    var onNextPage: () -> Void
    
    var body: some View {
        GeometryReader { geometry in
            WeatherContentView(
                viewModel: viewModel, 
                scrollToTopAction: {
                    // 當從 LocationSearchView 返回時，發送通知回到第一頁
                    NotificationCenter.default.post(name: Notification.Name("ScrollToPage1"), object: nil)
                },
                onNextPage: onNextPage,
                horizontalPadding: horizontalPadding
            )
        }
        .background(HexColor.themed(.primaryBackground))
    }
}

// MARK: - 預覽
struct WeatherPageView_Previews: PreviewProvider {
    static var previews: some View {
        WeatherPageView(viewModel: WeatherViewModel(), onNextPage: {})
    }
} 