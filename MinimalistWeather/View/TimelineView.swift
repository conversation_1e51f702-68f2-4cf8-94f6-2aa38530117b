//
//  TimelineView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch
import Combine

// MARK: - 時間軸視圖
struct TimelineView: View {
    // MARK: - 屬性
    @ObservedObject var viewModel: WeatherViewModel
    @Binding var selectedTimeIndex: Int
    @State private var isScrolling: Bool = false
    @State private var scrollSpeed: CGFloat = 0
    @State private var lastScrollTime: Date = Date()
    @State private var timelineInitialized: Bool = false
    @State private var isAnimatingToCenter: Bool = false
    
    // 佈局常量
    private let hourWidth: CGFloat = CGFloat(38).auto()     // 略微增加每小時寬度，確保更好的可讀性
    private let majorTickHeight: CGFloat = CGFloat(24).auto()  // 增加主刻度高度
    private let minorTickHeight: CGFloat = CGFloat(12).auto()  // 調整次刻度高度
    private let labelHeight: CGFloat = CGFloat(22).auto()      // 增加標籤高度
    private let scrollAnimationDuration: Double = 0.35        // 略微縮短滾動動畫時間
    private let labelHideDelay: Double = 1.2               // 縮短標籤隱藏延遲時間
    
    // MARK: - 時間數據，合併小時和日期
    private struct TimelineItem: Identifiable {
        let id = UUID()
        let date: Date           // 完整日期時間
        let hour: Int            // 小時 (0-23)
        let isNewDay: Bool       // 是否為新的一天的開始 (0點)
        let dayLabel: String     // 星期幾標籤 (Mon, Tue...)
        let timeLabel: String    // 時間標籤 (03:00, 06:00...)
        let showTimeLabel: Bool  // 是否顯示時間標籤 (每3小時顯示一次)
        let showTick: Bool       // 是否顯示刻度線 (每2小時顯示一次)
        let isMajorTick: Bool    // 是否為主刻度 (每3小時為主刻度)
        let originalIndex: Int   // 原始數據中的索引
    }
    
    // 生成時間線項目，整合小時和日期
    private var timelineItems: [TimelineItem] {
        var items: [TimelineItem] = []
        let hourlyData = viewModel.hourlyForecasts
        
        // 如果沒有數據或數據不足，創建足夠的測試數據以確保UI可見
        if hourlyData.count < 40 {
            // 創建5天的測試數據，每小時一個時間點
            for hour in stride(from: 0, to: 120, by: 1) {
                let calendar = Calendar.current
                let baseDate = Date()
                let date = calendar.date(byAdding: .hour, value: hour, to: baseDate) ?? baseDate
                
                let hour24 = hour % 24
                let isNewDay = hour24 == 0
                // 使用多語系星期縮寫
                let dayLabel = DateTimeFormatter.shared.formatWeekdayAbbreviation(date)
                
                let timeLabel = hour == 0 ? "NOW" : DateTimeFormatter.shared.formatTimeString("\(hour24):00")
                
                // 調整刻度顯示密度
                let showTimeLabel = hour == 0 || hour24 % 3 == 0  // 確保"現在"顯示時間標籤
                let showTick = hour == 0 || hour24 % 2 == 0       // 確保"現在"顯示刻度線
                let isMajorTick = hour == 0 || hour24 % 3 == 0    // 確保"現在"為主刻度
                
                items.append(TimelineItem(
                    date: date,
                    hour: hour24,
                    isNewDay: isNewDay,
                    dayLabel: dayLabel,
                    timeLabel: timeLabel,
                    showTimeLabel: showTimeLabel,
                    showTick: showTick,
                    isMajorTick: isMajorTick,
                    originalIndex: hour
                ))
            }
        } else {
            // 使用真實數據
            // 使用多語系星期縮寫
            
            // 處理每個小時預報
            for (index, forecast) in hourlyData.enumerated() {
                // 從時間字串提取小時
                let timeString = forecast.time
                
                // 如果是第一個項目(當前時間)，則特殊處理
                if index == 0 {
                    // 獲取完整日期時間 - 當前時間
                    let date = Date()
                    let hour = Calendar.current.component(.hour, from: date)
                    let isNewDay = hour == 0
                    let dayLabel = DateTimeFormatter.shared.formatWeekdayAbbreviation(date)
                    
                    // 添加到項目列表
                    items.append(TimelineItem(
                        date: date,
                        hour: hour,
                        isNewDay: isNewDay,
                        dayLabel: dayLabel,
                        timeLabel: "NOW",
                        showTimeLabel: true,  // 確保顯示"現在"標籤
                        showTick: true,       // 確保顯示刻度線
                        isMajorTick: true,    // 確保為主刻度
                        originalIndex: index
                    ))
                    continue
                }
                
                // 從時間字串提取小時 (對於非第一個項目)
                let timeComponents = timeString.split(separator: ":")
                let hour = Int(timeComponents.first ?? "0") ?? 0
                
                // 獲取完整日期時間
                let calendar = Calendar.current
                let baseDate = Date()
                
                // 計算距離現在的小時數
                let hoursFromNow = index
                
                // 創建對應日期
                let date = calendar.date(byAdding: .hour, value: hoursFromNow, to: baseDate) ?? baseDate
                
                // 判斷是否為新的一天的開始 (0點)
                let isNewDay = hour == 0
                
                // 獲取星期幾標籤
                let dayLabel = DateTimeFormatter.shared.formatWeekdayAbbreviation(date)
                
                // 獲取時間標籤（使用系統格式）
                let timeLabel = DateTimeFormatter.shared.formatTimeString("\(hour):00")
                
                // 調整刻度顯示密度
                let showTimeLabel = hour % 3 == 0  // 每3小時顯示一次時間標籤
                let showTick = hour % 2 == 0       // 每2小時顯示一次刻度線
                let isMajorTick = hour % 3 == 0    // 每3小時為主刻度
                
                // 添加到項目列表
                items.append(TimelineItem(
                    date: date,
                    hour: hour,
                    isNewDay: isNewDay,
                    dayLabel: dayLabel,
                    timeLabel: timeLabel,
                    showTimeLabel: showTimeLabel,
                    showTick: showTick,
                    isMajorTick: isMajorTick,
                    originalIndex: index
                ))
            }
        }
        
        return items
    }
    
    // MARK: - 視圖
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 主滾動區域
                ScrollViewReader { scrollProxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        ZStack(alignment: .top) {
                            // 時間軸內容
                            HStack(spacing: 0) {
                                // 左側間距（使當前時間可以正確居中）
                                Spacer()
                                    .frame(width: geometry.size.width / 2 - hourWidth / 2)
                                
                                // 時間刻度線和標籤
                                ForEach(timelineItems.indices, id: \.self) { index in
                                    let item = timelineItems[index]
                                    timelineTickView(
                                        item: item, 
                                        index: index,
                                        isVisible: isScrolling || index == selectedTimeIndex
                                    )
                                    .id("time_\(index)")
                                }
                                
                                // 右側間距
                                Spacer()
                                    .frame(width: geometry.size.width / 2 - hourWidth / 2)
                            }
                        }
                        .onAppear {
                            // 首次載入時設定初始位置
                            if !timelineInitialized {
                                timelineInitialized = true
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        scrollProxy.scrollTo("time_\(selectedTimeIndex)", anchor: .center)
                                    }
                                }
                            }
                        }
                        .contentShape(Rectangle())
                    }
                    .simultaneousGesture(
                        DragGesture(minimumDistance: 5)
                            .onChanged { value in
                                // 標記為正在滾動
                                isScrolling = true
                                
                                // 計算滾動速度
                                let currentTime = Date()
                                let timeDelta = currentTime.timeIntervalSince(lastScrollTime)
                                if timeDelta > 0 {
                                    scrollSpeed = abs(CGFloat(value.translation.width) / CGFloat(timeDelta))
                                }
                                lastScrollTime = currentTime
                            }
                            .onEnded { value in
                                // 計算拖動後的新選中索引
                                let totalItems = timelineItems.count
                                
                                // 如果只是輕點或輕微拖動，不處理
                                if abs(value.translation.width) < 5 {
                                    return
                                }
                                
                                var newIndex = selectedTimeIndex
                                // 根據拖動方向和距離調整索引
                                if value.translation.width > 0 {
                                    // 向右拖動，索引減少
                                    let delta = Int(abs(value.translation.width) / hourWidth)
                                    newIndex = max(0, selectedTimeIndex - delta - (scrollSpeed > 300 ? 1 : 0))
                                } else {
                                    // 向左拖動，索引增加
                                    let delta = Int(abs(value.translation.width) / hourWidth)
                                    newIndex = min(totalItems - 1, selectedTimeIndex + delta + (scrollSpeed > 300 ? 1 : 0))
                                }
                                
                                // 更新索引並滾動到新位置
                                if newIndex != selectedTimeIndex {
                                    isAnimatingToCenter = true
                                    withAnimation(.spring(response: scrollAnimationDuration, dampingFraction: 0.75, blendDuration: 0)) {
                                        selectedTimeIndex = newIndex
                                        // 滾動到選中位置
                                        scrollProxy.scrollTo("time_\(newIndex)", anchor: .center)
                                    }
                                    
                                    // 動畫結束後
                                    DispatchQueue.main.asyncAfter(deadline: .now() + scrollAnimationDuration) {
                                        isAnimatingToCenter = false
                                    }
                                }
                                
                                // 滑動結束後，短暫延遲後隱藏其他時間
                                DispatchQueue.main.asyncAfter(deadline: .now() + labelHideDelay) {
                                    if !isScrolling && !isAnimatingToCenter { // 確保沒有新的滾動事件發生
                                        isScrolling = false
                                    }
                                }
                            }
                    )
                }
            }
            .edgesIgnoringSafeArea(.horizontal)
        }
        .frame(height: 85) // 稍微增加高度，確保視圖能顯示完整
        .background(HexColor.themed(.primaryBackground))
    }
    
    // MARK: - 時間刻度視圖
    private func timelineTickView(item: TimelineItem, index: Int, isVisible: Bool) -> some View {
        VStack(spacing: 4) {
            // 日期標籤（僅在新的一天開始時顯示）
            if item.isNewDay {
                Text(item.dayLabel)
                    .font(.system(size: CGFloat(13).auto(), weight: .semibold, design: .rounded))
                                                .foregroundColor(HexColor.themed(.primaryText))
                    .opacity(isScrolling || index == selectedTimeIndex ? 1 : 0)
                    .padding(.bottom, 2)
            } else {
                Spacer()
                    .frame(height: 15)
            }
            
            // 刻度線
            Rectangle()
                .fill(item.isMajorTick ? 
                                          (index == selectedTimeIndex ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)) :
                    HexColor.themed(.disabled))
                .frame(width: item.isMajorTick ? (index == selectedTimeIndex ? 2 : 1.5) : 1, 
                       height: item.isMajorTick ? majorTickHeight : minorTickHeight)
                .opacity(item.showTick ? 1 : 0.7)
            
            // 時間標籤（每3小時顯示一次）- 使用ZStack擴大顯示區域
            if item.showTimeLabel {
                // 使用ZStack使標籤有更多空間
                ZStack {
                    Text(item.timeLabel)
                        .font(.system(size: CGFloat(13).auto(), weight: index == selectedTimeIndex ? .medium : .regular))
                        .foregroundColor(index == selectedTimeIndex ? 
                                                             HexColor.themed(.primaryText) :
                    HexColor.themed(.secondaryText))
                        .opacity(isVisible ? 1 : 0)
                        .animation(.easeInOut(duration: 0.2), value: isVisible)
                }
                .frame(width: hourWidth * 1.7) // 進一步增加標籤寬度
            } else {
                Spacer()
                    .frame(height: 16)
            }
        }
        .frame(width: hourWidth)
        .contentShape(Rectangle())
        .onTapGesture {
            if index != selectedTimeIndex {
                withAnimation(.easeInOut(duration: 0.25)) {
                    selectedTimeIndex = index
                }
            }
        }
        .background(
            index == selectedTimeIndex ? 
            RoundedRectangle(cornerRadius: 2)
                                    .fill(HexColor.themed(.separator).opacity(0.4))
                .frame(width: hourWidth * 0.9, height: majorTickHeight + 28) : 
            RoundedRectangle(cornerRadius: 0)
                .fill(Color.clear)
                .frame(width: 0, height: 0)
        )
    }
}

// MARK: - 預覽
struct TimelineView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            TimelineView(
                viewModel: WeatherViewModel(),
                selectedTimeIndex: .constant(0)
            )
            .previewLayout(.sizeThatFits)
            .frame(height: 85)
            
            Spacer()
        }
        .padding()
        .frame(height: 200)
        .background(HexColor.themed(.primaryBackground))
        .previewDisplayName("時間軸")
    }
} 
