//
//  ThemePickerView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

// MARK: - 主題選擇器模態框
struct ThemePickerView: View {
    @Binding var selectedTheme: ThemeMode
    @Environment(\.presentationMode) var presentationMode
    let onDismiss: () -> Void
    let onConfirm: () -> Void

    // 內部狀態來確保 UI 更新
    @State private var internalSelectedTheme: ThemeMode

    // IAP 相關
    @ObservedObject private var iapService = IAPService.shared
    @State private var showingUpgradeAlert = false
    @State private var showingPaywall = false

    init(selectedTheme: Binding<ThemeMode>, onDismiss: @escaping () -> Void, onConfirm: @escaping () -> Void) {
        self._selectedTheme = selectedTheme
        self.onDismiss = onDismiss
        self.onConfirm = onConfirm
        self._internalSelectedTheme = State(initialValue: selectedTheme.wrappedValue)
    }

    // MARK: - 計算屬性

    /// 檢查是否可以選擇主題（某些主題需要 Pro 訂閱）
    private var canSelectTheme: Bool {
        return iapService.isPro
    }

    /// 檢查特定主題是否需要 Pro 訂閱
    private func isProTheme(_ theme: ThemeMode) -> Bool {
        switch theme {
        case .light:
            return false  // 白天主題免費
        case .dark, .system:
            return true   // 夜晚主題和系統主題需要 Pro
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("theme_setting".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                
                // 系統偵測選擇按鈕
                Button(action: {
                    if canSelectTheme || !isProTheme(.system) {
                        // 有 Pro 訂閱或主題免費，可以選擇
                        withAnimation(.easeInOut(duration: 0.2)) {
                            internalSelectedTheme = .system
                        }
                        print("選擇系統偵測主題")
                    } else {
                        // 沒有 Pro 訂閱，顯示升級提示
                        showUpgradeAlert()
                    }
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: internalSelectedTheme == .system ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: internalSelectedTheme == .system ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(canSelectTheme || !isProTheme(.system) ? (internalSelectedTheme == .system ? 1.0 : 0.3) : 0.2)
                        .animation(.easeInOut(duration: 0.2), value: internalSelectedTheme)

                        Text("theme_system".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(internalSelectedTheme == .system ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(canSelectTheme || !isProTheme(.system) ? (internalSelectedTheme == .system ? 1.0 : 0.3) : 0.4)
                            .animation(.easeInOut(duration: 0.2), value: internalSelectedTheme)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
                
                // 白天主題選擇按鈕
                Button(action: {
                    if canSelectTheme || !isProTheme(.light) {
                        // 有 Pro 訂閱或主題免費，可以選擇
                        withAnimation(.easeInOut(duration: 0.2)) {
                            internalSelectedTheme = .light
                        }
                        print("選擇白天主題")
                    } else {
                        // 沒有 Pro 訂閱，顯示升級提示
                        showUpgradeAlert()
                    }
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: internalSelectedTheme == .light ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: internalSelectedTheme == .light ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(canSelectTheme || !isProTheme(.light) ? (internalSelectedTheme == .light ? 1.0 : 0.3) : 0.2)
                        .animation(.easeInOut(duration: 0.2), value: internalSelectedTheme)

                        Text("theme_light".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(internalSelectedTheme == .light ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(canSelectTheme || !isProTheme(.light) ? (internalSelectedTheme == .light ? 1.0 : 0.3) : 0.4)
                            .animation(.easeInOut(duration: 0.2), value: internalSelectedTheme)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
                
                // 夜晚主題選擇按鈕
                Button(action: {
                    if canSelectTheme || !isProTheme(.dark) {
                        // 有 Pro 訂閱或主題免費，可以選擇
                        withAnimation(.easeInOut(duration: 0.2)) {
                            internalSelectedTheme = .dark
                        }
                        print("選擇夜晚主題")
                    } else {
                        // 沒有 Pro 訂閱，顯示升級提示
                        showUpgradeAlert()
                    }
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: internalSelectedTheme == .dark ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: internalSelectedTheme == .dark ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(canSelectTheme || !isProTheme(.dark) ? (internalSelectedTheme == .dark ? 1.0 : 0.3) : 0.2)
                        .animation(.easeInOut(duration: 0.2), value: internalSelectedTheme)

                        Text("theme_dark".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(internalSelectedTheme == .dark ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(canSelectTheme || !isProTheme(.dark) ? (internalSelectedTheme == .dark ? 1.0 : 0.3) : 0.4)
                            .animation(.easeInOut(duration: 0.2), value: internalSelectedTheme)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                    
                    // 右側圓形勾選按鈕
                    Button(action: {
                        if canSelectTheme || !isProTheme(internalSelectedTheme) {
                            // 確認時才更新 selectedTheme
                            selectedTheme = internalSelectedTheme
                            onConfirm()
                        } else {
                            // 沒有 Pro 訂閱，顯示升級提示
                            showUpgradeAlert()
                        }
                    }) {
                        HStack(spacing: CGFloat(8).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                        }
                        .padding(.horizontal, CGFloat(2).auto())
                        .padding(.vertical, CGFloat(2).auto())
                        .background(
                            Circle()
                                .fill(HexColor.themed(.primaryText))
                        )
                    }
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
        .iapUpgradeAlert(isPresented: $showingUpgradeAlert, showingPaywall: $showingPaywall)
        .fullScreenCover(isPresented: $showingPaywall) {
            PaywallView()
        }
        .onChange(of: iapService.isPro) { isPro in
            // 當 IAP 狀態變化時，同步 UI 狀態
            if !isPro {
                // 降級到免費版，重置為免費主題
                if isProTheme(internalSelectedTheme) {
                    internalSelectedTheme = .light  // 重置為免費的白天主題
                    print("🔧 UI 狀態已同步：重置主題選擇為白天主題")
                }
            }
        }
    }

    // MARK: - 私有方法

    /// 顯示升級提示
    private func showUpgradeAlert() {
        showingUpgradeAlert = true
    }
}

// MARK: - 預覽
#Preview {
    ThemePickerView(
        selectedTheme: .constant(.light),
        onDismiss: {},
        onConfirm: {}
    )
} 