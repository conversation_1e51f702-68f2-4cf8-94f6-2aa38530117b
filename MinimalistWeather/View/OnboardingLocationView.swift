//
//  OnboardingLocationView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct OnboardingLocationView: View {
    let onNext: () -> Void
    let onPrevious: () -> Void
    
    // MARK: - State
    @State private var isSearchMode = false
    @State private var searchText = ""
    @State private var searchResults: [GoogleGeocoding.Result] = []
    @State private var selectedResult: GoogleGeocoding.Result?
    @State private var savedLocations: [SavedLocation] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    @FocusState private var isTextFieldFocused: Bool
    
    // MARK: - Services
    private let geocodingService = GoogleGeocodingService()
    private let locationRepository = LocationRepository()
    private let localizationManager = LocalizationManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("location".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
                
                if !isSearchMode && savedLocations.isEmpty {
                    // 新增地區按鈕 (初始狀態)
                    addLocationButton
                } else if isSearchMode {
                    // 搜尋模式
                    searchModeContent
                } else {
                    // 已儲存的地區清單
                    savedLocationsContent
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域 (搜尋模式時隱藏)
            if !isSearchMode {
                OnboardingBottomButtons(
                    onNext: savedLocations.isEmpty ? nil : onNext,
                    onPrevious: onPrevious,
                    nextButtonText: "continue".localized,
                    isNextDisabled: false,
                    shouldShowPurchaseFlow: false
                )
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .onAppear {
            loadSavedLocations()
        }
    }
    
    // MARK: - 新增地區按鈕
    private var addLocationButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                isSearchMode = true
                // 延遲一點讓動畫完成後再 focus
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isTextFieldFocused = true
                }
            }
        }) {
            HStack(spacing: CGFloat(0).auto()) {
                AppIconsSymbol.createView(for: AppIcons.search, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                
                Text("add_forecasts_city".localized)
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
            }
            .offset(x: CGFloat(-10).auto())
        }
    }
    
    // MARK: - 搜尋模式內容
    private var searchModeContent: some View {
        VStack(alignment: .leading, spacing: CGFloat(12).auto()) {
            
            // 搜尋輸入框
            HStack(spacing: CGFloat(0).auto()) {
                Button(action: performSearch) {
                    AppIconsSymbol.createView(for: AppIcons.search, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                }
                .disabled(searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

                TextField("search_city_name".localized, text: $searchText)
                    .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                    .focused($isTextFieldFocused)
                    .onSubmit {
                        performSearch()
                    }                
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        cancelSearch()
                    }
                }) {
                    AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(24).auto(), color: HexColor.themed(.primaryText))
                }
            }
            .offset(x: CGFloat(-10).auto())
            
            // 載入指示器
            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("searching".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                }
                .padding(.vertical, CGFloat(8).auto())
            }
            
            // 錯誤訊息
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.accent))
                    .padding(.vertical, CGFloat(8).auto())
            }
            
            // 搜尋結果清單
            if !searchResults.isEmpty {
                searchResultsList
            }
        }
    }
    
    // MARK: - 搜尋結果清單
    private var searchResultsList: some View {
        VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
            ForEach(searchResults) { result in
                searchResultRow(result)
            }
        }
    }
    
    // MARK: - 搜尋結果行
    private func searchResultRow(_ result: GoogleGeocoding.Result) -> some View {
        Button(action: {
            // 直接加入位置，不使用 radio box 選擇
            addLocationDirectly(result)
        }) {
            HStack(spacing: CGFloat(0).auto()) {
                AppIconsSymbol.createView(for: AppIcons.add, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                
                HStack(spacing: CGFloat(1).auto()) {
                    Text(result.extractCityName())
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                    
                    Text(result.formatted_address)
                        .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.themed(.secondaryText))
                        .lineLimit(1)
                }
                
                Spacer()
            }
            .offset(x: CGFloat(-10).auto())
            .padding(.vertical, CGFloat(2).auto())
        }
    }
    

    
    // MARK: - 已儲存的地區內容
    private var savedLocationsContent: some View {
        VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
            
            // 新增更多地區按鈕
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isSearchMode = true
                    // 延遲一點讓動畫完成後再 focus
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        isTextFieldFocused = true
                    }
                }
            }) {
                HStack(spacing: CGFloat(0).auto()) {
                    AppIconsSymbol.createView(for: AppIcons.add, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                    
                    Text("add_more_cities".localized)
                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                }
                .offset(x: CGFloat(-10).auto())
            }
            
            // 已儲存的地區清單
            ForEach(savedLocations) { location in
                savedLocationRow(location)
            }
        }
    }
    
    // MARK: - 已儲存地區行
    private func savedLocationRow(_ location: SavedLocation) -> some View {
        HStack(spacing: CGFloat(0).auto()) {
            Button(action: {
                removeLocation(location)
            }) {
                AppIconsSymbol.createView(for: AppIcons.clear, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
            }
            
            HStack(spacing: CGFloat(1).auto()) {
                Text(location.name)
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                
                Text(location.formattedAddress)
                    .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .lineLimit(1)
            }
            
            Spacer()
        }
        .offset(x: CGFloat(-10).auto())
        .padding(.vertical, CGFloat(2).auto())
    }
    
    // MARK: - 方法
    
    private func loadSavedLocations() {
        savedLocations = locationRepository.getAllSavedLocations()
    }
    
    private func performSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        print("開始搜尋: \(searchText)")
        isLoading = true
        errorMessage = nil
        selectedResult = nil
        
        geocodingService.searchLocations(searchText, language: localizationManager.currentLanguage) { result in
            DispatchQueue.main.async {
                isLoading = false
                
                switch result {
                case .success(let results):
                    print("搜尋成功，找到 \(results.count) 個結果")
                    searchResults = results
                    if results.isEmpty {
                        errorMessage = "找不到搜尋結果"
                    }
                case .failure(let error):
                    print("搜尋失敗: \(error.message)")
                    errorMessage = error.message
                    searchResults = []
                }
            }
        }
    }
    
    private func addLocationDirectly(_ result: GoogleGeocoding.Result) {
        let savedLocation = result.toSavedLocation()
        locationRepository.addSavedLocation(savedLocation)
        
        withAnimation(.easeInOut(duration: 0.3)) {
            loadSavedLocations()
            cancelSearch()
        }
    }
    
    private func cancelSearch() {
        isSearchMode = false
        searchText = ""
        searchResults = []
        selectedResult = nil
        errorMessage = nil
        isLoading = false
        isTextFieldFocused = false
    }
    
    private func removeLocation(_ location: SavedLocation) {
        withAnimation(.easeInOut(duration: 0.3)) {
            locationRepository.removeSavedLocation(location)
            loadSavedLocations()
        }
    }
}

#Preview {
    OnboardingLocationView(onNext: {}, onPrevious: {})
} 