//
//  TimeFormatPickerView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

// MARK: - 時間格式選擇器模態框
struct TimeFormatPickerView: View {
    @Binding var selectedTimeFormat: TimeFormat
    @Environment(\.presentationMode) var presentationMode
    let onDismiss: () -> Void
    let onConfirm: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("time_format_setting".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                
                // 12小時制選擇按鈕
                Button(action: {
                    selectedTimeFormat = .twelveHour
                    print("選擇12小時制")
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: selectedTimeFormat == .twelveHour ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: selectedTimeFormat == .twelveHour ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(selectedTimeFormat == .twelveHour ? 1.0 : 0.3)
                        
                        Text("12_hour_format".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(selectedTimeFormat == .twelveHour ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(selectedTimeFormat == .twelveHour ? 1.0 : 0.3)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
                
                // 24小時制選擇按鈕
                Button(action: {
                    selectedTimeFormat = .twentyFourHour
                    print("選擇24小時制")
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: selectedTimeFormat == .twentyFourHour ? AppIcons.radioboxcheck : AppIcons.radiobox,
                            fontSize: CGFloat(44).auto(),
                            color: selectedTimeFormat == .twentyFourHour ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(selectedTimeFormat == .twentyFourHour ? 1.0 : 0.3)
                        
                        Text("24_hour_format".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(selectedTimeFormat == .twentyFourHour ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(selectedTimeFormat == .twentyFourHour ? 1.0 : 0.3)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域 (搜尋模式時隱藏)
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                    
                    // 右側圓形勾選按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        HStack(spacing: CGFloat(8).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                        }
                        .padding(.horizontal, CGFloat(2).auto())
                        .padding(.vertical, CGFloat(2).auto())
                        .background(
                            Circle()
                                .fill(HexColor.themed(.primaryText))
                        )
                    }
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))

        // ZStack(alignment: .top) {
        //     // 主要內容
        //     VStack(alignment: .leading, spacing: 0) {
        //         // 自定義頂部工具欄
        //         HStack {
        //             // 關閉按鈕
        //             Button(action: {
        //                 print("關閉按鈕被點擊：確認時間格式 \(selectedTimeFormat.displayName)")
        //                 onConfirm()
        //             }) {
        //                 AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.color("222222"))
        //             }
        //         }
        //         .padding(.horizontal, CGFloat(40).auto())
        //         .padding(.top, CGFloat(40).auto())
        //         .padding(.bottom, 0)
                
        //         Spacer() // 頂部推力
                
        //         // 內容區域 - 時間格式選擇器
        //         VStack(spacing: CGFloat(30).auto()) {
        //             // 12小時制選擇按鈕
        //             Button(action: {
        //                 selectedTimeFormat = .twelveHour
        //                 print("選擇12小時制")
        //             }) {
        //                 HStack(spacing: CGFloat(15).auto()) {
        //                     // Radio button
        //                     AppIconsSymbol.createView(
        //                         for: selectedTimeFormat == .twelveHour ? AppIcons.language : AppIcons.language, // 暫時使用 language 圖示
        //                         fontSize: CGFloat(44).auto(),
        //                         color: selectedTimeFormat == .twelveHour ? HexColor.color("222222") : HexColor.color("888888")
        //                     )
        //                     .opacity(selectedTimeFormat == .twelveHour ? 1.0 : 0.3)
                            
        //                     Text("12_hour_format".localized)
        //                         .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
        //                         .foregroundColor(selectedTimeFormat == .twelveHour ? HexColor.color("222222") : HexColor.color("888888"))
        //                         .opacity(selectedTimeFormat == .twelveHour ? 1.0 : 0.3)
        //                 }
        //                 .frame(maxWidth: .infinity, alignment: .leading)
        //             }
                    
        //             // 24小時制選擇按鈕
        //             Button(action: {
        //                 selectedTimeFormat = .twentyFourHour
        //                 print("選擇24小時制")
        //             }) {
        //                 HStack(spacing: CGFloat(15).auto()) {
        //                     // Radio button
        //                     AppIconsSymbol.createView(
        //                         for: selectedTimeFormat == .twentyFourHour ? AppIcons.language : AppIcons.language, // 暫時使用 language 圖示
        //                         fontSize: CGFloat(44).auto(),
        //                         color: selectedTimeFormat == .twentyFourHour ? HexColor.color("222222") : HexColor.color("888888")
        //                     )
        //                     .opacity(selectedTimeFormat == .twentyFourHour ? 1.0 : 0.3)
                            
        //                     Text("24_hour_format".localized)
        //                         .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
        //                         .foregroundColor(selectedTimeFormat == .twentyFourHour ? HexColor.color("222222") : HexColor.color("888888"))
        //                         .opacity(selectedTimeFormat == .twentyFourHour ? 1.0 : 0.3)
        //                 }
        //                 .frame(maxWidth: .infinity, alignment: .leading)
        //             }
        //         }
        //         .frame(maxWidth: .infinity)
        //         .padding(.horizontal, CGFloat(60).auto())
        //         .padding(.bottom, CGFloat(120).auto())
                
        //         Spacer() // 底部推力
        //     }
        //     .frame(maxWidth: .infinity, maxHeight: .infinity)
        // }
        // .edgesIgnoringSafeArea(.all)
        // .background(HexColor.color("F9F9F9"))
    }
}

// MARK: - 預覽
#Preview {
    TimeFormatPickerView(
        selectedTimeFormat: .constant(.twentyFourHour),
        onDismiss: {},
        onConfirm: {}
    )
} 