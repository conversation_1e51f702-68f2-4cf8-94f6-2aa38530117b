//
//  OnboardingFeaturePlanView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch
import RevenueCat

// 功能項目枚舉
enum FeaturePlanItem: String, CaseIterable {
    case currentWeather = "current_weather_forecast"
    case hourly2Day = "3_hour_2_day_forecast"
    case oneLocation = "1_location_forecast"
    case hourly5Day = "3_hour_5_day_forecast"
    case twoLocations = "2_location_forecast"
    case detailedWeatherInfo = "detailed_weather_info"
    case customNightTheme = "custom_night_theme"
    case noAds = "no_ads"
    
    var isPremium: Bool {
        switch self {
        case .currentWeather, .hourly2Day, .oneLocation:
            return false
        case .hourly5Day, .twoLocations, .detailedWeatherInfo, .customNightTheme, .noAds:
            return true
        }
    }
}

struct OnboardingFeaturePlanView: View {
    let onNext: () -> Void
    let onPrevious: () -> Void
    
    // IAP 相關狀態
    @ObservedObject private var iapService = IAPService.shared
    @State private var selectedFeatures: Set<FeaturePlanItem> = []
    @State private var isYearly = false
    @State private var monthlyPackage: Package?
    @State private var yearlyPackage: Package?
    @State private var isPurchasing = false
    @State private var isRestoring = false
    @State private var isShowingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    // 計算屬性
    private var hasSelectedPremiumFeatures: Bool {
        selectedFeatures.contains { $0.isPremium }
    }
    
    private var hasSelectedAnyFeature: Bool {
        !selectedFeatures.isEmpty
    }
    
    private var nextButtonText: String {
        if !hasSelectedAnyFeature {
            return "continue".localized
        } else if hasSelectedPremiumFeatures {
            return "start_7_day_trial".localized
        } else {
            return "start_free".localized
        }
    }

    private var shouldShowPurchaseFlow: Bool {
        hasSelectedPremiumFeatures
    }
    
    private var selectedPackage: Package? {
        isYearly ? yearlyPackage : monthlyPackage
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("choose_your_plan".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 功能選項列表
            VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                ForEach(FeaturePlanItem.allCases, id: \.self) { feature in
                    FeatureOptionRow(
                        feature: feature,
                        isSelected: selectedFeatures.contains(feature),
                        onToggle: {
                            toggleFeature(feature)
                        }
                    )
                }
                
                // 如果選擇了付費功能，顯示分隔線和價格區域
                if hasSelectedPremiumFeatures {
                    // 分隔線
                    Rectangle()
                        .fill(HexColor.themed(.separator))
                        .frame(height: 1)
                        .padding(.vertical, CGFloat(10).auto())
                    
                    // 價格和恢復購買區域
                    HStack {
                        VStack(alignment: .trailing, spacing: CGFloat(2).auto()) {
                            // 恢復購買按鈕
                            Button {
                                restorePurchases()
                            } label: {
                                if isRestoring {
                                    HStack {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: HexColor.themed(.primaryText)))
                                        .scaleEffect(0.8)
                                    Text("restoring".localized)
                                        .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(HexColor.themed(.primaryText))
                                    }
                                } else {
                                    Text("restore".localized)
                                        .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(HexColor.themed(.primaryText))
                                }
                            }
                            .disabled(isRestoring)

                            Text("restore".localized)
                                .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                                .opacity(0)
                        }
                        
                        Spacer()

                        VStack(alignment: .trailing, spacing: CGFloat(2).auto()) {
                            // 價格顯示
                            if let package = selectedPackage {
                                Text("\(package.storeProduct.localizedPriceString)\(isYearly ? "per_year".localized : "per_month".localized)")
                                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.primaryText))
                            } else {
                                Text("loading".localized)
                                    .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }

                            // 月度/年度切換按鈕
                            Button {
                                withAnimation {
                                    isYearly.toggle()
                                }
                            } label: {
                                Text(isYearly ? "yearly_plan".localized : "monthly_plan".localized)
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                                    .padding(.top, CGFloat(2).auto())
                            }
                        }                        
                    }
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()

            // 底部按鈕區域
            OnboardingBottomButtons(
                onNext: {
                    handleNextAction()
                },
                onPrevious: onPrevious,
                nextButtonText: nextButtonText,
                isNextDisabled: !hasSelectedAnyFeature || isPurchasing,
                shouldShowPurchaseFlow: shouldShowPurchaseFlow
            )
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .onAppear {
            loadOfferings()
        }
        .alert(isPresented: $isShowingAlert) {
            Alert(title: Text(alertTitle), message: Text(alertMessage), dismissButton: .default(Text("ok".localized)))
        }
    }
    
    // MARK: - 方法
    
    private func toggleFeature(_ feature: FeaturePlanItem) {
        if selectedFeatures.contains(feature) {
            selectedFeatures.remove(feature)
        } else {
            selectedFeatures.insert(feature)
        }
    }
    
    private func handleNextAction() {
        if hasSelectedPremiumFeatures {
            // 需要購買訂閱
            guard let package = selectedPackage else {
                alertTitle = "error".localized
                alertMessage = "package_not_available".localized
                isShowingAlert = true
                return
            }
            purchasePackage(package)
        } else {
            // 免費方案，直接進入下一步
            onNext()
        }
    }
    
    // 載入商品信息
    private func loadOfferings() {
        iapService.getOfferings { offerings in
            if let current = offerings?.current {
                DispatchQueue.main.async {
                    self.monthlyPackage = current.monthly
                    self.yearlyPackage = current.annual
                }
            }
        }
    }
    
    // 處理購買
    private func purchasePackage(_ package: Package) {
        isPurchasing = true
        
        iapService.purchasePackage(package) { success, customerInfo, error in
            DispatchQueue.main.async {
                self.isPurchasing = false
                
                if success {
                    // 購買成功，進入下一步
                    self.iapService.updateCustomerInfo()
                    self.onNext()
                } else if let error = error {
                    // 如果是用戶取消，不顯示錯誤
                    if (error as NSError).domain == "SKErrorDomain" && (error as NSError).code == 2 {
                        print("用戶取消購買")
                    } else {
                        // 顯示錯誤訊息
                        self.alertTitle = "purchase_failed".localized
                        self.alertMessage = error.localizedDescription
                        self.isShowingAlert = true
                        print("購買失敗: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    // 恢復購買
    private func restorePurchases() {
        isRestoring = true
        
        Purchases.shared.restorePurchases { customerInfo, error in
            DispatchQueue.main.async {
                self.isRestoring = false
                
                if let error = error {
                    self.alertTitle = "restore_failed".localized
                    self.alertMessage = error.localizedDescription
                    self.isShowingAlert = true
                    print("恢復購買失敗: \(error.localizedDescription)")
                    return
                }
                
                guard let customerInfo = customerInfo else {
                    self.alertTitle = "error".localized
                    self.alertMessage = "cannot_get_user_information".localized
                    self.isShowingAlert = true
                    return
                }
                
                let isPro = customerInfo.entitlements["pro"]?.isActive == true
                
                if isPro {
                    self.alertTitle = "restore_success".localized
                    self.alertMessage = "purchase_restored".localized
                    print("恢復購買成功，entitlement 有效")
                    
                    self.iapService.updateCustomerInfo()
                    // 恢復成功後進入下一步
                    self.onNext()
                } else {
                    self.alertTitle = "no_restorable_items".localized
                    self.alertMessage = "no_restorable_items_message".localized
                    print("恢復完成，但沒有 entitlement")
                }
                
                self.isShowingAlert = true
            }
        }
    }
}

// MARK: - 功能選項行視圖
struct FeatureOptionRow: View {
    let feature: FeaturePlanItem
    let isSelected: Bool
    let onToggle: () -> Void
    
    var body: some View {
        Button(action: onToggle) {
            HStack(spacing: CGFloat(0).auto()) {
                // Checkbox
                AppIconsSymbol.createView(
                    for: isSelected ? AppIcons.checkboxcheck : AppIcons.checkbox,
                    fontSize: CGFloat(44).auto(),
                    color: isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                )
                
                Text(feature.rawValue.localized)
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(isSelected ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
            }
            .offset(x: CGFloat(-10).auto())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    OnboardingFeaturePlanView(onNext: {}, onPrevious: {})
} 