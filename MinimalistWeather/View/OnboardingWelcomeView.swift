//
//  OnboardingWelcomeView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct OnboardingWelcomeView: View {
    let onNext: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // Welcome 標題
            Text("welcome".localized)
                    .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                    .padding(.top, CGFloat(50).auto())

            AppIconsSymbol.createView(for: AppIcons.appicon, fontSize: CGFloat(44).auto(), color: HexColor.themed(.primaryText))
                    .padding(.bottom, CGFloat(20).auto())
            
            Spacer()
            
            // 底部按鈕區域
            OnboardingBottomButtons(
                onNext: onNext,
                onPrevious: nil,
                nextButtonText: "",
                isNextDisabled: false,
                shouldShowPurchaseFlow: false
            )
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
    }
}

#Preview {
    OnboardingWelcomeView(onNext: {})
} 