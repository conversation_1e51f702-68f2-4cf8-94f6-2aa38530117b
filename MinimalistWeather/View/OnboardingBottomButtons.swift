//
//  OnboardingBottomButtons.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct OnboardingBottomButtons: View {
    let onNext: (() -> Void)?
    let onPrevious: (() -> Void)?
    let nextButtonText: String
    let isNextDisabled: Bool
    let shouldShowPurchaseFlow: Bool
    
    // 計算是否應該禁用按鈕
    private var shouldDisableNext: Bool {
        onNext == nil || isNextDisabled
    }
    
    var body: some View {
        ZStack(alignment: .leading) {
            HStack {
                // 左側箭頭按鈕
                Button(action: {
                    if !shouldDisableNext {
                        onNext?()
                    }
                }) {
                    Text(nextButtonText)
                        .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(shouldDisableNext ? HexColor.themed(.disabled) : HexColor.themed(.primaryText))  
                    
                    // .padding(.horizontal, CGFloat(20).auto())
                    // .padding(.vertical, CGFloat(12).auto())
                    // .background(
                    //     RoundedRectangle(cornerRadius: CGFloat(22).auto())
                    //         .fill(HexColor.color("222222"))
                    // )
                }
                .disabled(shouldDisableNext)
                // Button(action: {
                //     onPrevious?()
                // }) {
                //     Text("←")
                //         .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                //         .foregroundColor(onPrevious != nil ? HexColor.color("222222") : HexColor.color("CCCCCC"))
                //         .frame(width: CGFloat(44).auto(), height: CGFloat(44).auto())
                //         .background(
                //             Circle()
                //                 .fill(onPrevious != nil ? HexColor.color("FFFFFF") : HexColor.color("F5F5F5"))
                //                 .shadow(color: HexColor.color("000000").opacity(0.1), radius: 2, x: 0, y: 1)
                //         )
                // }
                // .disabled(onPrevious == nil)
                
                Spacer()
                
                // 右側 Next 按鈕
                Button(action: {
                    if !shouldDisableNext {
                        onNext?()
                    }
                }) {
                    HStack(spacing: CGFloat(8).auto()) {
                        // Text(nextButtonText)
                        //     .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        //     .foregroundColor(HexColor.color("FFFFFF"))
                        
                        // Text("→")
                        //     .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                        //     .foregroundColor(HexColor.color("FFFFFF"))
                        AppIconsSymbol.createView(for: AppIcons.next, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                    }
                    .padding(.horizontal, CGFloat(2).auto())
                    .padding(.vertical, CGFloat(2).auto())
                    .background(
                        Circle()
                            .fill(shouldDisableNext ? HexColor.themed(.disabled) : HexColor.themed(.primaryText))
                    )
                }
                .disabled(shouldDisableNext)
            }
            // .padding(.horizontal, CGFloat(20).auto())
            // 只有在需要購買流程時才顯示協議文字
            if shouldShowPurchaseFlow {
                Text(buildAgreementText())
                    .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .multilineTextAlignment(.leading)
                    .frame(height: CGFloat(20).auto())
                    .offset(y: CGFloat(32).auto())
            }
        }
    }
}

private func buildAgreementText() -> AttributedString {
        var str = AttributedString("purchase_agreement".localized)

        if let termsRange = str.range(of: "terms".localized) {
            str[termsRange].foregroundColor = HexColor.themed(.secondaryText)
            str[termsRange].underlineStyle = .single
            str[termsRange].link = URL(string: "https://www.minlsm.com/terms")
        }

        if let policyRange = str.range(of: "privacy_policy".localized) {
            str[policyRange].foregroundColor = HexColor.themed(.secondaryText)
            str[policyRange].underlineStyle = .single
            str[policyRange].link = URL(string: "https://www.minlsm.com/privacy")
        }

        return str
    }

#Preview {
    VStack {
        OnboardingBottomButtons(
            onNext: {},
            onPrevious: nil,
            nextButtonText: "Next",
            isNextDisabled: false,
            shouldShowPurchaseFlow: false
        )
        
        OnboardingBottomButtons(
            onNext: nil,
            onPrevious: {},
            nextButtonText: "continue".localized,
            isNextDisabled: false,
            shouldShowPurchaseFlow: false
        )
        
        OnboardingBottomButtons(
            onNext: {},
            onPrevious: nil,
            nextButtonText: "Disabled",
            isNextDisabled: true,
            shouldShowPurchaseFlow: false
        )
    }
    .padding()
} 