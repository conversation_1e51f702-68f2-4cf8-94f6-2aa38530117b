//
//  IAPUpgradeAlert.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/20.
//

import SwiftUI

/// IAP 升級提示 Alert 的 ViewModifier
struct IAPUpgradeAlert: ViewModifier {
    @Binding var isPresented: Bo<PERSON>
    @Binding var showingPaywall: Bool
    
    func body(content: Content) -> some View {
        content
            .alert("please_complete_setup".localized, isPresented: $isPresented) {
                Button("go_to_setup".localized) {
                    showingPaywall = true
                }
                Button("cancel".localized, role: .cancel) { }
            } message: {
                Text("complete_setup_before_using".localized)
            }
    }
}

/// View 擴展，提供便捷的 IAP 升級提示方法
extension View {
    /// 添加 IAP 升級提示 Alert
    /// - Parameters:
    ///   - isPresented: 控制 Alert 顯示狀態的綁定
    ///   - showingPaywall: 控制 Paywall 顯示狀態的綁定
    /// - Returns: 添加了 IAP 升級提示的 View
    func iapUpgradeAlert(isPresented: Binding<Bool>, showingPaywall: Binding<Bool>) -> some View {
        self.modifier(IAPUpgradeAlert(isPresented: isPresented, showingPaywall: showingPaywall))
    }
}
