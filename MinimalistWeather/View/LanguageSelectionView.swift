//
//  LanguageSelectionView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct LanguageSelectionView: View {
    @EnvironmentObject var languageService: LanguageService
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("language_setting".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 語言選項列表
            VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                ForEach(LanguageService.Language.allCases, id: \.self) { language in
                    languageRow(language: language)
                    
                    if language != LanguageService.Language.allCases.last {
                        Divider()
                            .background(HexColor.themed(.separator))
                    }
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
    }
    
    private func languageRow(language: LanguageService.Language) -> some View {
        Button(action: {
            languageService.setLanguage(language)
        }) {
            HStack {
                VStack(alignment: .leading, spacing: CGFloat(4).auto()) {
                    Text(language.nativeDisplayName)
                        .font(.system(size: CGFloat(18).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(HexColor.themed(.primaryText))
                    
                    Text(language.displayName)
                        .font(.system(size: CGFloat(14).auto(), weight: .regular))
                        .foregroundColor(HexColor.themed(.secondaryText))
                }
                
                Spacer()
                
                if languageService.currentLanguage == language {
                    AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(20).auto(), color: HexColor.themed(.primaryText))
                }
            }
            .padding(.vertical, CGFloat(16).auto())
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    LanguageSelectionView()
        .environmentObject(LanguageService.shared)
} 