//
//  TemperatureUnitPickerView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

// MARK: - 溫度單位選擇器模態框
struct TemperatureUnitPickerView: View {
    @Binding var selectedUnit: TemperatureUnit
    @Environment(\.presentationMode) var presentationMode
    let onDismiss: () -> Void
    let onConfirm: () -> Void
    
    var body: some View {
         VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
            
            // 標題
            Text("temperature_unit_setting".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            // 主要內容區域
            VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
                
                // 攝氏溫度選擇按鈕
                Button(action: {
                    selectedUnit = .celsius
                    print("選擇攝氏溫度")
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: selectedUnit == .celsius ? AppIcons.radioboxcheck : AppIcons.radiobox, // 暫時使用 language 圖示
                            fontSize: CGFloat(44).auto(),
                            color: selectedUnit == .celsius ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(selectedUnit == .celsius ? 1.0 : 0.3)
                        
                        Text("celsius_with_symbol".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(selectedUnit == .celsius ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(selectedUnit == .celsius ? 1.0 : 0.3)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
                
                // 華氏溫度選擇按鈕
                Button(action: {
                    selectedUnit = .fahrenheit
                    print("選擇華氏溫度")
                }) {
                    HStack(spacing: CGFloat(0).auto()) {
                        // Radio button
                        AppIconsSymbol.createView(
                            for: selectedUnit == .fahrenheit ? AppIcons.radioboxcheck : AppIcons.radiobox, // 暫時使用 language 圖示
                            fontSize: CGFloat(44).auto(),
                            color: selectedUnit == .fahrenheit ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText)
                        )
                        .opacity(selectedUnit == .fahrenheit ? 1.0 : 0.3)
                        
                        Text("fahrenheit_with_symbol".localized)
                            .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(selectedUnit == .fahrenheit ? HexColor.themed(.primaryText) : HexColor.themed(.secondaryText))
                            .opacity(selectedUnit == .fahrenheit ? 1.0 : 0.3)
                    }
                    .offset(x: CGFloat(-10).auto())
                }
            }
            .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域 (搜尋模式時隱藏)
            HStack(spacing: CGFloat(20).auto()) {
                HStack {
                    // 左側文字按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        Text("close".localized)
                            .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.themed(.primaryText))
                    }
                    
                    Spacer()
                    
                    // 右側圓形勾選按鈕
                    Button(action: {
                        onConfirm()
                    }) {
                        HStack(spacing: CGFloat(8).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryBackground))
                        }
                        .padding(.horizontal, CGFloat(2).auto())
                        .padding(.vertical, CGFloat(2).auto())
                        .background(
                            Circle()
                                .fill(HexColor.themed(.primaryText))
                        )
                    }
                }
            }
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
        
        // ZStack(alignment: .top) {
        //     // 主要內容
        //     VStack(alignment: .leading, spacing: 0) {
        //         // 自定義頂部工具欄 - 取代系統toolbar
        //         HStack {
        //             // 關閉按鈕
        //             Button(action: {
        //                 // 先應用設置溫度單位
        //                 print("關閉按鈕被點擊：確認溫度單位 \(selectedUnit.displayName)")
        //                 onConfirm()
        //                 // 無需顯式呼叫 dismiss，因為 onConfirm 已經設置了 showingTemperatureUnitPicker = false
        //             }) {
        //                 AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.color("222222"))
        //             }
                    
        //             // Spacer()
        //         }
        //         .padding(.horizontal, CGFloat(40).auto())
        //         .padding(.top, CGFloat(40).auto()) // 這裡控制工具欄距離頂部的距離
        //         .padding(.bottom, 0)
                
        //         Spacer() // 頂部推力
                
        //         // 內容區域 - 使用兩個圖標並排作為溫度單位選擇器
        //         HStack(spacing: CGFloat(40).auto()) {
        //             // 攝氏溫度選擇按鈕
        //             Button(action: {
        //                 selectedUnit = .celsius
        //                 print("選擇攝氏溫度")
        //                 // onConfirm() // 直接應用選擇
        //             }) {
        //                 VStack {
        //                     AppIconsSymbol.createView(for: AppIcons.ldc, fontSize: CGFloat(70).auto(), color: HexColor.color("222222"))
        //                         .opacity(selectedUnit == .celsius ? 1.0 : 0.3)
        //                     // Text("°C")
        //                     //     .font(.system(size: 16, weight: .medium))
        //                     //     .opacity(selectedUnit == .celsius ? 1.0 : 0.3)
        //                     //     .padding(.top, 4)
        //                 }
        //             }
                    
        //             // 華氏溫度選擇按鈕
        //             Button(action: {
        //                 selectedUnit = .fahrenheit
        //                 print("選擇華氏溫度")
        //                 // onConfirm() // 直接應用選擇
        //             }) {
        //                 VStack {
        //                     AppIconsSymbol.createView(for: AppIcons.ldf, fontSize: CGFloat(70).auto(), color: HexColor.color("222222"))
        //                         .opacity(selectedUnit == .fahrenheit ? 1.0 : 0.3)
        //                     // Text("°F")
        //                     //     .font(.system(size: 16, weight: .medium))
        //                     //     .opacity(selectedUnit == .fahrenheit ? 1.0 : 0.3)
        //                     //     .padding(.top, 4)
        //                 }
        //             }
        //         }
        //         .frame(maxWidth: .infinity)
        //         .padding(.horizontal, CGFloat(60).auto())
        //         // .background(Color(.red))
        //         .padding(.bottom, CGFloat(120).auto())
                
        //         Spacer() // 底部推力
        //     }
        //     .frame(maxWidth: .infinity, maxHeight: .infinity) // 移除 alignment: .top 讓內容可以垂直居中
        // }
        // .edgesIgnoringSafeArea(.all) // 忽略安全區域以全螢幕顯示
        // .background(HexColor.color("F9F9F9"))
    }
}

// MARK: - 預覽
#Preview {
    TemperatureUnitPickerView(
        selectedUnit: .constant(.celsius),
        onDismiss: {},
        onConfirm: {}
    )
} 