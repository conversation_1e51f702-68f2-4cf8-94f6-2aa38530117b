//
//  LanguageTestView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct LanguageTestView: View {
    @EnvironmentObject var languageService: LanguageService
    
    var body: some View {
        VStack(spacing: CGFloat(20).auto()) {
            Text("Language Test")
                .font(.system(size: CGFloat(24).auto(), weight: .bold))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            Text("Current Language: \(languageService.currentLanguage.displayName)")
                .font(.system(size: CGFloat(16).auto()))
                .foregroundColor(HexColor.themed(.secondaryText))
            
            VStack(spacing: CGFloat(10).auto()) {
                Text("welcome".localized)
                Text("location".localized)
                Text("feedback".localized)
                Text("settings".localized)
            }
            .font(.system(size: CGFloat(18).auto()))
            .foregroundColor(HexColor.themed(.primaryText))
            
            VStack(spacing: CGFloat(10).auto()) {
                ForEach(LanguageService.Language.allCases, id: \.self) { language in
                    Button(action: {
                        languageService.setLanguage(language)
                    }) {
                        Text(language.nativeDisplayName)
                            .padding()
                            .background(languageService.currentLanguage == language ? HexColor.themed(.accent) : HexColor.themed(.separator))
                            .foregroundColor(languageService.currentLanguage == language ? HexColor.themed(.secondaryBackground) : HexColor.themed(.primaryText))
                            .cornerRadius(8)
                    }
                }
            }
            
            Spacer()
        }
        .padding()
        .background(HexColor.themed(.primaryBackground))
    }
}

#Preview {
    LanguageTestView()
        .environmentObject(LanguageService.shared)
} 