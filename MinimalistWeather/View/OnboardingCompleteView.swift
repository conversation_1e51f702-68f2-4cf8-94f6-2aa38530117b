//
//  OnboardingCompleteView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import AutoInch

struct OnboardingCompleteView: View {
    let onComplete: () -> Void
    let onPrevious: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(20).auto()) {

            // 完成標題
            Text("all_set".localized)
                .font(.system(size: CGFloat(24).auto(), weight: .light, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .padding(.top, CGFloat(50).auto())
            
            Text("ready_to_start".localized)
                .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
                .multilineTextAlignment(.leading)
                .padding(.top, CGFloat(12).auto())
            
            Spacer()
            
            // 底部按鈕區域
            OnboardingBottomButtons(
                onNext: onComplete,
                onPrevious: onPrevious,
                nextButtonText: "get_started".localized,
                isNextDisabled: false,
                shouldShowPurchaseFlow: false
            )
        }
        .padding(.horizontal, CGFloat(50).auto())
        .padding(.bottom, CGFloat(50).auto())
        .background(HexColor.themed(.primaryBackground))
    }
}

#Preview {
    OnboardingCompleteView(onComplete: {}, onPrevious: {})
} 