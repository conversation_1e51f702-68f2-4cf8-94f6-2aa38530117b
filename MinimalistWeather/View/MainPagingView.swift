//
//  MainPagingView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import Combine
import UIKit
import AutoInch

// 不需要導入 Utilities，它是應用內的文件夾，不是獨立模組

struct MainPagingView: View {
    // MARK: - 屬性
    @StateObject private var viewModel = WeatherViewModel()
    @EnvironmentObject private var networkMonitor: NetworkMonitor
    @EnvironmentObject private var whatsNewService: WhatsNewService
    @State private var isShowingLocationSearch: Bool = false
    
    // MARK: - 視圖
    var body: some View {
        VerticalPageView(viewModel: viewModel, isShowingLocationSearch: $isShowingLocationSearch)
            .background(HexColor.themed(.primaryBackground))
            .edgesIgnoringSafeArea(.all) // 使用 edgesIgnoringSafeArea 替代 ignoresSafeArea
            .onAppear {
                // 移除舊的載入邏輯，現在由 WeatherViewModel 的初始化處理
                // loadLastSelectedLocation()
            }
            .onChange(of: networkMonitor.isConnected) { isConnected in
                if isConnected {
                    // 當網絡恢復時，使用重試機制刷新當前天氣數據
                    viewModel.retryFetchingWeather()
                }
            }
            .fullScreenCover(isPresented: $isShowingLocationSearch) {
                LocationSearchView(viewModel: viewModel, scrollToTopAction: {
                    // 選擇位置後，發送通知而不是直接使用 proxy
                    NotificationCenter.default.post(name: Notification.Name("ScrollToPage1"), object: nil)
                })
            }
            .fullScreenCover(isPresented: $whatsNewService.shouldShowWhatsNew) {
                CustomWhatsNewView(showWhatsNew: $whatsNewService.shouldShowWhatsNew)
            }
    }
    
    // MARK: - 方法
    
    /// 載入上次選擇的地區 (已廢棄，由 WeatherViewModel 處理)
    private func loadLastSelectedLocation() {
        // 這個方法已經不再使用，位置載入現在由 WeatherViewModel 的初始化處理
        print("舊的 loadLastSelectedLocation 方法被調用，但已廢棄")
    }
}

// 垂直分頁視圖包裝器
struct VerticalPageView: UIViewControllerRepresentable {
    @ObservedObject var viewModel: WeatherViewModel
    @Binding var isShowingLocationSearch: Bool
    
    func makeUIViewController(context: Context) -> UIPageViewController {
        let pageViewController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .vertical) // 垂直方向導航
        
        // 移除 dataSource 以禁用手勢滑動
        // pageViewController.dataSource = context.coordinator
        pageViewController.delegate = context.coordinator
        
        // 保存 pageViewController 到 coordinator
        context.coordinator.currentPageViewController = pageViewController
        
        // 設置初始頁面
        if let firstVC = context.coordinator.viewControllers.first {
            pageViewController.setViewControllers([firstVC], direction: .forward, animated: false)
        }
        
        return pageViewController
    }
    
    func updateUIViewController(_ pageViewController: UIPageViewController, context: Context) {
        // 更新 coordinator 中的數據
        context.coordinator.parent = self
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIPageViewControllerDataSource, UIPageViewControllerDelegate {
        var parent: VerticalPageView
        var viewControllers: [UIViewController] = []
        weak var currentPageViewController: UIPageViewController?
        
        init(_ parent: VerticalPageView) {
            self.parent = parent
            super.init()
            
            // 初始化視圖控制器
            let weatherVC = UIHostingController(
                rootView: WeatherPageView(
                    viewModel: parent.viewModel, 
                    onNextPage: { [weak self] in
                        self?.navigateToNextPage()
                    }
                )
            )
            
            let settingsVC = UIHostingController(
                rootView: SettingsPageView(
                    viewModel: parent.viewModel,
                    scrollToTopAction: { [weak self] in
                        self?.navigateToPreviousPage()
                    }
                )
            )
            
            self.viewControllers = [weatherVC, settingsVC]
            
            // 監聽通知，用於頁面切換
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(scrollToPage1),
                name: Notification.Name("ScrollToPage1"),
                object: nil
            )
            
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(scrollToPage2),
                name: Notification.Name("ScrollToPage2"),
                object: nil
            )
        }
        
        deinit {
            NotificationCenter.default.removeObserver(self)
        }
        
        @objc func scrollToPage1() {
            if let firstVC = viewControllers.first {
                currentPageViewController?.setViewControllers([firstVC], direction: .reverse, animated: true)
            }
        }
        
        @objc func scrollToPage2() {
            if viewControllers.count > 1 {
                let secondVC = viewControllers[1]
                currentPageViewController?.setViewControllers([secondVC], direction: .forward, animated: true)
            }
        }
        
        // 編程方式導航到下一頁
        func navigateToNextPage() {
            guard let currentVC = currentPageViewController?.viewControllers?.first,
                  let currentIndex = viewControllers.firstIndex(of: currentVC),
                  currentIndex + 1 < viewControllers.count else {
                return
            }
            
            let nextVC = viewControllers[currentIndex + 1]
            currentPageViewController?.setViewControllers([nextVC], direction: .forward, animated: true)
        }
        
        // 編程方式導航到上一頁
        func navigateToPreviousPage() {
            guard let currentVC = currentPageViewController?.viewControllers?.first,
                  let currentIndex = viewControllers.firstIndex(of: currentVC),
                  currentIndex > 0 else {
                return
            }
            
            let prevVC = viewControllers[currentIndex - 1]
            currentPageViewController?.setViewControllers([prevVC], direction: .reverse, animated: true)
        }
        
        // UIPageViewControllerDataSource
        func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
            guard let index = viewControllers.firstIndex(of: viewController) else { return nil }
            let previousIndex = index - 1
            return previousIndex >= 0 ? viewControllers[previousIndex] : nil
        }
        
        func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
            guard let index = viewControllers.firstIndex(of: viewController) else { return nil }
            let nextIndex = index + 1
            return nextIndex < viewControllers.count ? viewControllers[nextIndex] : nil
        }
    }
}

// MARK: - 預覽
struct MainPagingView_Previews: PreviewProvider {
    static var previews: some View {
        MainPagingView()
    }
} 