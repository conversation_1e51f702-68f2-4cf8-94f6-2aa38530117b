import SwiftUI

struct RulerTestView: View {
    @Binding var selectedIndex: Int
    @State private var sliderValue: Double = 0.0 // 默認會被初始值覆蓋
    var forecastTimes: [String] // API的時間數據，格式如 "2023-10-25 12:00:00"
    var timeLabels: [String] // 直接接收計算好的時間標籤
    var timezone: String? // 當前選擇地區的時區，例如 "Asia/Taipei"
    var currentTime: Date? // 當前選擇地區的時間
    var onDragStarted: (() -> Void)? = nil // 開始拖動的回調
    var onDragEnded: (() -> Void)? = nil // 結束拖動的回調
    
    // 內部狀態
    @State private var isDragging: Bool = false
    @State private var lastSelectedIndex: Int = -1 // 追蹤上一次的選中索引
    
    // 觸覺反饋生成器
    private let selectionFeedback = UISelectionFeedbackGenerator()
    
    // 添加初始化器，方便使用
    init(selectedIndex: Binding<Int>, forecastTimes: [String] = [], timeLabels: [String] = [], timezone: String? = nil, currentTime: Date? = nil, onDragStarted: (() -> Void)? = nil, onDragEnded: (() -> Void)? = nil) {
        self._selectedIndex = selectedIndex
        self.forecastTimes = forecastTimes
        self.timeLabels = timeLabels
        self.timezone = timezone
        self.currentTime = currentTime
        self.onDragStarted = onDragStarted
        self.onDragEnded = onDragEnded
    }
    
    var body: some View {
        VStack(spacing: 0) {
            Spacer() // 添加頂部間距使時間軸垂直居中
            
            RulerSlider(
                minValue: 0.0,
                maxValue: forecastTimes.isEmpty ? 23.0 : Double(forecastTimes.count - 1), // 根據API數據動態設置最大值
                initialValue: sliderValue, // 使用 sliderValue 而不是 selectedIndex
                rulerWidth: UIScreen.main.bounds.width,
                rulerHeight: CGFloat(80).auto(), // 使用CGFloat和.auto()
                selectedTickColor: HexColor.themed(.primaryText),
                unselectedTickColor: HexColor.themed(.disabled),
                tickSpacing: CGFloat(38).auto(), // 使用CGFloat和.auto()
                valueFont: .system(size: CGFloat(13).auto()),
                onValueChange: { value in
                    let index = Int(value.rounded())
                    if index != selectedIndex && index >= 0 && index < forecastTimes.count {
                        // 檢查索引是否真的改變了
                        if index != lastSelectedIndex {
                            // 觸發觸覺反饋
                            selectionFeedback.selectionChanged()
                            print("觸覺反饋觸發: 從索引 \(lastSelectedIndex) 變更到 \(index)")
                            
                            // 更新追蹤的索引
                            lastSelectedIndex = index
                        }
                        
                        selectedIndex = index
                    }
                },
                onDragStart: {
                    isDragging = true
                    // 準備觸覺反饋生成器
                    selectionFeedback.prepare()
                    onDragStarted?()
                },
                onDragEnd: {
                    isDragging = false
                    onDragEnded?()
                },
                customLabels: timeLabels,
                showFixedIndicator: true,
                fixedIndicatorColor: HexColor.themed(.primaryText),
                fixedIndicatorWidth: CGFloat(5).auto(),
                fixedIndicatorHeight: CGFloat(20).auto(),
                showFixedLabel: false,
                scrollSensitivity: 1.0,
                enableSnapping: true,
                majorTickInterval: 3, // 每3個值一個主刻度，對應每3小時
                labelInterval: 3,     // 每3個值一個標籤（與主刻度對應）
                labelVerticalOffset: CGFloat(35).auto(),
                showBottomLabels: true,
                labelFont: .system(size: CGFloat(13).auto()),
                labelColor: HexColor.themed(.secondaryText),
                majorTickHeight: CGFloat(16).auto(),
                minorTickHeight: CGFloat(16).auto(),
                tickLineWidth: CGFloat(5).auto(),
                minorTicksPerInterval: 2 // 每個主刻度之間有2個次要刻度
            )
            .onAppear {
                print("RulerTestView: onAppear - 初始 forecastTimes 數量: \(forecastTimes.count)")
                
                // 初始化 slider 值
                sliderValue = Double(selectedIndex)
                lastSelectedIndex = selectedIndex
                
                // 準備觸覺反饋生成器
                selectionFeedback.prepare()
            }
            .onChange(of: selectedIndex) { newIndex in
                // 當外部改變 selectedIndex 時，同步更新 RulerSlider 的位置
                if newIndex != lastSelectedIndex && !isDragging {
                    // 只有在不是拖動狀態時才同步，避免拖動時的衝突
                    sliderValue = Double(newIndex)
                    lastSelectedIndex = newIndex
                    print("RulerTestView: selectedIndex 變更為 \(newIndex)，同步 sliderValue")
                }
            }
            
            Spacer() // 添加底部間距使時間軸垂直居中
        }
        .padding(0)
        .onChange(of: forecastTimes) { newTimes in
            // 當預報時間數據改變時，確保 selectedIndex 在有效範圍內
            print("RulerTestView: forecastTimes 已改變，新的數量: \(newTimes.count)")
            if selectedIndex >= newTimes.count && !newTimes.isEmpty {
                selectedIndex = 0
                sliderValue = 0.0
                lastSelectedIndex = 0
                print("RulerTestView: selectedIndex 超出範圍，重置為 0")
            }
        }
        .onChange(of: timezone) { _ in
            // 當時區改變時，數據已經通過 ViewModel 統一處理
        }
    }
    
    // 將API時間字符串轉換為日期對象
    private func convertTimeStringToDate(_ dateTimeString: String) -> Date? {
        return DateTimeFormatter.shared.parseAPITimeString(dateTimeString)
    }
    
    // 從API時間格式轉換為顯示格式，考慮時區因素
    private func formatDateTimeFromAPI(_ dateTimeString: String) -> String? {
        return DateTimeFormatter.shared.formatAPITimeForDisplay(dateTimeString, timezoneId: timezone, showDateIfNeeded: true)
    }
    
    // 格式化當地時間
    private func formatLocalTime(_ date: Date) -> String {
        return DateTimeFormatter.shared.formatTimeForRuler(date, timezoneId: timezone)
    }
    
    // 獲取當前天氣數據的時區
    private func currentWeatherTimezone() -> TimeZone? {
        guard let timezoneId = timezone else { return nil }
        return TimeZone(identifier: timezoneId)
    }
    
    // 將UTC時間轉換為當地時間
    private func convertToLocalTime(_ date: Date) -> Date {
        return DateTimeFormatter.shared.convertToTimezone(date, timezoneId: timezone)
    }
    
    // 格式化小時顯示
    private func formatHour(hour: Int) -> String {
        return DateTimeFormatter.shared.formatTimeString("\(hour):00")
    }
}

// A customizable ruler slider with tick marks, labels, and snapping behavior.
struct RulerSlider: View {
    // MARK: - Configurable Parameters
    var minValue: Double = 0.0
    var maxValue: Double = 100.0
    var initialValue: Double = 50.0
    var rulerWidth: CGFloat = 300.0
    var rulerHeight: CGFloat = 100.0

    var selectedTickColor: Color = HexColor.themed(.primaryText)
    var unselectedTickColor: Color = HexColor.themed(.disabled)
    var tickSpacing: CGFloat = 20.0
    var valueFont: Font = .system(size: 18)

    var onValueChange: ((Double) -> Void)? = nil
    var onDragStart: (() -> Void)? = nil
    var onDragEnd: (() -> Void)? = nil
    var customLabels: [String]? = nil

    var showFixedIndicator: Bool = true
    var fixedIndicatorColor: Color = HexColor.themed(.primaryText)
    var fixedIndicatorWidth: CGFloat = 2.0
    var fixedIndicatorHeight: CGFloat = 60.0

    var showFixedLabel: Bool = true
    var fixedLabelColor: Color = HexColor.themed(.primaryText)
    var scrollSensitivity: CGFloat = 0.5
    var enableSnapping: Bool = false

    var majorTickInterval: Int = 3
    var labelInterval: Int = 3
    var labelVerticalOffset: CGFloat = 25.0
    var showBottomLabels: Bool = true
    var labelFont: Font = .system(size: 12)
    var labelColor: Color = HexColor.themed(.primaryText)

    var majorTickHeight: CGFloat = 20.0
    var minorTickHeight: CGFloat = 10.0
    var tickLineWidth: CGFloat = 2.0

    var minorTicksPerInterval: Int = 2

    // MARK: - Internal State
    @State private var offset: CGFloat = 0.0
    @State private var dragStartOffset: CGFloat = 0.0
    @State private var currentValue: Double = 50.0
    @State private var isInitialized: Bool = false
    @State private var isShowingAllLabels: Bool = false
    @State private var isDragging: Bool = false // 追蹤是否正在拖動
    @State private var lastTapDate: Date? = nil // 追蹤上次點擊時間

    private var totalScrollableWidth: CGFloat {
        return CGFloat(maxValue - minValue) * tickSpacing
    }

    var body: some View {
        ZStack {
            // 背景畫布用於繪製刻度線
            Canvas { context, size in
                let centerY = size.height / 2
                
                // 計算主刻度間的總點數（主刻度+次要刻度）
                let totalTicksPerInterval = 1 + minorTicksPerInterval
                
                // 繪製主刻度線和次要刻度線
                for majorIndex in Int(minValue/Double(majorTickInterval))...Int(ceil(maxValue/Double(majorTickInterval))) {
                    // 計算主刻度的值和位置
                    let majorTickValue = Double(majorIndex * majorTickInterval)
                    if majorTickValue > maxValue { continue } // 如果超出範圍，跳過
                    
                    let majorTickXInContent = CGFloat(majorTickValue - minValue) * tickSpacing
                    let majorTickX = majorTickXInContent + offset
                    
                    // 主刻度線
                    if majorTickX >= 0 && majorTickX <= size.width {
                        // 判斷是否為已選中刻度
                        let isSelected = (majorTickX <= size.width / 2)
                        _ = isSelected ? selectedTickColor : unselectedTickColor
                        
                        var path = Path()
                        path.move(to: CGPoint(x: majorTickX, y: centerY - majorTickHeight / 2))
                        path.addLine(to: CGPoint(x: majorTickX, y: centerY + majorTickHeight / 2))
                        context.stroke(path, with: .color(HexColor.themed(.secondaryText)), style: StrokeStyle(lineWidth: tickLineWidth, lineCap: .round))
                    }
                    
                    // 次要刻度線
                    // 確保包括最後一個主刻度後的次要刻度（即使majorIndex是最後一個）
                    let isLastMajorTick = majorTickValue + Double(majorTickInterval) > maxValue
                    
                    // 決定要繪製多少個次要刻度
                    var minorTicksToRender = minorTicksPerInterval
                    
                    if isLastMajorTick {
                        // 計算最後一個間隔有多少個有效的次要刻度
                        let remainingValue = maxValue - majorTickValue
                        let valuePerMinorTick = Double(majorTickInterval) / Double(totalTicksPerInterval)
                        
                        // 計算可以容納多少個完整的次要刻度
                        let possibleTicks = Int(floor(remainingValue / valuePerMinorTick))
                        
                        // 確保至少為1，最大不超過minorTicksPerInterval
                        minorTicksToRender = max(0, min(possibleTicks, minorTicksPerInterval))
                    }
                    
                    // 確保 minorTicksToRender 不小於 1，避免範圍錯誤
                    if minorTicksToRender > 0 {
                        for minorIndex in 1...minorTicksToRender {
                            let minorTickValue = majorTickValue + Double(minorIndex) * Double(majorTickInterval) / Double(totalTicksPerInterval)
                            if minorTickValue <= maxValue {
                                let minorTickXInContent = CGFloat(minorTickValue - minValue) * tickSpacing
                                let minorTickX = minorTickXInContent + offset
                                
                                if minorTickX >= 0 && minorTickX <= size.width {
                                    // 判斷是否為已選中刻度
                                    let isSelected = (minorTickX <= size.width / 2)
                                    _ = isSelected ? selectedTickColor : unselectedTickColor
                                    
                                    var path = Path()
                                    path.move(to: CGPoint(x: minorTickX, y: centerY - minorTickHeight / 2))
                                    path.addLine(to: CGPoint(x: minorTickX, y: centerY + minorTickHeight / 2))
                                    context.stroke(path, with: .color(HexColor.themed(.separator)), style: StrokeStyle(lineWidth: tickLineWidth * 0.8, lineCap: .round))
                                }
                            }
                        }
                    }
                }
            }
            .frame(width: rulerWidth, height: rulerHeight)
            
            // 繪製標籤層
            ZStack {
                if let customLabels = customLabels, showBottomLabels {
                    ForEach(0..<customLabels.count, id: \.self) { i in
                        let tickValue = Double(i * majorTickInterval)
                        let tickXInContent = CGFloat(tickValue - minValue) * tickSpacing
                        let tickX = tickXInContent + offset
                        let centerX = rulerWidth / 2
                        
                        // 只顯示在可見範圍內的標籤
                        if tickX >= 0 && tickX <= rulerWidth {
                            // 計算標籤與中心的距離以決定透明度
                            let distanceFromCenter = abs(tickX - centerX)
                            let isNearCenter = distanceFromCenter < tickSpacing * 3
                            
                            // 只顯示靠近中心的標籤，或者在拖動狀態下顯示所有標籤
                            if isNearCenter || isShowingAllLabels {
                                Text(customLabels[i])
                                    .font(labelFont)
                                    .foregroundColor(tickX <= centerX ? selectedTickColor : unselectedTickColor)
                                    .opacity(isNearCenter ? 1.0 : 0.7)
                                    .position(x: tickX, y: rulerHeight/2 + labelVerticalOffset)
                            }
                        }
                    }
                }
            }
            // .clipped() // 裁剪超出範圍的標籤
            
            // 中央固定指示器
            if showFixedIndicator {
                VStack(spacing: 0) {
                    // 頂部三角形指示器
                    // Triangle()
                    //     .fill(fixedIndicatorColor)
                    //     .frame(width: 8, height: 4)
                    
                    // 中心垂直線
                    Rectangle()
                        .fill(fixedIndicatorColor)
                        .frame(width: fixedIndicatorWidth, height: fixedIndicatorHeight)
                        .clipShape(Capsule()) // 為中央指示器也添加圓角
                }
                .allowsHitTesting(false) // 允許點擊穿透到下層
            }
            
            // 顯示當前值標籤
            if showFixedLabel {
                VStack {
                    Text("\(String(format: "%.0f", currentValue))")
                        .font(valueFont)
                        .foregroundColor(fixedLabelColor)
                        .animation(.easeInOut, value: currentValue)
                }
                .frame(width: rulerWidth, height: 30, alignment: .top)
                .allowsHitTesting(false)
            }
            
            // 手勢處理層
                Color.clear
                .contentShape(Rectangle())
                .onTapGesture {
                    // 雙擊檢測
                    let now = Date()
                    if let lastTap = lastTapDate, now.timeIntervalSince(lastTap) < 0.3 {
                        // 雙擊：重置到 index 0
                        let resetValue = minValue
                        let newOffset = rulerWidth / 2 - CGFloat(resetValue - minValue) * tickSpacing

                        withAnimation(.easeOut(duration: 0.4)) {
                            currentValue = resetValue
                            offset = newOffset
                            dragStartOffset = newOffset
                        }

                        onValueChange?(resetValue)
                        print("雙擊檢測：重置 ruler 到 index 0")

                        // 重置點擊時間
                        lastTapDate = nil
                    } else {
                        // 單擊：記錄時間
                        lastTapDate = now
                    }
                }
                .gesture(
                    DragGesture(minimumDistance: 5)
                        .onChanged { gesture in
                            // 標記為拖動狀態，顯示所有標籤
                            isShowingAllLabels = true

                            // 更新偏移量
                            offset = dragStartOffset + gesture.translation.width * scrollSensitivity

                            // 限制滑動範圍
                            let maxOffset = rulerWidth / 2
                            let minOffset = rulerWidth / 2 - totalScrollableWidth
                            offset = min(max(offset, minOffset), maxOffset)

                            // 計算當前值
                            currentValue = minValue + (rulerWidth / 2 - offset) / tickSpacing
                            currentValue = max(minValue, min(maxValue, currentValue))

                            // 如果是首次拖動，調用拖動開始回調
                            if !isDragging {
                                isDragging = true
                                onDragStart?()
                            }

                            // 通知值變更
                            onValueChange?(currentValue)
                        }
                        .onEnded { _ in
                            // 拖動結束，如果啟用吸附則吸附到最近的刻度
                            if enableSnapping {
                                let snappedValue = round(currentValue)
                                let clampedValue = max(minValue, min(maxValue, snappedValue))

                                // 計算新的偏移量
                                let newOffset = rulerWidth / 2 - CGFloat(clampedValue - minValue) * tickSpacing

                                // 使用動畫過渡到吸附位置
                                withAnimation(.easeOut(duration: 0.3)) {
                                    offset = newOffset
                                    currentValue = clampedValue
                                }

                                onValueChange?(clampedValue)
                            }

                            // 更新拖動起始偏移量
                            dragStartOffset = offset

                            // 延遲隱藏非中心標籤
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                                withAnimation(.easeOut(duration: 0.3)) {
                                    isShowingAllLabels = false
                                }
                            }

                            // 調用拖動結束回調
                            isDragging = false
                            onDragEnd?()
                        }
                )
        }
        .onAppear {
            // 初始化當前值
            currentValue = max(minValue, min(maxValue, initialValue))
            
            // 計算初始偏移量
            offset = rulerWidth / 2 - CGFloat(currentValue - minValue) * tickSpacing
            dragStartOffset = offset
            
            isInitialized = true
            print("RulerSlider: onAppear - 初始化 currentValue: \(currentValue), offset: \(offset)")
        }
        .onChange(of: initialValue) { newValue in
            // 當 initialValue 變化時，更新 RulerSlider 的位置（但不在拖動時）
            if !isDragging {
                let clampedValue = max(minValue, min(maxValue, newValue))
                let newOffset = rulerWidth / 2 - CGFloat(clampedValue - minValue) * tickSpacing
                
                print("RulerSlider: initialValue 變更為 \(newValue)，更新位置")
                
                withAnimation(.easeOut(duration: 0.2)) {
                    currentValue = clampedValue
                    offset = newOffset
                    dragStartOffset = newOffset
                }
            } else {
                print("RulerSlider: initialValue 變更但正在拖動中，跳過更新")
            }
        }
    }
}

// 三角形形狀
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.closeSubpath()
        return path
    }
} 
