import SwiftUI
import Lottie

struct CustomLottieView: UIViewRepresentable {
    let animationName: String
    let targetColor: UIColor
    let isPlaying: Bool
    
    init(animationName: String, targetColor: UIColor, isPlaying: Bool = true) {
        self.animationName = animationName
        self.targetColor = targetColor
        self.isPlaying = isPlaying
    }
    
    func makeUIView(context: Context) -> LottieAnimationView {
        let animationView = LottieAnimationView(name: animationName)
        animationView.contentMode = .scaleAspectFit
        animationView.loopMode = .loop
        // animationView.animationSpeed = 0.5
        
        // 設置所有weatherColor圖層的顏色
        setWeatherColors(for: animationView)
        
        if isPlaying {
            animationView.play()
        } else {
            animationView.pause()
        }
        
        return animationView
    }
    
    func updateUIView(_ uiView: LottieAnimationView, context: Context) {
        // 簡單重新載入動畫
        uiView.animation = LottieAnimation.named(animationName)
        
        // 重新設置所有weatherColor圖層的顏色
        setWeatherColors(for: uiView)
        
        if isPlaying {
            uiView.play()
        } else {
            uiView.pause()
        }
    }
    
    private func setWeatherColors(for animationView: LottieAnimationView) {
        let colorProvider = ColorValueProvider(targetColor.lottieColorValue)
        
        // 使用通配符匹配所有weatherColor開頭的圖層
        animationView.setValueProvider(colorProvider, keypath: AnimationKeypath(keypath: "**.Color"))
    }
} 