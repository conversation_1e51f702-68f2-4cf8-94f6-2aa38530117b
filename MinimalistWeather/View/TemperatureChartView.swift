import SwiftUI
import AutoInch

struct TemperatureChartView: View {
    // MARK: - 屬性
    var temperaturePoints: [TemperaturePoint]
    @Binding var selectedIndex: Int
    var timezone: String? // 當前選擇地區的時區
    var onUserInteraction: (() -> Void)? = nil // 新增：用戶互動回調
    @State private var animate: Bool = false
    
    // 計算最高和最低溫度，用於設置圖表 Y 軸範圍
    private var maxTemperature: Double {
        let temps = temperaturePoints.map { $0.temperature }
        return (temps.max() ?? 0) + 1
    }
    
    private var minTemperature: Double {
        let temps = temperaturePoints.map { $0.temperature }
        return (temps.min() ?? 10) - 1
    }
    
    // 計算溫度範圍
    private var temperatureRange: Double {
        return maxTemperature - minTemperature
    }
    
    // MARK: - 視圖
    var body: some View {
        VStack(alignment: .leading, spacing: CGFloat(8).auto()) {
            // 自定義溫度圖表
            customTemperatureChart
        }
        .padding(.top, CGFloat(60).auto())
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animate = true
            }
        }
    }
    
    // 自定義溫度圖表
    private var customTemperatureChart: some View {
        GeometryReader { geometry in
            ZStack(alignment: .topLeading) {
                // 背景網格
                VStack(spacing: 0) {
                    ForEach(0..<4, id: \.self) { _ in
                        Spacer()
                        Divider()
                            .background(HexColor.themed(.primaryBackground))
                            .opacity(0.0)
                        Spacer()
                    }
                    Divider()
                        .background(HexColor.themed(.primaryBackground))
                        .opacity(0.0)
                }
                
                // 溫度線和點
                TemperatureLineShape(
                    temperaturePoints: temperaturePoints,
                    selectedIndex: selectedIndex,
                    minTemp: minTemperature,
                    maxTemp: maxTemperature,
                    animate: animate
                )
                .stroke(
                    LinearGradient(
                        colors: [HexColor.themed(.gradientStart), HexColor.themed(.gradientEnd)],
                        startPoint: .top,
                        endPoint: .bottom
                    ),
                    style: StrokeStyle(lineWidth: CGFloat(2).auto(), lineCap: .round, lineJoin: .round)
                )
                .padding(.leading, CGFloat(0).auto())
                
                // 選中點的標記
                if selectedIndex < temperaturePoints.count {
                    let temp = temperaturePoints[selectedIndex].temperature
                    let tempPercentage = (temp - minTemperature) / temperatureRange
                    let yPosition = geometry.size.height - (geometry.size.height * CGFloat(tempPercentage))
                    let xPosition = (CGFloat(selectedIndex) / CGFloat(max(1, temperaturePoints.count - 1))) * (geometry.size.width - CGFloat(0).auto())
                    
                    // 垂直虛線
                    Rectangle()
                        .fill(HexColor.themed(.disabled))
                        .frame(width: CGFloat(1).auto())
                        .frame(height: geometry.size.height)
                        .position(x: xPosition + CGFloat(0).auto(), y: geometry.size.height / 2)
                        .opacity(0.0)
                        .compositingGroup()
                        .opacity(animate ? 1 : 0)
                    
                    // 選中點
                    Circle()
                        .fill(HexColor.themed(.primaryText))
                        .frame(width: CGFloat(10).auto(), height: CGFloat(10).auto())
                        .position(x: xPosition + CGFloat(0).auto(), y: yPosition)
                        .opacity(animate ? 1 : 0)
                    
                    // 溫度氣泡提示
                    temperatureBubble
                        .position(x: min(max(xPosition + CGFloat(0).auto(), CGFloat(80).auto()), geometry.size.width - CGFloat(60).auto()), y: max(yPosition - CGFloat(40).auto(), CGFloat(0).auto()))
                        .opacity(animate ? 1 : 0)
                }
                
                // X軸時間刻度
                HStack(spacing: 0) {
                    ForEach(0..<min(6, temperaturePoints.count), id: \.self) { i in
                        let index = i * max(1, temperaturePoints.count / 6)
                        if index < temperaturePoints.count {
                            Text(formatTime(temperaturePoints[index].time))
                                .font(.system(size: CGFloat(10).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.secondaryText))
                                .frame(maxWidth: .infinity)
                        }
                    }
                }
                .padding(.leading, CGFloat(0).auto())
                .frame(height: CGFloat(20).auto())
                .position(x: geometry.size.width / 2, y: geometry.size.height + CGFloat(10).auto())
            }
            .contentShape(Rectangle())
            .gesture(
                DragGesture(minimumDistance: 5)
                    .onChanged { value in
                        onUserInteraction?() // 觸發用戶互動回調
                        
                        let availableWidth = geometry.size.width - CGFloat(0).auto()
                        let adjustedX = value.location.x - CGFloat(0).auto() // 減去左邊距
                        let percentage = adjustedX / availableWidth
                        let newIndex = Int(CGFloat(temperaturePoints.count) * percentage)
                        let safeIndex = max(0, min(temperaturePoints.count - 1, newIndex))
                        
                        if safeIndex != selectedIndex {
                            withAnimation(.easeOut(duration: 0.1)) {
                                selectedIndex = safeIndex
                            }
                        }
                    }
            )
        }
        .frame(height: CGFloat(180).auto())
    }
    
    // 溫度氣泡
    private var temperatureBubble: some View {
        let point = temperaturePoints[selectedIndex]
        
        return VStack(alignment: .center, spacing: CGFloat(2).auto()) {
            HStack {
                Text("°")
                    .font(.system(size: CGFloat(16).auto(), weight: .bold, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryBackground))
                    .opacity(0.0)
                Text("\(Int(point.temperature))")
                    .font(.system(size: CGFloat(24).auto(), weight: .bold, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                Text("°")
                    .font(.system(size: CGFloat(16).auto(), weight: .bold, design: .rounded))
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .offset(x: CGFloat(-4).auto())
            }
            
            Text(formatTime(point.time))
                .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
        }
        .padding(.horizontal, CGFloat(12).auto())
        .padding(.vertical, CGFloat(8).auto())
        .background {
            RoundedRectangle(cornerRadius: CGFloat(8).auto())
                .fill(HexColor.themed(.primaryBackground))
                .shadow(color: HexColor.themed(.shadow).opacity(0.1), radius: CGFloat(4).auto(), x: 0, y: CGFloat(2).auto())
        }
    }
    
    // 格式化時間顯示（基於 RulerTestView 的正確邏輯）
    private func formatTime(_ timeString: String) -> String {
        if timeString == "NOW" {
            return "now".localized
        }
        
        // 檢查時間格式
        if timeString.contains(" ") {
            // API 格式時間（如 "2023-10-25 12:00:00"），使用共用方法處理
            return DateTimeFormatter.shared.formatAPITimeForTimeline(timeString, timezoneId: timezone)
        } else if timeString.contains(":") && timeString.count <= 5 {
            // 簡單時間格式（如 "14:30"），直接格式化
            return DateTimeFormatter.shared.formatTimeString(timeString)
        } else {
            // 已經格式化的時間字串，直接返回
            return timeString
        }
    }
}

// 溫度線形狀
struct TemperatureLineShape: Shape {
    var temperaturePoints: [TemperaturePoint]
    var selectedIndex: Int
    var minTemp: Double
    var maxTemp: Double
    var animate: Bool
    
    // 動畫支持
    var animatableData: Double {
        get { animate ? 1.0 : 0.0 }
        set { }
    }
    
    func path(in rect: CGRect) -> Path {
        guard temperaturePoints.count > 1 else {
            return Path()
        }
        
        let temperatureRange = maxTemp - minTemp
        let width = rect.width
        let height = rect.height
        let stepX = width / CGFloat(max(1, temperaturePoints.count - 1))
        
        var path = Path()
        
        // 收集所有點
        var points: [CGPoint] = []
        for i in 0..<temperaturePoints.count {
            let point = temperaturePoints[i]
            let temperature = point.temperature
            
            // 計算溫度在圖表中的位置 (相對於min和max之間)
            let tempPercentage = (temperature - minTemp) / temperatureRange
            
            // 使用 animatableData 控制動畫
            let animatedPercentage = animate ? tempPercentage : 0
            let yPosition = height - (height * CGFloat(animatedPercentage))
            let xPosition = stepX * CGFloat(i)
            
            points.append(CGPoint(x: xPosition, y: yPosition))
        }
        
        // 確保至少有兩個點
        guard points.count >= 2 else {
            return path
        }
        
        // 移動到第一個點
        path.move(to: points[0])
        
        // 特殊處理：如果只有兩個點
        if points.count == 2 {
            path.addLine(to: points[1])
            return path
        }
        
        // 創建擴展點陣列以處理邊界情況
        var extendedPoints = points
        // 反射第一個點作為虛擬前點
        extendedPoints.insert(CGPoint(x: 2 * points[0].x - points[1].x, 
                                     y: 2 * points[0].y - points[1].y), at: 0)
        // 反射最後一個點作為虛擬後點
        extendedPoints.append(CGPoint(x: 2 * points.last!.x - points[points.count - 2].x, 
                                     y: 2 * points.last!.y - points[points.count - 2].y))
        
        // 使用增強的三階樣條曲線方法
        for i in 1..<points.count {
            let p0 = extendedPoints[i-1]  // 前一個點
            let p1 = extendedPoints[i]    // 當前點
            let p2 = extendedPoints[i+1]  // 下一個點
            let p3 = extendedPoints[i+2]  // 下下個點
            
            // 使用參數化的三階樣條曲線控制點計算
            let smoothness: CGFloat = 1.0  // 控制平滑度，越大越平滑
            
            // 分別計算x和y方向的控制點
            // 第一個控制點是當前點的切線方向
            let dx1 = (p2.x - p0.x) / (6 * (1 + smoothness))
            let dy1 = (p2.y - p0.y) / (6 * (1 + smoothness))
            
            // 第二個控制點是下一個點的切線方向
            let dx2 = (p3.x - p1.x) / (6 * (1 + smoothness))
            let dy2 = (p3.y - p1.y) / (6 * (1 + smoothness))
            
            let control1 = CGPoint(x: p1.x + dx1, y: p1.y + dy1)
            let control2 = CGPoint(x: p2.x - dx2, y: p2.y - dy2)
            
            path.addCurve(to: p2, control1: control1, control2: control2)
        }
        
        return path
    }
}

// MARK: - 預覽
struct TemperatureChartView_Previews: PreviewProvider {
    static var previews: some View {
        let mockData = [
            TemperaturePoint(time: "10:00", date: Date(), temperature: 25, iconCode: "01d"),
            TemperaturePoint(time: "13:00", date: Date().addingTimeInterval(3600 * 3), temperature: 28, iconCode: "02d"),
            TemperaturePoint(time: "16:00", date: Date().addingTimeInterval(3600 * 6), temperature: 26, iconCode: "03d"),
            TemperaturePoint(time: "19:00", date: Date().addingTimeInterval(3600 * 9), temperature: 22, iconCode: "04d")
        ]
        
        return TemperatureChartView(
            temperaturePoints: mockData,
            selectedIndex: .constant(1),
            timezone: nil,
            onUserInteraction: nil
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}

// 添加一個小的擴展，用於格式化溫度顯示
extension Double {
    var clean: String {
        return String(format: "%.0f", self)
    }
} 
 