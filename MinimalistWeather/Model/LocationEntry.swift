//
//  LocationEntry.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation

/// 儲存的位置條目模型
struct LocationEntry: Identifiable, Codable {
    /// 唯一識別符
    var id: UUID
    
    /// 位置名稱
    var locationName: String
    
    /// 上次查詢的溫度
    var lastTemperature: String
    
    /// 上次查詢的天氣狀況
    var lastCondition: String
    
    /// 上次更新時間
    var lastUpdated: Date
    
    /// 初始化
    init(id: UUID = UUID(), locationName: String, lastTemperature: String, lastCondition: String, lastUpdated: Date) {
        self.id = id
        self.locationName = locationName
        self.lastTemperature = lastTemperature
        self.lastCondition = lastCondition
        self.lastUpdated = lastUpdated
    }
} 