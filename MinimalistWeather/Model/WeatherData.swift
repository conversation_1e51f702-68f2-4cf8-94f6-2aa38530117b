//
//  WeatherData.swift
//  MinimalistWeather
//
//  Created by Ke<PERSON> on 2025/4/15.
//

import Foundation

/// 天氣數據模型
struct WeatherData: Codable {
    /// 位置名稱
    let location: String
    
    /// 溫度（攝氏度）
    let temperature: Int
    
    /// 天氣狀況（例如：晴、多雲、雨等）
    let condition: String
    
    /// 濕度百分比
    let humidity: Int
    
    /// 風速（公里/小時）
    let windSpeed: Double
    
    /// 降雨機率（百分比）
    let precipitationProbability: Int
    
    /// 體感溫度（攝氏度）
    let feelsLike: Int
    
    /// 天氣圖標代碼
    let iconCode: String
    
    /// 日出時間
    let sunrise: Date?
    
    /// 日落時間
    let sunset: Date?
    
    /// 未來天氣預報
    let forecast: [Forecast]
    
    /// 每三小時預報
    let hourlyForecast: [HourlyForecast]
    
    /// 時區偏移量（秒）
    let timezoneOffset: Int
    
    // 新增的詳細天氣數據
    /// 氣壓（hPa）
    let pressure: Double?
    
    /// 海平面氣壓（hPa）
    let seaLevelPressure: Double?
    
    /// 地面氣壓（hPa）
    let groundLevelPressure: Double?
    
    /// 風向（度）
    let windDirection: Double?
    
    /// 陣風速度（m/s）
    let windGust: Double?
    
    /// 雲量百分比
    let cloudiness: Int?
    
    /// 能見度（公里）
    let visibility: Double?
    
    /// 降雨量（毫米）
    let rainVolume: Double?
    
    /// 降雪量（毫米）
    let snowVolume: Double?

    /// 紫外線指數（0-11）
    let uvIndex: Int?

    /// 初始化
    init(location: String, temperature: Int, condition: String, humidity: Int, windSpeed: Double, precipitationProbability: Int, feelsLike: Int, iconCode: String, sunrise: Date?, sunset: Date?, forecast: [Forecast], hourlyForecast: [HourlyForecast] = [], timezoneOffset: Int = 0, pressure: Double? = nil, seaLevelPressure: Double? = nil, groundLevelPressure: Double? = nil, windDirection: Double? = nil, windGust: Double? = nil, cloudiness: Int? = nil, visibility: Double? = nil, rainVolume: Double? = nil, snowVolume: Double? = nil, uvIndex: Int? = nil) {
        self.location = location
        self.temperature = temperature
        self.condition = condition
        self.humidity = humidity
        self.windSpeed = windSpeed
        self.precipitationProbability = precipitationProbability
        self.feelsLike = feelsLike
        self.iconCode = iconCode
        self.sunrise = sunrise
        self.sunset = sunset
        self.forecast = forecast
        self.hourlyForecast = hourlyForecast
        self.timezoneOffset = timezoneOffset
        self.pressure = pressure
        self.seaLevelPressure = seaLevelPressure
        self.groundLevelPressure = groundLevelPressure
        self.windDirection = windDirection
        self.windGust = windGust
        self.cloudiness = cloudiness
        self.visibility = visibility
        self.rainVolume = rainVolume
        self.snowVolume = snowVolume
        self.uvIndex = uvIndex
    }
}

/// 天氣預報模型
struct Forecast: Codable {
    /// 日期
    let date: Date
    
    /// 最高溫度
    let highTemperature: Int
    
    /// 最低溫度
    let lowTemperature: Int
    
    /// 天氣狀況
    let condition: String
    
    /// 天氣圖標代碼
    let iconCode: String
    
    /// 降雨機率（百分比）
    let precipitationProbability: Int
    
    /// 濕度百分比
    let humidity: Int
}

/// 每三小時天氣預報模型
struct HourlyForecast: Codable, Identifiable {
    var id: Int { dt }
    
    /// 時間戳
    let dt: Int
    
    /// 時間
    let time: Date
    
    /// 溫度
    let temperature: Int
    
    /// 天氣狀況
    let condition: String
    
    /// 天氣圖標代碼
    let iconCode: String
    
    /// 降雨機率（百分比 0-100）
    let precipitationProbability: Int
    
    /// 濕度百分比
    let humidity: Int
    
    /// 體感溫度
    let feelsLike: Int?
    
    /// 氣壓（hPa）
    let pressure: Double?
    
    /// 海平面氣壓（hPa）
    let seaLevelPressure: Double?
    
    /// 地面氣壓（hPa）
    let groundLevelPressure: Double?
    
    /// 風速（m/s）
    let windSpeed: Double?
    
    /// 風向（度）
    let windDirection: Double?
    
    /// 陣風速度（m/s）
    let windGust: Double?
    
    /// 雲量百分比
    let cloudiness: Int?
    
    /// 能見度（公里）
    let visibility: Double?
    
    /// 降雨量（毫米）
    let rainVolume: Double?
    
    /// 降雪量（毫米）
    let snowVolume: Double?
    
    /// 日出時間（僅對當天有效）
    let sunrise: Date?
    
    /// 日落時間（僅對當天有效）
    let sunset: Date?

    /// 紫外線指數（0-11）
    let uvIndex: Int?

    /// 初始化
    init(dt: Int, time: Date, temperature: Int, condition: String, iconCode: String, precipitationProbability: Int, humidity: Int, feelsLike: Int? = nil, pressure: Double? = nil, seaLevelPressure: Double? = nil, groundLevelPressure: Double? = nil, windSpeed: Double? = nil, windDirection: Double? = nil, windGust: Double? = nil, cloudiness: Int? = nil, visibility: Double? = nil, rainVolume: Double? = nil, snowVolume: Double? = nil, sunrise: Date? = nil, sunset: Date? = nil, uvIndex: Int? = nil) {
        self.dt = dt
        self.time = time
        self.temperature = temperature
        self.condition = condition
        self.iconCode = iconCode
        self.precipitationProbability = precipitationProbability
        self.humidity = humidity
        self.feelsLike = feelsLike
        self.pressure = pressure
        self.seaLevelPressure = seaLevelPressure
        self.groundLevelPressure = groundLevelPressure
        self.windSpeed = windSpeed
        self.windDirection = windDirection
        self.windGust = windGust
        self.cloudiness = cloudiness
        self.visibility = visibility
        self.rainVolume = rainVolume
        self.snowVolume = snowVolume
        self.sunrise = sunrise
        self.sunset = sunset
        self.uvIndex = uvIndex
    }
} 