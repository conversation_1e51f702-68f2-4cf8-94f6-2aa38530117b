//
//  MainPageData.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/5/17.
//

import Foundation

// 溫度圖表數據點
struct TemperaturePoint: Identifiable, Hashable {
    var id = UUID()
    let time: String
    let date: Date
    let temperature: Double
    let iconCode: String
}

// 時間軸預覽數據
struct TimelineHourlyData: Identifiable, Hashable {
    var id = UUID()
    let time: String
    let date: Date
    let temperature: String
    let iconCode: String
    let precipitationProbability: String
    let humidity: String
    let lowHighTemp: String
} 