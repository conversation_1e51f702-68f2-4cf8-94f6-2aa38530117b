//
//  SavedLocation.swift
//  MinimalistWeather
//
//  Created by Ke<PERSON> on 2025/4/15.
//

import Foundation

/// 已儲存位置模型
struct SavedLocation: Identifiable, Codable, Hashable {
    /// 唯一識別符
    let id: UUID
    
    /// 位置名稱
    var name: String
    
    /// 完整地址
    var formattedAddress: String
    
    /// 緯度
    let lat: Double
    
    /// 經度
    let lon: Double
    
    /// 國家代碼
    let country: String
    
    /// 天氣來源（可選，用於向後相容）
    var weatherSource: WeatherSource?
    
    /// 時區ID（可選，用於向後相容）
    var timezone: String?
    
    /// 有效的天氣來源（計算屬性）
    var effectiveWeatherSource: WeatherSource {
        return weatherSource ?? WeatherSource.defaultSource(for: country)
    }
    
    /// 初始化
    init(id: UUID = UUID(), name: String, formattedAddress: String = "", lat: Double, lon: Double, country: String = "", weatherSource: WeatherSource? = nil, timezone: String? = nil) {
        self.id = id
        self.name = name
        self.formattedAddress = formattedAddress
        self.lat = lat
        self.lon = lon
        self.country = country
        self.weatherSource = weatherSource
        self.timezone = timezone
    }
    
    // MARK: - Codable 支援向後兼容
    
    /// 自定義解碼初始化，支援舊資料格式
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        lat = try container.decode(Double.self, forKey: .lat)
        lon = try container.decode(Double.self, forKey: .lon)
        country = try container.decode(String.self, forKey: .country)
        
        // formattedAddress 可能不存在於舊資料中，提供預設值
        if let address = try container.decodeIfPresent(String.self, forKey: .formattedAddress) {
            formattedAddress = address
        } else {
            // 如果沒有 formattedAddress，使用 name 和 country 組合
            formattedAddress = country.isEmpty ? name : "\(name), \(country)"
        }
        
        // weatherSource 可能不存在於舊資料中，支援資料遷移
        if let source = try container.decodeIfPresent(WeatherSource.self, forKey: .weatherSource) {
            weatherSource = source
        } else {
            // 舊資料沒有 weatherSource，根據國家代碼自動設定預設值
            weatherSource = WeatherSource.defaultSource(for: country)
        }
        
        // timezone 可能不存在於舊資料中，支援資料遷移
        timezone = try container.decodeIfPresent(String.self, forKey: .timezone)
    }
    
    /// 編碼鍵
    private enum CodingKeys: String, CodingKey {
        case id, name, formattedAddress, lat, lon, country, weatherSource, timezone
    }
    
    /// 從GeocodingResult創建
    static func from(result: GeocodingResult) -> SavedLocation {
        SavedLocation(
            name: result.name,
            formattedAddress: "\(result.name), \(result.country)", // 組合名稱和國家作為地址
            lat: result.lat,
            lon: result.lon,
            country: result.country,
            weatherSource: WeatherSource.defaultSource(for: result.country) // 自動設定預設天氣來源
        )
    }
} 