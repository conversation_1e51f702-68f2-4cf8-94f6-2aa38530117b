/* 
  Localizable.strings (Danish)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "Velkommen";
"terms_privacy" = "Betingelser og privatliv";
"privacy_policy" = "Privatlivspolitik";
"terms_of_service" = "Servicevilkår";
"accept" = "Accepter";
"location" = "Placeringer";
"add_forecasts_city" = "Tilføj en placering";
"searching" = "Søger...";
"confirm_add" = "Bekræft";
"add_more_cities" = "Tilføj flere placeringer";
"choose_your_plan" = "Vælg dit abonnement";
"feature_plans_available" = "Abonnementer vil være tilgængelige her";
"coming_soon" = "Kommer snart...";
"continue" = "Fortsæt";
"all_set" = "Alt er klar!";
"ready_to_start" = "Du er klar til at bruge Minimalist Weather";
"get_started" = "Kom i gang";

// MARK: - Main App
"no_connection" = "INGEN FORBINDELSE";
"check_internet_connection" = "Tjek venligst din internetforbindelse";
"feedback" = "Feedback";

// MARK: - Weather
"server_error" = "Forbindelsen fik timeout";
"send_feedback_check_announcements" = "Tryk på Genindlæs for at genoprette forbindelsen.";

// MARK: - Settings
"unit" = "ENHED";
"pro" = "Pro-indstillinger";
"language" = "Sprog";
"time_format" = "Tidsformat";
"12_hour_format" = "12-timers format";
"24_hour_format" = "24-timers format";

// MARK: - Location Search
"search_city_name" = "Indtast placering eller adresse";
"no_locations_matched" = "INGEN PLACERINGER FUNDET";
"search_for_a_city" = "SØG EFTER EN BY";
"no_saved_locations" = "INGEN GEMTE PLACERINGER";
"unlimited_locations_available" = "Ubegrænset antal placeringer tilgængeligt.";
"upgrade_to_pro_add_more" = "Opgrader til Pro for at tilføje flere placeringer.";

// MARK: - Paywall
"thanks_for_pro" = "Tak for din opgradering til Premium! Vi vil meget gerne høre dine idéer.";
"pro_description" = "Hvis du har spørgsmål eller forslag, så lad os det vide.";
"unlock_full_experience" = "Lås op for den fulde minimale oplevelse";
"5_day_forecast" = "5-døgnsprognose";
"multiple_locations" = "Flere placeringer";
"all_future_features" = "Alle fremtidige funktioner inkluderet";
"purchasing" = "Køber...";
"upgrade" = "Opgrader";
"restoring" = "Gendanner...";
"restore" = "Gendan";
"purchase_agreement" = "Betingelser | Privatliv";
"terms" = "Betingelser";
"loading" = "HENTER";

// MARK: - Common
"ok" = "OK";
"cancel" = "Annuller";
"search" = "Søg";
"close" = "Luk";
"add" = "Tilføj";
"delete" = "Slet";
"edit" = "Rediger";
"retry" = "Genindlæs";
"auto_retrying" = "Genindlæser...";

// MARK: - Time
"am" = "AM";
"pm" = "PM";
"now" = "NU";

// MARK: - Weekdays
"monday" = "MAN";
"tuesday" = "TIR";
"wednesday" = "ONS";
"thursday" = "TOR";
"friday" = "FRE";
"saturday" = "LØR";
"sunday" = "SØN";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - Feature Plans
"current_weather_forecast" = "Nuværende vejrudsigt";
"3_hour_2_day_forecast" = "48-timers prognose";
"1_location_forecast" = "1 placeringsprognose";
"3_hour_5_day_forecast" = "5-døgns vejrudsigt";
"2_location_forecast" = "50-byers prognose";
"detailed_weather_info" = "Detaljerede vejrdata";
"custom_night_theme" = "Brugerdefineret nattema";
"no_ads" = "Ingen reklamer";
"start_free" = "Start gratis";
"start_7_day_trial" = "Start 3 dages gratis prøveperiode";
"monthly_plan" = "Månedligt";
"yearly_plan" = "Årligt -50 %";

// MARK: - Alerts
"no_connection_alert" = "Ingen forbindelse";
"connect_internet_message" = "Opret forbindelse til internettet for vejropdateringer.";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "Opgrader til Pro";
"upgrade_now" = "Opgrader nu";
"upgrade_message_multiple_locations" = "Opgrader til Pro for at tilføje flere placeringer og funktioner.";

// MARK: - Setup Completion Alert
"please_complete_setup" = "Gennemfør venligst opsætningen";
"complete_setup_before_using" = "Du skal gennemføre opsætningen for at bruge denne funktion.";
"go_to_setup" = "Gå til opsætning";

// MARK: - Subscription Period
"per_year" = "/år";
"per_month" = "/md.";

// MARK: - Settings Menu
"about" = "OM";

// MARK: - Sunrise Sunset
"sunrise" = "Solopgang";
"sunset" = "Solnedgang";
"sunset_sunrise" = "Solopgang og solnedgang";

// MARK: - Paywall Continue
"continue_using" = "Fortsæt med at bruge";

// MARK: - Google Geocoding Errors
"network_error" = "Netværksfejl";
"invalid_response" = "Ugyldigt svar";
"api_error" = "Prøv venligst en anden eller mere specifik placering";
"decoding_error" = "Kunne ikke læse data";
"no_search_results" = "Ingen søgeresultater fundet";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "Temperaturenhed";
"time_format_setting" = "Tidsformat";
"theme_setting" = "Temaindstilling";
"weather_source_setting" = "Vejrleverandør";

// MARK: - Theme Settings
"theme_system" = "System";
"theme_light" = "Lyst tema";
"theme_dark" = "Mørkt tema";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "Celsius °C";
"fahrenheit_with_symbol" = "Fahrenheit °F";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "Central Weather Administration";

// MARK: - Widget Settings
"widget_settings" = "Widgetindstillinger";

// MARK: - What's New
"whats_new" = "Hvad er nyt";
"show_whats_new" = "Vis nyheder";
"release_notes" = "Udgivelsesnoter";
"version" = "Version";
"no_updates_available" = "Ingen opdateringer tilgængelige";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "Køb mislykkedes";
"subscription_success" = "Abonnement oprettet";
"thank_you_for_subscribing" = "Tak for dit abonnement!";
"error" = "Fejl";
"package_not_available" = "Pakke ikke tilgængelig";
"cannot_get_user_information" = "Kan ikke hente brugeroplysninger";
"restore_failed" = "Gendannelse mislykkedes";
"restore_success" = "Gendannelse lykkedes";
"purchase_restored" = "Dit køb er blevet gendannet";
"no_restorable_items" = "Ingen elementer at gendanne";
"no_restorable_items_message" = "Vi kunne ikke finde nogen tidligere køb at gendanne.";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "Lås op for den fulde prognose";
"paywall_forecasts_120hr_subtitle" = "Langtidsprognoser & detaljerede data.";
"paywall_saved_50_locations_title" = "50 gemte placeringer";
"paywall_saved_50_locations_subtitle" = "Gratis version er begrænset til 1 placering.";
"paywall_home_widget_title" = "Hjem-widget";
"paywall_home_widget_subtitle" = "Ikke tilgængelig i gratis version.";
"paywall_night_theme_title" = "Mørkt tema";
"paywall_night_theme_subtitle" = "Mørkt tema er en premium-funktion.";
"paywall_switch_provider_title" = "Skift leverandør";
"paywall_switch_provider_subtitle" = "Ikke tilgængelig i gratis version.";

// Unknown weather
"weather_unknown" = "Ukendt vejr";

// MARK: - Measurement System
"metric_system" = "Metrisk";
"imperial_system" = "Imperial";
"measurement_system_setting" = "Målesystem";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "Føles som";
"humidity" = "Fugtighed";
"precipitation_probability" = "Nedbør";
"cloudiness" = "Skydække";
"uv_index" = "UV-indeks";
"wind_speed" = "Vindhastighed";
"wind_gust" = "Vindstød";
"visibility" = "Sigtbarhed";
"sea_pressure" = "Havtryk";
"ground_pressure" = "Jordtryk";
"rain_volume" = "Regn";
"snow_volume" = "Sne";

// MARK: - Weather Providers
"weather_providers_info" = "Leverandørinfo";
"more_providers_info" = "Mere leverandørinfo →";
"different_weather_models_info" = "Forskellige vejrmodeller tilbyder forskellige prognosetider.";
"weather_provider_apple" = "Apple Vejr";
"weather_provider_apple_subtitle" = "Understøtter alle lande\n120-timers prognose";
"weather_provider_cwa" = "Central Weather Administration";
"weather_provider_cwa_subtitle" = "Understøtter kun Taiwan\n72-timers prognose";
"weather_provider_google" = "Google Vejr";
"weather_provider_google_subtitle" = "Understøtter ikke Japan eller Korea\n120-timers prognose";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "Understøtter alle lande\n120-timers prognose";

// MARK: - Weather Sources
"weather_source_jma" = "Japan Meteorological Agency";
"weather_source_eccc" = "Environment and Climate Change Canada";
"weather_source_dwd" = "Deutscher Wetterdienst";
"weather_source_nws_noaa" = "National Weather Service";
"weather_source_metoffice_ecmwf" = "The Met Office/European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_weather_com" = "Weather";
"weather_source_cwb" = "Weather Forecast Center";
"weather_source_environment_canada" = "Environment Canada";
"weather_source_eumetnet" = "EUMETNET";
"weather_source_ecmwf" = "European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_noaa" = "National Oceanic and Atmospheric Administration (NOAA)";
"weather_source_metoffice" = "Met Office";
"weather_source_gem_cmc" = "GEM (CMC, Canadian Meteorological Centre)";
