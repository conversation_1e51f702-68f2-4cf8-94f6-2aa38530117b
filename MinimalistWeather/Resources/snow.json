{"assets": [{"id": "3", "layers": [{"ind": 2, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "el", "p": {"a": 0, "k": [2, 2]}, "s": {"a": 0, "k": [4, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}]}, {"id": "7", "layers": [{"ind": 6, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "el", "p": {"a": 0, "k": [2, 2]}, "s": {"a": 0, "k": [4, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}]}, {"id": "11", "layers": [{"ind": 10, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "el", "p": {"a": 0, "k": [2, 2]}, "s": {"a": 0, "k": [4, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}]}, {"id": "17", "layers": [{"ind": 16, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [11, 2]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [22, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, -0.83], [-0.83, 0], [0, 0], [0, 0.83], [0.83, 0]], "o": [[0, 0], [-0.83, 0], [0, 0.83], [0, 0], [0.83, 0], [0, -0.83], [0, 0]], "v": [[15, 0], [1.5, 0], [0, 1.5], [1.5, 3], [15, 3], [16.5, 1.5], [15, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "22", "layers": [{"ind": 21, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.72, 0.41], [0.41, -0.71], [0, 0], [-0.72, -0.41], [-0.42, 0.72]], "o": [[0, 0], [0.41, -0.71], [-0.72, -0.41], [0, 0], [-0.41, 0.72], [0.72, 0.41], [0, 0]], "v": [[3.18, 14.09], [9.93, 2.4], [9.38, 0.36], [7.32, 0.9], [0.57, 12.59], [1.13, 14.64], [3.18, 14.09]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "27", "layers": [{"ind": 26, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.72, 0.41], [0.41, -0.71], [0, 0], [-0.72, -0.41], [-0.42, 0.72]], "o": [[0, 0], [0.41, -0.71], [-0.72, -0.41], [0, 0], [-0.41, 0.72], [0.72, 0.41], [0, 0]], "v": [[3.18, 14.09], [9.93, 2.4], [9.38, 0.36], [7.32, 0.9], [0.57, 12.59], [1.13, 14.64], [3.18, 14.09]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "32", "layers": [{"ind": 31, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.72, 0.41], [0.41, -0.71], [0, 0], [-0.72, -0.41], [-0.42, 0.72]], "o": [[0, 0], [0.41, -0.71], [-0.72, -0.41], [0, 0], [-0.41, 0.72], [0.72, 0.41], [0, 0]], "v": [[3.18, 14.09], [9.93, 2.4], [9.38, 0.36], [7.32, 0.9], [0.57, 12.59], [1.13, 14.64], [3.18, 14.09]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "37", "layers": [{"ind": 36, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.72, 0.41], [0.41, -0.71], [0, 0], [-0.72, -0.41], [-0.42, 0.72]], "o": [[0, 0], [0.41, -0.71], [-0.72, -0.41], [0, 0], [-0.41, 0.72], [0.72, 0.41], [0, 0]], "v": [[3.18, 14.09], [9.93, 2.4], [9.38, 0.36], [7.32, 0.9], [0.57, 12.59], [1.13, 14.64], [3.18, 14.09]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "42", "layers": [{"ind": 41, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.72, 0.41], [0.41, 0.71], [0, 0], [0.72, -0.41], [-0.41, -0.72]], "o": [[0, 0], [0.42, 0.71], [0.72, -0.41], [0, 0], [-0.41, -0.72], [-0.72, 0.41], [0, 0]], "v": [[0.57, 2.41], [7.32, 14.1], [9.38, 14.64], [9.93, 12.6], [3.18, 0.91], [1.13, 0.36], [0.57, 2.41]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "47", "layers": [{"ind": 46, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.72, 0.41], [0.41, 0.71], [0, 0], [0.72, -0.41], [-0.41, -0.72]], "o": [[0, 0], [0.42, 0.71], [0.72, -0.41], [0, 0], [-0.41, -0.72], [-0.72, 0.41], [0, 0]], "v": [[0.57, 2.41], [7.32, 14.1], [9.38, 14.64], [9.93, 12.6], [3.18, 0.91], [1.13, 0.36], [0.57, 2.41]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "52", "layers": [{"ind": 51, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.72, 0.41], [0.41, 0.71], [0, 0], [0.72, -0.41], [-0.41, -0.72]], "o": [[0, 0], [0.42, 0.71], [0.72, -0.41], [0, 0], [-0.41, -0.72], [-0.72, 0.41], [0, 0]], "v": [[0.57, 2.41], [7.32, 14.1], [9.38, 14.64], [9.93, 12.6], [3.18, 0.91], [1.13, 0.36], [0.57, 2.41]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "57", "layers": [{"ind": 56, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [7, 10]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [14, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.72, 0.41], [0.41, 0.71], [0, 0], [0.72, -0.41], [-0.41, -0.72]], "o": [[0, 0], [0.42, 0.71], [0.72, -0.41], [0, 0], [-0.41, -0.72], [-0.72, 0.41], [0, 0]], "v": [[0.57, 2.41], [7.32, 14.1], [9.38, 14.64], [9.93, 12.6], [3.18, 0.91], [1.13, 0.36], [0.57, 2.41]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "62", "layers": [{"ind": 61, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [11, 2]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [22, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, -0.83], [-0.83, 0], [0, 0], [0, 0.83], [0.83, 0]], "o": [[0, 0], [-0.83, 0], [0, 0.83], [0, 0], [0.83, 0], [0, -0.83], [0, 0]], "v": [[15, 0], [1.5, 0], [0, 1.5], [1.5, 3], [15, 3], [16.5, 1.5], [15, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "67", "layers": [{"ind": 66, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [11, 2]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [22, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, -0.83], [-0.83, 0], [0, 0], [0, 0.83], [0.83, 0]], "o": [[0, 0], [-0.83, 0], [0, 0.83], [0, 0], [0.83, 0], [0, -0.83], [0, 0]], "v": [[15, 0], [1.5, 0], [0, 1.5], [1.5, 3], [15, 3], [16.5, 1.5], [15, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "72", "layers": [{"ind": 71, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [11, 2]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [22, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, -0.83], [-0.83, 0], [0, 0], [0, 0.83], [0.83, 0]], "o": [[0, 0], [-0.83, 0], [0, 0.83], [0, 0], [0.83, 0], [0, -0.83], [0, 0]], "v": [[15, 0], [1.5, 0], [0, 1.5], [1.5, 3], [15, 3], [16.5, 1.5], [15, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "77", "layers": [{"ind": 76, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [32, 54]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [64, 108]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.72, 0.41], [0.41, -0.72], [0, 0], [-0.72, -0.41], [-0.42, 0.72]], "o": [[0, 0], [0.41, -0.72], [-0.72, -0.41], [0, 0], [-0.41, 0.72], [0.72, 0.41], [0, 0]], "v": [[3.18, 79.57], [47.43, 2.93], [46.88, 0.88], [44.82, 1.43], [0.57, 78.07], [1.13, 80.12], [3.18, 79.57]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "82", "layers": [{"ind": 81, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [32, 54]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [64, 108]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0.72, -0.41], [-0.41, -0.72], [0, 0], [-0.72, 0.41], [0.41, 0.72]], "o": [[0, 0], [-0.42, -0.72], [-0.72, 0.41], [0, 0], [0.41, 0.72], [0.72, -0.41], [0, 0]], "v": [[47.43, 78.07], [3.18, 1.43], [1.13, 0.88], [0.57, 2.93], [44.82, 79.57], [46.88, 80.12], [47.43, 78.07]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "87", "layers": [{"ind": 86, "ty": 4, "ks": {}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [61, 2]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [122, 4]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 151, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, -0.83], [-0.83, 0], [0, 0], [0, 0.83], [0.83, 0]], "o": [[0, 0], [-0.83, 0], [0, 0.83], [0, 0], [0.83, 0], [0, -0.83], [0, 0]], "v": [[90, 0], [1.5, 0], [0, 1.5], [1.5, 3], [90, 3], [91.5, 1.5], [90, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}], "fr": 60, "h": 160, "ip": 0, "layers": [{"ind": 5, "ty": 0, "parent": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 25.2, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 51, "s": [100], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 111, "s": [0], "h": 1}, {"t": 150, "s": [0], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [78, 34], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 25.2, "s": [78, 34], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 51, "s": [107, 73], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 111, "s": [94, 135], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 150, "s": [94, 135], "h": 1}]}}, "w": 4, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "3"}, {"ind": 9, "ty": 0, "parent": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 19.2, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 45, "s": [100], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 105, "s": [0], "h": 1}, {"t": 150, "s": [0], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [58, 34], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 19.2, "s": [58, 34], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 45, "s": [86, 81], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 105, "s": [73, 115], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 150, "s": [73, 115], "h": 1}]}}, "w": 4, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "7"}, {"ind": 13, "ty": 0, "parent": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 8.4, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 34.2, "s": [100], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 94.2, "s": [0], "h": 1}, {"t": 150, "s": [0], "h": 1}]}, "p": {"a": 1, "k": [{"t": 0, "s": [38, 34], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 8.4, "s": [38, 34], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 34.2, "s": [66, 71], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 94.2, "s": [33, 115], "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"t": 150, "s": [33, 115], "h": 1}]}}, "w": 4, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "11"}, {"ind": 19, "ty": 0, "parent": 15, "ks": {}, "w": 22, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "17"}, {"ind": 15, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [81, 88]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 24, "ty": 0, "parent": 20, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "22"}, {"ind": 20, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [38, 0]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 29, "ty": 0, "parent": 25, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "27"}, {"ind": 25, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [9, 53]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 34, "ty": 0, "parent": 30, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "32"}, {"ind": 30, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [70, 88]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 39, "ty": 0, "parent": 35, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "37"}, {"ind": 35, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [100, 35]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 44, "ty": 0, "parent": 40, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "42"}, {"ind": 40, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [70, 0]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 49, "ty": 0, "parent": 45, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "47"}, {"ind": 45, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [100, 53]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 54, "ty": 0, "parent": 50, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "52"}, {"ind": 50, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [9, 35]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 59, "ty": 0, "parent": 55, "ks": {}, "w": 14, "h": 20, "ip": 0, "op": 151, "st": 0, "refId": "57"}, {"ind": 55, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [38, 88]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 64, "ty": 0, "parent": 60, "ks": {}, "w": 22, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "62"}, {"ind": 60, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [20, 88]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 69, "ty": 0, "parent": 65, "ks": {}, "w": 22, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "67"}, {"ind": 65, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [20, 16]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 74, "ty": 0, "parent": 70, "ks": {}, "w": 22, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "72"}, {"ind": 70, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [80, 16]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 79, "ty": 0, "parent": 75, "ks": {}, "w": 64, "h": 108, "ip": 0, "op": 151, "st": 0, "refId": "77"}, {"ind": 75, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [29, 0]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 84, "ty": 0, "parent": 80, "ks": {}, "w": 64, "h": 108, "ip": 0, "op": 151, "st": 0, "refId": "82"}, {"ind": 80, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [29, 0]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 89, "ty": 0, "parent": 85, "ks": {}, "w": 122, "h": 4, "ip": 0, "op": 151, "st": 0, "refId": "87"}, {"ind": 85, "ty": 3, "parent": 14, "ks": {"p": {"a": 0, "k": [0, 52]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 14, "ty": 3, "parent": 1, "ks": {"a": {"a": 0, "k": [61, 54]}, "p": {"a": 0, "k": [80, 80]}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 0, "y": 1}, "o": {"x": 0.5, "y": 0}}, {"t": 75, "s": [180], "i": {"x": 0, "y": 1}, "o": {"x": 0.5, "y": 0}}, {"t": 150, "s": [0], "h": 1}]}}, "ip": 0, "op": 151, "st": 0}, {"ind": 1, "ty": 3, "parent": 0, "ks": {}, "ip": 0, "op": 151, "st": 0}, {"ind": 0, "ty": 3, "ks": {}, "ip": 0, "op": 151, "st": 0}], "meta": {"g": "https://jitter.video"}, "op": 150, "v": "5.7.4", "w": 160}