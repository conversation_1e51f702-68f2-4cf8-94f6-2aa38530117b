//
//  PaywallManagerTests.swift
//  MinimalistWeather
//
//  Created by Kered on 2025/7/25.
//

import Foundation

/// PaywallManager 測試類別
/// 這個檔案包含了測試 PaywallManager 各種情境的方法
class PaywallManagerTests {
    
    static func runAllTests() {
        print("🧪 開始執行 PaywallManager 測試...")
        
        testFirstInstall()
        testSameDayReopen()
        testAfterOneDay()
        testDebugMode()
        testSubscribedUser()
        
        print("🧪 PaywallManager 測試完成")
    }
    
    /// 測試首次安裝情境
    static func testFirstInstall() {
        print("\n📱 測試情境：首次安裝")
        
        let manager = PaywallManager.shared
        
        // 清除所有記錄，模擬首次安裝
        manager.clearPaywallHistory()
        
        // 確保不是 debug 模式
        AppSettings.shared.paywallDebugMode = false
        
        let shouldShow = manager.shouldShowPaywallOnAppLaunch()
        
        print("結果：\(shouldShow ? "顯示" : "不顯示") paywall")
        print("預期：不顯示 paywall（首次安裝）")
        print("✅ 測試\(shouldShow ? "失敗" : "通過")")
        
        // 檢查是否已記錄時間
        let lastShown = manager.getLastPaywallShownDate()
        print("上次顯示時間：\(lastShown != nil ? "已記錄" : "未記錄")")
    }
    
    /// 測試同一天重複開啟
    static func testSameDayReopen() {
        print("\n📅 測試情境：同一天重複開啟")
        
        let manager = PaywallManager.shared
        
        // 確保不是 debug 模式
        AppSettings.shared.paywallDebugMode = false
        
        // 記錄今天已顯示過
        manager.recordPaywallShown()
        
        let shouldShow = manager.shouldShowPaywallOnAppLaunch()
        
        print("結果：\(shouldShow ? "顯示" : "不顯示") paywall")
        print("預期：不顯示 paywall（同一天內）")
        print("✅ 測試\(shouldShow ? "失敗" : "通過")")
    }
    
    /// 測試距離上次顯示超過1天
    static func testAfterOneDay() {
        print("\n⏰ 測試情境：距離上次顯示超過1天")
        
        let manager = PaywallManager.shared
        
        // 確保不是 debug 模式
        AppSettings.shared.paywallDebugMode = false
        
        // 設定上次顯示時間為2天前
        let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: Date())!
        UserDefaults.standard.set(twoDaysAgo, forKey: "lastPaywallShownDate")
        
        let shouldShow = manager.shouldShowPaywallOnAppLaunch()
        
        print("結果：\(shouldShow ? "顯示" : "不顯示") paywall")
        print("預期：顯示 paywall（超過1天）")
        print("✅ 測試\(shouldShow ? "通過" : "失敗")")
    }
    
    /// 測試 Debug 模式
    static func testDebugMode() {
        print("\n🐛 測試情境：Debug 模式")
        
        let manager = PaywallManager.shared
        
        // 啟用 debug 模式
        AppSettings.shared.paywallDebugMode = true
        
        // 即使今天已顯示過，debug 模式也應該顯示
        manager.recordPaywallShown()
        
        let shouldShow = manager.shouldShowPaywallOnAppLaunch()
        
        print("結果：\(shouldShow ? "顯示" : "不顯示") paywall")
        print("預期：顯示 paywall（Debug 模式）")
        print("✅ 測試\(shouldShow ? "通過" : "失敗")")
        
        // 關閉 debug 模式
        AppSettings.shared.paywallDebugMode = false
    }
    
    /// 測試已訂閱用戶
    static func testSubscribedUser() {
        print("\n💎 測試情境：已訂閱用戶")
        
        let manager = PaywallManager.shared
        
        // 確保不是 debug 模式
        AppSettings.shared.paywallDebugMode = false
        
        // 設定上次顯示時間為2天前
        let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: Date())!
        UserDefaults.standard.set(twoDaysAgo, forKey: "lastPaywallShownDate")
        
        // 注意：這個測試需要實際的 IAP 狀態，在真實環境中測試
        let shouldShow = manager.shouldShowPaywallOnAppLaunch()
        
        print("結果：\(shouldShow ? "顯示" : "不顯示") paywall")
        print("當前訂閱狀態：\(IAPService.shared.isPro ? "已訂閱" : "未訂閱")")
        
        if IAPService.shared.isPro {
            print("預期：不顯示 paywall（已訂閱）")
            print("✅ 測試\(shouldShow ? "失敗" : "通過")")
        } else {
            print("預期：顯示 paywall（未訂閱且超過1天）")
            print("✅ 測試\(shouldShow ? "通過" : "失敗")")
        }
    }
    
    /// 顯示當前狀態
    static func showCurrentStatus() {
        print("\n📊 當前 PaywallManager 狀態：")
        print(PaywallManager.shared.getDebugInfo())
    }
}

// MARK: - 便利方法

extension PaywallManagerTests {
    
    /// 重置所有測試狀態
    static func resetTestState() {
        PaywallManager.shared.clearPaywallHistory()
        AppSettings.shared.paywallDebugMode = false
        print("🔄 測試狀態已重置")
    }
    
    /// 模擬不同的時間場景
    static func simulateTimeScenario(daysAgo: Int) {
        let targetDate = Calendar.current.date(byAdding: .day, value: -daysAgo, to: Date())!
        UserDefaults.standard.set(targetDate, forKey: "lastPaywallShownDate")
        print("⏰ 已模擬 \(daysAgo) 天前的場景")
    }
}
