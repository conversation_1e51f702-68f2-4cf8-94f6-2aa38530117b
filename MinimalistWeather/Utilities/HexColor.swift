import SwiftUI

struct HexColor {
    /// 將十六進制顏色代碼轉換為Color
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 對應的SwiftUI Color
    static func color(_ hex: String) -> Color {
        let (r, g, b, a) = hexToRGBA(hex)
        return Color(red: Double(r) / 255.0, green: Double(g) / 255.0, blue: Double(b) / 255.0, opacity: Double(a) / 255.0)
    }
    
    /// 將十六進制顏色代碼轉換為RGBA元組
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 包含紅、綠、藍、透明度的元組 (r, g, b, a)，每個值範圍為0-255
    static func hexToRGBA(_ hex: String) -> (r: Int, g: Int, b: Int, a: Int) {
        var hexString = hex.trimmingCharacters(in: .whitespacesAndNewlines).uppercased()
        
        // 移除可能存在的#前綴
        if hexString.hasPrefix("#") {
            hexString.remove(at: hexString.startIndex)
        }
        
        // 默認透明度為255（完全不透明）
        var alpha = 255
        var rgb = 0
        
        // 檢查是否有透明度值
        if hexString.count == 8 {
            // 格式為 RRGGBBAA
            if let intValue = Int(hexString, radix: 16) {
                alpha = intValue & 0xFF
                rgb = intValue >> 8
            }
        } else if hexString.count == 6 {
            // 格式為 RRGGBB
            if let intValue = Int(hexString, radix: 16) {
                rgb = intValue
            }
        } else {
            // 無效的十六進制格式
            print("無效的十六進制顏色格式: \(hex)")
            return (0, 0, 0, 255)
        }
        
        // 分離RGB值
        let red = (rgb >> 16) & 0xFF
        let green = (rgb >> 8) & 0xFF
        let blue = rgb & 0xFF
        
        return (red, green, blue, alpha)
    }
    
    /// 將十六進制顏色代碼轉換為UIColor
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 對應的UIColor
    static func uiColor(_ hex: String) -> UIColor {
        let (r, g, b, a) = hexToRGBA(hex)
        return UIColor(red: CGFloat(r) / 255.0, green: CGFloat(g) / 255.0, blue: CGFloat(b) / 255.0, alpha: CGFloat(a) / 255.0)
    }
}

// MARK: - 主題管理
extension HexColor {
    /// 顏色主題枚舉
    enum ColorTheme {
        /// 主要背景色
        case primaryBackground
        /// 次要背景色
        case secondaryBackground
        /// 主要文字顏色
        case primaryText
        /// 次要文字顏色
        case secondaryText
        /// 輔助文字顏色
        case tertiaryText
        /// 強調顏色
        case accent
        /// 分隔線顏色
        case separator
        /// 禁用狀態顏色
        case progressBar
        case disabled
        /// 漸層起始顏色
        case gradientStart
        /// 漸層結束顏色
        case gradientEnd
        /// 圖表顏色
        case chart
        /// 成功狀態顏色
        case success
        /// 陰影顏色
        case shadow
        /// Premium
        case premium
        /// UV 指數顏色 1 (0-2)
        case uvOne
        /// UV 指數顏色 2 (3-5)
        case uvTwo
        /// UV 指數顏色 3 (6-7)
        case uvThree
        /// UV 指數顏色 4 (8-11)
        case uvFour
    }
    
    /// 白天主題顏色配置
    private static let lightThemeColors: [ColorTheme: String] = [
        .primaryBackground: "F9F9F9",
        .secondaryBackground: "FFFFFF",
        .primaryText: "222222",
        .secondaryText: "888888",
        .tertiaryText: "CCCCCC",
        .accent: "222222",
        .separator: "DEDEDE",
        .progressBar: "EEEEEE",
        .disabled: "CCCCCC",
        .gradientStart: "888888",
        .gradientEnd: "B0B0B0",
        .chart: "AAAAAA",
        .success: "009966",
        .shadow: "888888",
        .premium: "B60000",
        .uvOne: "EEEEEE",
        .uvTwo: "FFE99A",
        .uvThree: "FFD586",
        .uvFour: "FFAAAA"
    ]
    
    /// 夜晚主題顏色配置
    private static let darkThemeColors: [ColorTheme: String] = [
        .primaryBackground: "222222",
        .secondaryBackground: "2C2C2E",
        .primaryText: "F9F9F9",
        .secondaryText: "AEAEB2",
        .tertiaryText: "636366",
        .accent: "F9F9F9",
        .separator: "38383A",
        .progressBar: "38383A",
        .disabled: "636366",
        .gradientStart: "AEAEB2",
        .gradientEnd: "636366",
        .chart: "8E8E93",
        .success: "30D158",
        .shadow: "000000",
        .premium: "B60000",
        .uvOne: "EEEEEE",
        .uvTwo: "FFE99A",
        .uvThree: "FFD586",
        .uvFour: "FFAAAA"
    ]
    
    /// 獲取當前主題下的顏色
    /// - Parameter colorTheme: 顏色主題
    /// - Returns: 對應的 SwiftUI Color
    static func themed(_ colorTheme: ColorTheme) -> Color {
        let currentTheme = getCurrentTheme()
        let colorMap = currentTheme == .dark ? darkThemeColors : lightThemeColors
        
        guard let hexValue = colorMap[colorTheme] else {
            print("警告：找不到顏色主題 \(colorTheme) 的配置")
            return Color.primary
        }
        
        return color(hexValue)
    }
    
    /// 獲取當前主題下的 UIColor
    /// - Parameter colorTheme: 顏色主題
    /// - Returns: 對應的 UIColor
    static func themedUIColor(_ colorTheme: ColorTheme) -> UIColor {
        let currentTheme = getCurrentTheme()
        let colorMap = currentTheme == .dark ? darkThemeColors : lightThemeColors
        
        guard let hexValue = colorMap[colorTheme] else {
            print("警告：找不到顏色主題 \(colorTheme) 的配置")
            return UIColor.label
        }
        
        return uiColor(hexValue)
    }
    
    /// 獲取當前主題模式
    /// - Returns: 當前的主題模式
    private static func getCurrentTheme() -> ThemeMode {
        let settings = AppSettings.shared
        let themeMode = settings.themeMode
        
        switch themeMode {
        case .system:
            // 根據系統外觀判斷
            if #available(iOS 13.0, *) {
                return UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
            } else {
                return .light
            }
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}

// MARK: - 向後兼容的便利方法
extension HexColor {
    /// 主要背景色 (向後兼容)
    static var primaryBackground: Color { themed(.primaryBackground) }
    /// 次要背景色 (向後兼容)
    static var secondaryBackground: Color { themed(.secondaryBackground) }
    /// 主要文字顏色 (向後兼容)
    static var primaryText: Color { themed(.primaryText) }
    /// 次要文字顏色 (向後兼容)
    static var secondaryText: Color { themed(.secondaryText) }
    /// 輔助文字顏色 (向後兼容)
    static var tertiaryText: Color { themed(.tertiaryText) }
    /// 強調顏色 (向後兼容)
    static var accent: Color { themed(.accent) }
    /// 分隔線顏色 (向後兼容)
    static var separator: Color { themed(.separator) }
    /// 禁用狀態顏色 (向後兼容)
    static var disabled: Color { themed(.disabled) }
    /// 成功狀態顏色 (向後兼容)
    static var success: Color { themed(.success) }
    /// Premium 顏色 (向後兼容)
    static var premium: Color { themed(.premium) }
}
