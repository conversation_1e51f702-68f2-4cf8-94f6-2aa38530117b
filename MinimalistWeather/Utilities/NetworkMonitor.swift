//
//  NetworkMonitor.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation
import Network

/// 網絡狀態監控
class NetworkMonitor: ObservableObject {
    /// 網絡連接狀態
    @Published var isConnected: Bool = true
    
    /// 連接類型
    @Published var connectionType: ConnectionType = .unknown
    
    /// 網絡連接類型枚舉
    enum ConnectionType {
        case wifi
        case cellular
        case wiredEthernet
        case unknown
    }
    
    /// 網絡路徑監控器
    private let monitor = NWPathMonitor()
    
    /// 監控隊列
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    /// 初始化並開始監控
    init() {
        startMonitoring()
    }
    
    /// 開始監控網絡狀態
    func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.determineConnectionType(path)
            }
        }
        monitor.start(queue: queue)
    }
    
    /// 停止監控
    func stopMonitoring() {
        monitor.cancel()
    }
    
    /// 確定連接類型
    /// - Parameter path: 網絡路徑
    private func determineConnectionType(_ path: NWPath) {
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .wiredEthernet
        } else {
            connectionType = .unknown
        }
    }
} 