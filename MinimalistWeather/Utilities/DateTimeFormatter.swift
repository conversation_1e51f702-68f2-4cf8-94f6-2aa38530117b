//
//  DateTimeFormatter.swift
//  MinimalistWeather
//
//  Created by Kered on 2025/4/15.
//

import Foundation

/// 日期時間格式化工具類別
class DateTimeFormatter {
    
    static let shared = DateTimeFormatter()
    
    private init() {}
    
    // MARK: - 系統設定偵測
    
    /// 偵測系統是否使用12小時制
    var isSystem12HourFormat: Bool {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        
        let testDate = Date()
        let timeString = formatter.string(from: testDate)
        
        // 檢查是否包含 AM/PM 指示符
        return timeString.contains(formatter.amSymbol) || timeString.contains(formatter.pmSymbol)
    }
    
    /// 獲取當前應用設定的時間格式（優先使用用戶設定，否則使用系統設定）
    private var currentTimeFormat: TimeFormat {
        return AppSettings.shared.timeFormat
    }
    
    /// 判斷當前是否使用12小時制
    private var is12HourFormat: Bool {
        return currentTimeFormat.is12Hour
    }
    
    /// 獲取當前語言環境
    private var currentLanguage: String {
        return LocalizationManager.shared.currentLanguage
    }
    
    /// 判斷是否為英文語言環境
    private var isEnglishLanguage: Bool {
        return currentLanguage.hasPrefix("en")
    }
    
    /// 判斷是否為中日韓語言環境（AM/PM 放在時間後面）
    private var isAsianLanguage: Bool {
        return currentLanguage.hasPrefix("zh") || currentLanguage.hasPrefix("ja") || currentLanguage.hasPrefix("ko")
    }
    
    // MARK: - API 時間處理（基於 RulerTestView 的正確邏輯）
    
    /// 將 API 時間字串轉換為 Date 對象
    /// - Parameter apiTimeString: API 時間字串（格式：yyyy-MM-dd HH:mm:ss）
    /// - Returns: Date 對象，如果轉換失敗則返回 nil
    func parseAPITimeString(_ apiTimeString: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return dateFormatter.date(from: apiTimeString)
    }
    
    /// 將 UTC 時間轉換為指定時區的當地時間（基於 RulerTestView 邏輯）
    /// - Parameters:
    ///   - utcDate: UTC 時間
    ///   - timezoneId: 時區標識符（如 "Asia/Taipei"）
    /// - Returns: 轉換後的當地時間

    
    /// 從 API 時間字串格式化為顯示時間（完整的 RulerTestView 邏輯）
    /// - Parameters:
    ///   - apiTimeString: API 時間字串（格式：yyyy-MM-dd HH:mm:ss）
    ///   - timezoneId: 時區標識符
    ///   - showDateIfNeeded: 是否在需要時顯示日期
    /// - Returns: 格式化後的時間字串
    func formatAPITimeForDisplay(_ apiTimeString: String, timezoneId: String?, showDateIfNeeded: Bool = false) -> String {
        guard let utcDate = parseAPITimeString(apiTimeString) else {
            return apiTimeString
        }
        
        // 轉換到當地時間
        let localDate = convertToTimezone(utcDate, timezoneId: timezoneId)
        
        // 獲取小時和分鐘
        var calendar = Calendar.current
        if let timezoneId = timezoneId, let timeZone = TimeZone(identifier: timezoneId) {
            calendar.timeZone = timeZone
        }
        let hour = calendar.component(.hour, from: localDate)
        _ = calendar.component(.minute, from: localDate)
        
        if showDateIfNeeded && hour == 0 {
            // 如果是00:00，顯示日期和時間
            let dateString = formatMonthDay(localDate)
            let timeString = formatTimeString("\(hour):00")
            return "\(dateString) \(timeString)"
        } else {
            // 只顯示時間
            return formatTimeString("\(hour):00")
        }
    }
    
    /// 專門用於時間軸顯示的 API 時間格式化（簡化版本）
    /// - Parameters:
    ///   - apiTimeString: API 時間字串
    ///   - timezoneId: 時區標識符
    /// - Returns: 格式化後的時間字串
    func formatAPITimeForTimeline(_ apiTimeString: String, timezoneId: String?) -> String {
        return formatAPITimeForDisplay(apiTimeString, timezoneId: timezoneId, showDateIfNeeded: false)
    }
    
    // MARK: - 時區轉換增強功能
    
    /// 將 Date 轉換為指定時區的時間
    /// - Parameters:
    ///   - date: 原始日期（通常是 UTC 時間）
    ///   - timezoneId: 時區標識符（如 "Asia/Taipei"）
    /// - Returns: 轉換後的 Date（本地時間）
    func convertToTimezone(_ date: Date, timezoneId: String?) -> Date {
        guard let timezoneId = timezoneId,
              let timeZone = TimeZone(identifier: timezoneId) else {
            return date
        }
        
        var calendar = Calendar.current
        calendar.timeZone = timeZone
        
        // 從UTC時間創建時區調整後的新日期
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute, .second], from: date)
        return calendar.date(from: components) ?? date
    }
    
    /// 格式化時間顯示（支援時區轉換）
    /// - Parameters:
    ///   - date: 要格式化的日期（UTC 時間）
    ///   - timezoneId: 時區標識符
    ///   - showNow: 是否顯示 "NOW" 而不是實際時間
    /// - Returns: 格式化後的時間字串
    func formatTimeWithTimezone(_ date: Date, timezoneId: String?, showNow: Bool = false) -> String {
        if showNow {
            return "now".localized
        }
        
        // 轉換到指定時區
        let localDate = convertToTimezone(date, timezoneId: timezoneId)
        
        // 使用指定時區的 Calendar
        var calendar = Calendar.current
        if let timezoneId = timezoneId, let timeZone = TimeZone(identifier: timezoneId) {
            calendar.timeZone = timeZone
        }
        
        if is12HourFormat {
            return format12HourTimeWithCalendar(localDate, calendar: calendar)
        } else {
            return format24HourTimeWithCalendar(localDate, calendar: calendar)
        }
    }
    
    /// 格式化日期顯示（支援時區轉換）
    /// - Parameters:
    ///   - date: 要格式化的日期（UTC 時間）
    ///   - timezoneId: 時區標識符
    /// - Returns: 格式化後的日期字串
    func formatDateWithTimezone(_ date: Date, timezoneId: String?) -> String {
        let localDate = convertToTimezone(date, timezoneId: timezoneId)
        
        let formatter = DateFormatter()
        if let timezoneId = timezoneId, let timeZone = TimeZone(identifier: timezoneId) {
            formatter.timeZone = timeZone
        }
        
        // 根據語言環境設定不同的日期格式
        if isAsianLanguage {
            formatter.dateFormat = "E, M月d日"
        } else {
            formatter.dateFormat = "E, MMM d"
        }
        
        return formatter.string(from: localDate)
    }
    
    /// 格式化日期和時間顯示（支援時區轉換）
    /// - Parameters:
    ///   - date: 要格式化的日期（UTC 時間）
    ///   - timezoneId: 時區標識符
    ///   - showNow: 是否顯示 "NOW" 而不是實際時間
    /// - Returns: 格式化後的日期時間字串
    func formatDateTimeWithTimezone(_ date: Date, timezoneId: String?, showNow: Bool = false) -> String {
        let dateString = formatDateWithTimezone(date, timezoneId: timezoneId)
        let timeString = formatTimeWithTimezone(date, timezoneId: timezoneId, showNow: showNow)
        return "\(dateString) \(timeString)"
    }
    
    /// 格式化日出日落時間（支援時區轉換）
    /// - Parameters:
    ///   - date: 日出或日落時間（UTC 時間）
    ///   - timezoneId: 時區標識符
    /// - Returns: 格式化後的時間字串，如果是無效時間則返回 "--:--"
    func formatSunTimeWithTimezone(_ date: Date?, timezoneId: String?) -> String {
        guard let date = date else {
            return "--:--"
        }
        
        return formatTimeWithTimezone(date, timezoneId: timezoneId)
    }
    
    /// 為 RulerTestView 格式化時間
    /// - Parameters:
    ///   - date: 要格式化的日期
    ///   - timezoneId: 時區標識符
    ///   - isCurrentTime: 是否為當前時間（顯示 "NOW"）
    /// - Returns: 格式化後的時間字串
    func formatTimeForRuler(_ date: Date, timezoneId: String?, isCurrentTime: Bool = false) -> String {
        return formatTimeWithTimezone(date, timezoneId: timezoneId, showNow: isCurrentTime)
    }
    
    /// 為 TemperatureChart 格式化時間
    /// - Parameters:
    ///   - date: 要格式化的日期
    ///   - timezoneId: 時區標識符
    /// - Returns: 格式化後的時間字串
    func formatTimeForChart(_ date: Date, timezoneId: String?) -> String {
        return formatTimeWithTimezone(date, timezoneId: timezoneId)
    }
    
    /// 為 WeatherDetail 格式化詳細時間
    /// - Parameters:
    ///   - date: 要格式化的日期
    ///   - timezoneId: 時區標識符
    /// - Returns: 格式化後的詳細時間字串
    func formatDetailTimeWithTimezone(_ date: Date, timezoneId: String?) -> String {
        return formatDateTimeWithTimezone(date, timezoneId: timezoneId)
    }

    // MARK: - 時間格式化
    
    /// 格式化時間顯示
    /// - Parameters:
    ///   - date: 要格式化的日期（UTC 時間）
    ///   - showNow: 是否顯示 "NOW" 而不是實際時間
    /// - Returns: 格式化後的時間字串
    func formatTime(_ date: Date, showNow: Bool = false) -> String {
        if showNow {
            return "now".localized
        }
        
        // 暫時不進行時區轉換，直接使用 UTC 時間
        var calendar = Calendar.current
        calendar.timeZone = TimeZone(secondsFromGMT: 0)! // 使用 UTC 時區
        
        if is12HourFormat {
            return format12HourTimeWithCalendar(date, calendar: calendar)
        } else {
            return format24HourTimeWithCalendar(date, calendar: calendar)
        }
    }
    
    /// 格式化時間字串（從 HH:mm 格式）
    /// - Parameters:
    ///   - timeString: 時間字串（如 "14:30"）
    ///   - showNow: 是否顯示 "NOW"
    /// - Returns: 格式化後的時間字串
    func formatTimeString(_ timeString: String, showNow: Bool = false) -> String {
        if showNow {
            return "now".localized
        }
        
        // 解析時間字串
        let components = timeString.split(separator: ":")
        guard components.count >= 2,
              let hour = Int(components[0]),
              let minute = Int(components[1]) else {
            return timeString
        }
        
        if is12HourFormat {
            return format12HourTime(hour: hour, minute: minute)
        } else {
            return format24HourTime(hour: hour, minute: minute)
        }
    }
    
    /// 格式化12小時制時間
    private func format12HourTime(_ date: Date) -> String {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return format12HourTime(hour: hour, minute: minute)
    }
    
    /// 格式化12小時制時間（使用指定的 Calendar）
    private func format12HourTimeWithCalendar(_ date: Date, calendar: Calendar) -> String {
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return format12HourTime(hour: hour, minute: minute)
    }
    
    /// 格式化12小時制時間
    private func format12HourTime(hour: Int, minute: Int) -> String {
        let isPM = hour >= 12
        let hour12 = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour)
        
        let amPmText = isPM ? "pm".localized : "am".localized
        let timeText = String(format: "%d:%02d", hour12, minute)
        
        if isEnglishLanguage {
            // 英文：AM/PM 在後
            return "\(timeText) \(amPmText)"
        } else {
            // 中文、日文：AM/PM 在前
            return "\(amPmText) \(timeText)"
        }
    }
    
    /// 格式化24小時制時間
    private func format24HourTime(_ date: Date) -> String {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return format24HourTime(hour: hour, minute: minute)
    }
    
    /// 格式化24小時制時間（使用指定的 Calendar）
    private func format24HourTimeWithCalendar(_ date: Date, calendar: Calendar) -> String {
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        
        return format24HourTime(hour: hour, minute: minute)
    }
    
    /// 格式化24小時制時間
    private func format24HourTime(hour: Int, minute: Int) -> String {
        return String(format: "%02d:%02d", hour, minute)
    }
    
    // MARK: - 日期格式化
    
    /// 格式化日期顯示（根據系統設定）
    /// - Parameter date: 要格式化的日期（UTC 時間）
    /// - Returns: 格式化後的日期字串
    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        // 暫時不進行時區轉換，直接使用 UTC 時間
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        
        // 根據語言環境設定不同的日期格式
        if isAsianLanguage {
            formatter.dateFormat = "E, M月d日"
        } else {
            formatter.dateFormat = "E, MMM d"
        }
        
        return formatter.string(from: date)
    }
    
    /// 格式化月日顯示
    /// - Parameter date: 要格式化的日期
    /// - Returns: 格式化後的月日字串（如 "04/15"）
    func formatMonthDay(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        // 根據語言環境設定格式
        if isAsianLanguage {
            formatter.dateFormat = "MM/dd"
        } else {
            formatter.dateFormat = "MM/dd"
        }
        
        return formatter.string(from: date)
    }
    
    // MARK: - 星期格式化
    
    /// 格式化星期幾顯示（多語系）
    /// - Parameter date: 要格式化的日期
    /// - Returns: 格式化後的星期字串
    func formatWeekday(_ date: Date) -> String {
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: date)
        
        // 轉換為本地化的星期字串
        switch weekday {
        case 1: return "sunday".localized
        case 2: return "monday".localized
        case 3: return "tuesday".localized
        case 4: return "wednesday".localized
        case 5: return "thursday".localized
        case 6: return "friday".localized
        case 7: return "saturday".localized
        default: return ""
        }
    }
    
    /// 格式化星期幾顯示（英文縮寫，用於時間軸）
    /// - Parameter date: 要格式化的日期
    /// - Returns: 英文縮寫的星期字串（如 "MON"）
    func formatWeekdayAbbreviation(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE"
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: date).uppercased()
    }
}

// 注意：String.localized 擴展已在 LanguageService 中定義 
