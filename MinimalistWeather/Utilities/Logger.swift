//
//  Logger.swift
//  MinimalistWeather
//
//  Created by Augment Agent on 2025-07-28.
//

import Foundation

/// 統一的日誌記錄工具
/// 使用 #if DEBUG 確保只在 Debug Build 輸出
/// 使用 @autoclosure 讓 log message 在 Release 中不會被評估（避免效能損耗）
enum Logger {
    
    /// 輸出除錯訊息
    /// - Parameters:
    ///   - message: 要輸出的訊息（使用 @autoclosure 延遲評估）
    ///   - file: 檔案名稱（自動填入）
    ///   - line: 行號（自動填入）
    static func debug(_ message: @autoclosure () -> Any, file: String = #file, line: Int = #line) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        print("🪵 [\(fileName):\(line)] \(message())")
        #endif
    }
    
    /// 輸出警告訊息
    /// - Parameters:
    ///   - message: 要輸出的訊息（使用 @autoclosure 延遲評估）
    ///   - file: 檔案名稱（自動填入）
    ///   - line: 行號（自動填入）
    static func warning(_ message: @autoclosure () -> Any, file: String = #file, line: Int = #line) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        print("⚠️ [\(fileName):\(line)] \(message())")
        #endif
    }
    
    /// 輸出錯誤訊息
    /// - Parameters:
    ///   - message: 要輸出的訊息（使用 @autoclosure 延遲評估）
    ///   - file: 檔案名稱（自動填入）
    ///   - line: 行號（自動填入）
    static func error(_ message: @autoclosure () -> Any, file: String = #file, line: Int = #line) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        print("❌ [\(fileName):\(line)] \(message())")
        #endif
    }
    
    /// 輸出成功訊息
    /// - Parameters:
    ///   - message: 要輸出的訊息（使用 @autoclosure 延遲評估）
    ///   - file: 檔案名稱（自動填入）
    ///   - line: 行號（自動填入）
    static func success(_ message: @autoclosure () -> Any, file: String = #file, line: Int = #line) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        print("✅ [\(fileName):\(line)] \(message())")
        #endif
    }
    
    /// 輸出資訊訊息
    /// - Parameters:
    ///   - message: 要輸出的訊息（使用 @autoclosure 延遲評估）
    ///   - file: 檔案名稱（自動填入）
    ///   - line: 行號（自動填入）
    static func info(_ message: @autoclosure () -> Any, file: String = #file, line: Int = #line) {
        #if DEBUG
        let fileName = (file as NSString).lastPathComponent
        print("ℹ️ [\(fileName):\(line)] \(message())")
        #endif
    }
}
