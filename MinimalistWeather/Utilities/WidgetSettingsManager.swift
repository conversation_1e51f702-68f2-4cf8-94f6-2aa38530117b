//
//  WidgetSettingsManager.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/13.
//

import Foundation
import WidgetKit
import SwiftUI

/// Widget 設定管理器，負責儲存和讀取 Widget 相關設定
class WidgetSettingsManager: ObservableObject {
    static let shared = WidgetSettingsManager()

    private init() {}

    // MARK: - App Groups UserDefaults
    private let groupUserDefaults = UserDefaults(suiteName: "group.com.minlsm.weather")!
    
    // MARK: - UserDefaults Keys
    private enum Keys {
        static let selectedLocationId = "widget_selected_location_id"
        static let selectedLocationName = "widget_selected_location_name"
        static let selectedLocationLat = "widget_selected_location_lat"
        static let selectedLocationLon = "widget_selected_location_lon"
        static let selectedLocationCountry = "widget_selected_location_country"
        static let selectedLocationTimezone = "widget_selected_location_timezone"
        static let selectedLocationWeatherSource = "widget_selected_location_weather_source"
    }
    
    // MARK: - 位置設定
    
    /// 設定選擇的位置
    /// - Parameter location: 選擇的位置
    func setSelectedLocation(_ location: SavedLocation) {
        print("🔧 主應用調試: 設定 Widget 位置 - \(location.name)")

        // 同時寫入 App Groups 和標準 UserDefaults
        let standardDefaults = UserDefaults.standard

        // 寫入 App Groups
        groupUserDefaults.set(location.id.uuidString, forKey: Keys.selectedLocationId)
        groupUserDefaults.set(location.name, forKey: Keys.selectedLocationName)
        groupUserDefaults.set(location.lat, forKey: Keys.selectedLocationLat)
        groupUserDefaults.set(location.lon, forKey: Keys.selectedLocationLon)
        groupUserDefaults.set(location.country, forKey: Keys.selectedLocationCountry)
        if let timezone = location.timezone {
            groupUserDefaults.set(timezone, forKey: Keys.selectedLocationTimezone)
        } else {
            groupUserDefaults.removeObject(forKey: Keys.selectedLocationTimezone)
        }

        // 寫入標準 UserDefaults（Widget 可以讀取）
        standardDefaults.set(location.id.uuidString, forKey: Keys.selectedLocationId)
        standardDefaults.set(location.name, forKey: Keys.selectedLocationName)
        standardDefaults.set(location.lat, forKey: Keys.selectedLocationLat)
        standardDefaults.set(location.lon, forKey: Keys.selectedLocationLon)
        standardDefaults.set(location.country, forKey: Keys.selectedLocationCountry)
        if let timezone = location.timezone {
            standardDefaults.set(timezone, forKey: Keys.selectedLocationTimezone)
        } else {
            standardDefaults.removeObject(forKey: Keys.selectedLocationTimezone)
        }

        // 儲存天氣來源
        if let weatherSource = location.weatherSource {
            groupUserDefaults.set(weatherSource.rawValue, forKey: Keys.selectedLocationWeatherSource)
            standardDefaults.set(weatherSource.rawValue, forKey: Keys.selectedLocationWeatherSource)
        } else {
            groupUserDefaults.removeObject(forKey: Keys.selectedLocationWeatherSource)
            standardDefaults.removeObject(forKey: Keys.selectedLocationWeatherSource)
        }

        groupUserDefaults.synchronize()
        standardDefaults.synchronize()

        // 通知 Widget 更新
        WidgetCenter.shared.reloadAllTimelines()

        print("🔧 主應用調試: Widget 位置設定已儲存到兩個地方: \(location.name)")
        print("🔧 主應用調試: 已呼叫 WidgetCenter.shared.reloadAllTimelines()")
    }
    
    /// 獲取選擇的位置 ID
    /// - Returns: 位置 ID，如果沒有設定則返回 nil
    func getSelectedLocationId() -> UUID? {
        guard let idString = groupUserDefaults.string(forKey: Keys.selectedLocationId) else {
            return nil
        }

        return UUID(uuidString: idString)
    }
    
    /// 獲取選擇的位置資訊
    /// - Returns: 位置資訊，如果沒有設定則返回 nil
    func getSelectedLocation() -> SavedLocation? {
        guard let idString = groupUserDefaults.string(forKey: Keys.selectedLocationId),
              let id = UUID(uuidString: idString),
              let name = groupUserDefaults.string(forKey: Keys.selectedLocationName) else {
            return nil
        }

        let lat = groupUserDefaults.double(forKey: Keys.selectedLocationLat)
        let lon = groupUserDefaults.double(forKey: Keys.selectedLocationLon)
        let country = groupUserDefaults.string(forKey: Keys.selectedLocationCountry) ?? ""
        let timezone = groupUserDefaults.string(forKey: Keys.selectedLocationTimezone)

        // 讀取天氣來源
        var weatherSource: WeatherSource?
        if let sourceRawValue = groupUserDefaults.string(forKey: Keys.selectedLocationWeatherSource) {
            weatherSource = WeatherSource(rawValue: sourceRawValue)
        }

        return SavedLocation(
            id: id,
            name: name,
            formattedAddress: "\(name), \(country)",
            lat: lat,
            lon: lon,
            country: country,
            weatherSource: weatherSource,
            timezone: timezone
        )
    }
    
    /// 清除選擇的位置設定
    func clearSelectedLocation() {
        let userDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") ?? UserDefaults.standard
        
        userDefaults.removeObject(forKey: Keys.selectedLocationId)
        userDefaults.removeObject(forKey: Keys.selectedLocationName)
        userDefaults.removeObject(forKey: Keys.selectedLocationLat)
        userDefaults.removeObject(forKey: Keys.selectedLocationLon)
        userDefaults.removeObject(forKey: Keys.selectedLocationCountry)
        userDefaults.removeObject(forKey: Keys.selectedLocationTimezone)
        userDefaults.removeObject(forKey: Keys.selectedLocationWeatherSource)
        
        userDefaults.synchronize()
        
        // 通知 Widget 更新
        WidgetCenter.shared.reloadAllTimelines()
        
        print("Widget 位置設定已清除")
    }
    
    /// 檢查是否有設定位置
    /// - Returns: 是否有設定位置
    func hasSelectedLocation() -> Bool {
        return getSelectedLocationId() != nil
    }
}
