//
//  AppIconsSymbol.swift
//  MinimalistWeather
//
//  Created by Kered on 2025/4/15.
//

import SwiftUI

/// 用於創建和管理 AppIcons 符號顯示的工具類
struct AppIconsSymbol {
    // MARK: - 公開方法：創建視圖
    
    /// 創建一個顯示自定義圖標的 Text 視圖
    /// - Parameters:
    ///   - iconString: 符號字符串，來自 AppIcons
    ///   - fontSize: 字體大小
    ///   - color: 文本顏色
    /// - Returns: 配置好的 Text 視圖
    static func createView(for iconString: String, fontSize: CGFloat, color: Color = .primary) -> some View {
        Group {
            if isCustomFontAvailable() {
                // 使用自定義字體
                Text(iconString)
                    .font(.custom(FontLoader.customFontName, size: fontSize))
                    .foregroundColor(color)
                    .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
                    .scaledToFit() // 確保圖標適應容器大小
            } else {
                // 使用SF Symbols作為備用
                getSymbolForIcon(iconString)
                    .font(.system(size: fontSize))
                    .foregroundColor(color)
                    .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
                    .imageScale(.medium) // 固定圖標大小
            }
        }
    }
    
    /// 創建天氣圖標視圖，自動選擇自定義字體或系統圖標
    /// - Parameters:
    ///   - condition: 天氣狀況或圖標代碼
    ///   - fontSize: 字體大小
    ///   - color: 顏色
    /// - Returns: 配置好的視圖
    static func createWeatherIconView(for condition: String, fontSize: CGFloat, color: Color = .primary) -> some View {
        Group {
            if isCustomFontAvailable() {
                // 使用自定義字體
                Text(getWeatherIconFromCode(condition))
                    .font(.custom(FontLoader.customFontName, size: fontSize))
                    .foregroundColor(color)
            } else {
                // 使用系統圖標
                Image(systemName: getSystemSymbolNameForIcon(getWeatherIconFromCode(condition)))
                    .font(.system(size: fontSize))
                    .foregroundColor(color)
            }
        }
    }
    
    /// 將 AppIcons 圖標轉換為 UIImage
    /// - Parameters:
    ///   - iconString: 符號字符串，來自 AppIcons
    ///   - fontSize: 字體大小
    ///   - color: 圖標顏色
    /// - Returns: 轉換後的 UIImage，如果轉換失敗則返回系統圖標的 UIImage
    static func createUIImage(for iconString: String, fontSize: CGFloat, color: UIColor = HexColor.themedUIColor(.primaryText)) -> UIImage {
        // 檢查自定義字體是否可用
        if isCustomFontAvailable() {
            // 直接使用自定義字體 (自定義字體不會受到系統文字大小設定的影響)
            if let customFont = UIFont(name: FontLoader.customFontName, size: fontSize) {
                // 使用繪圖方式創建 UIImage
                let attributes: [NSAttributedString.Key: Any] = [
                    .font: customFont,
                    .foregroundColor: color
                ]
                
                let size = (iconString as NSString).size(withAttributes: attributes)
                let format = UIGraphicsImageRendererFormat()
                format.opaque = false
                
                let renderer = UIGraphicsImageRenderer(size: size, format: format)
                let image = renderer.image { context in
                    (iconString as NSString).draw(at: .zero, withAttributes: attributes)
                }
                
                return image
            }
        }
        
        // 如果自定義字體不可用或加載失敗，使用系統圖標
        let systemSymbolName = getSystemSymbolNameForIcon(iconString)
        
        // 創建配置，指定固定大小而非動態大小
        let config = UIImage.SymbolConfiguration(pointSize: fontSize, weight: .regular, scale: .medium)
        
        return UIImage(systemName: systemSymbolName, withConfiguration: config) ?? 
               UIImage(systemName: "questionmark.circle", withConfiguration: config)!
    }
    
    // MARK: - 輔助方法
    
    /// 判斷自定義字體是否可用
    /// - Returns: 是否可用
    private static func isCustomFontAvailable() -> Bool {
        return UIFont.fontNames(forFamilyName: FontLoader.customFontName).count > 0
    }
    
    /// 根據圖標字符獲取對應的系統SF Symbols
    /// - Parameter iconString: 圖標字符串
    /// - Returns: 對應的系統圖標
    static func getSymbolForIcon(_ iconString: String) -> Image {
        Image(systemName: getSystemSymbolNameForIcon(iconString))
    }
    
    /// 獲取與 AppIcons 圖標對應的系統圖標名稱
    /// - Parameter iconString: 圖標字符串
    /// - Returns: 系統圖標名稱
    static func getSystemSymbolNameForIcon(_ iconString: String) -> String {
        switch iconString {
        case AppIcons.clearDay:
            return "sun.max.fill"
        case AppIcons.clearNight:
            return "moon.stars.fill"
        case AppIcons.fewCloudsDay:
            return "cloud.sun.fill"
        case AppIcons.fewCloudsNight:
            return "cloud.moon.fill"
        case AppIcons.scatteredClouds:
            return "cloud.fill"
        case AppIcons.brokenClouds:
            return "smoke.fill"
        case AppIcons.showerRain:
            return "cloud.drizzle.fill"
        case AppIcons.rain:
            return "cloud.rain.fill"
        case AppIcons.thunderstorm:
            return "cloud.bolt.fill"
        case AppIcons.snow:
            return "snow"
        case AppIcons.mist:
            return "cloud.fog.fill"
        case AppIcons.sunrise:
            return "sunrise.fill"
        case AppIcons.sunset:
            return "sunset.fill"
        case AppIcons.temperature:
            return "thermometer"
        case AppIcons.humidity:
            return "humidity"
        case AppIcons.uv:
            return "sun.max"
        case AppIcons.search:
            return "magnifyingglass"
        case AppIcons.check:
            return "checkmark"
        case AppIcons.clear:
            return "trash"
        case AppIcons.close:
            return "xmark"
        case AppIcons.scrollDown:
            return "arrow.down"
        case AppIcons.scrollUp:
            return "arrow.up"
        default:
            return "questionmark.circle"
        }
    }
    
    // MARK: - 天氣圖標轉換方法
    
    /// 根據OpenWeatherMap的icon代碼獲取AppIcons字符
    /// - Parameter iconCode: OpenWeatherMap API的icon代碼 (如："01d"、"02n"等)
    /// - Returns: 對應的AppIcons字符
    static func getWeatherIconFromCode(_ iconCode: String) -> String {
        switch iconCode {
        case "01d": // 晴天 (白天)
            return AppIcons.clearDay
        case "01n": // 晴天 (夜間)
            return AppIcons.clearNight
        case "02d": // 少雲 (白天)
            return AppIcons.fewCloudsDay
        case "02n": // 少雲 (夜間)
            return AppIcons.fewCloudsNight
        case "03d", "03n": // 多雲 (散雲)
            return AppIcons.scatteredClouds
        case "04d", "04n": // 多雲 (碎雲)
            return AppIcons.brokenClouds
        case "09d", "09n": // 小雨
            return AppIcons.showerRain
        case "10d", "10n": // 雨
            return AppIcons.rain
        case "11d", "11n": // 雷雨
            return AppIcons.thunderstorm
        case "13d", "13n": // 雪
            return AppIcons.snow
        case "50d", "50n": // 霧
            return AppIcons.mist
        default:
            return AppIcons.fewCloudsDay
        }
    }
    
    // MARK: - Lottie 動畫映射方法
    
    /// 根據OpenWeatherMap的icon代碼獲取對應的Lottie動畫文件名
    /// - Parameter iconCode: OpenWeatherMap API的icon代碼 (如："01d"、"02n"等)
    /// - Returns: 對應的Lottie動畫文件名
    static func getLottieAnimationFromCode(_ iconCode: String) -> String {
        let animationName: String
        
        switch iconCode {
        case "01d": // 晴天白天
            animationName = "clearDay"
        case "01n": // 晴天夜間
            animationName = "clearNight"
        case "02d": // 少雲白天
            animationName = "fewCloudsDay"
        case "02n": // 少雲夜間
            animationName = "fewCloudsNight"
        case "03d", "03n": // 多雲白天
            animationName = "scatteredClouds"
        case "04d", "04n": // 多雲夜間
            animationName = "brokenClouds"
        case "09d", "09n": // 小雨白天
            animationName = "showerRain"
        case "10d", "10n": // 雨天白天
            animationName = "rain"
        case "11d", "11n": // 雷雨白天
            animationName = "thunderstorm"
        case "13d", "13n": // 雪天白天
            animationName = "snow"
        case "50d", "50n": // 霧天白天
            animationName = "mist"
        default:
            animationName = "clearDay" // 預設使用clearDay
        }
        
        return animationName
    }
    
    // MARK: - 天氣狀況方法
    
    /// 根據天氣圖標代碼返回對應的天氣狀況描述
    /// - Parameter iconCode: 天氣圖標代碼
    /// - Returns: 對應的天氣狀況描述
    static func getWeatherConditionFromCode(_ iconCode: String) -> String {
        // 新的 API 已直接提供本地化描述，此方法僅用於備用情況
        switch iconCode {
        case "01d", "01n":
            return "晴朗"
        case "02d", "02n":
            return "少雲"
        case "03d", "03n":
            return "多雲"
        case "04d", "04n":
            return "陰天"
        case "09d", "09n":
            return "陣雨"
        case "10d", "10n":
            return "小雨"
        case "11d", "11n":
            return "雷雨"
        case "13d", "13n":
            return "雪"
        case "50d", "50n":
            return "霧"
        default:
            return "未知天氣"
        }
    }
} 