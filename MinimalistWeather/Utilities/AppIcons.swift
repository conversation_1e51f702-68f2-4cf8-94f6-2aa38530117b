//
//  AppIcons.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation

/// 自定義字體圖標映射
struct AppIcons {
    static let appicon: String = "\u{E91E}" // 溫度
    // MARK: - 天氣圖標
    static let clearDay: String = "\u{E900}" // 太陽
    static let fewCloudsDay: String = "\u{E901}" // 雲
    static let clearNight: String = "\u{E902}" // 雨
    static let fewCloudsNight: String = "\u{E903}" // 雪
    static let scatteredClouds: String = "\u{E904}" // 暴風雨
    static let brokenClouds: String = "\u{E905}" // 多雲帶晴
    static let showerRain: String = "\u{E906}" // 霧
    static let rain: String = "\u{E907}" // 風
    static let thunderstorm: String = "\u{E908}" // 風
    static let snow: String = "\u{E909}" // 風
    static let mist: String = "\u{E90A}" // 風
    
    // 日出日落圖標
    static let sunrise: String = "\u{E926}" // 日出
    static let sunset: String = "\u{E927}" // 日落
    
    static let radioboxcheck: String            = "\u{E914}" // ✓ (Check Icon)
    static let radiobox: String            = "\u{E91B}" // ✓ (Check Icon)

    static let checkboxcheck: String            = "\u{E90F}" // ✓ (Check Icon)
    static let checkbox: String            = "\u{E911}" // ✓ (Check Icon)

    static let check: String         = "\u{E91F}" // ✓ (Pro Check Icon)
    static let procheck: String         = "\u{E91A}" // ✓ (Pro Check Icon)

    // MARK: - 控制圖標
    static let close: String            = "\u{E916}" // Example: History icon codepoint?
    static let add: String            = "\u{E912}" // Example: History icon codepoint?
    static let clear: String = "\u{E915}" // 清除
    static let search: String = "\u{E913}" // 搜尋
    
    // MARK: - 其他圖標
    static let nonet: String = "\u{E90E}" // 位置
    static let noserver: String = "\u{E910}" // 刷新
    static let file: String = "\u{E91C}" // 溫度
    static let next: String = "\u{E91D}" // 溫度
    static let lock: String = "\u{E923}" // 溫度
    
    
    static let cloud: String = "\u{E921}" // 雲量
    static let humidity: String = "\u{E90B}" // 濕度
    static let temperature: String = "\u{E90C}" // 溫度
    static let rainp: String = "\u{E90D}" // 降雨機率
    static let uv: String = "\u{E929}" // 紫外線指數 (使用太陽圖標)
    static let timeformat: String         = "\u{E917}" // 時間格式
    static let rulersystem: String         = "\u{E922}" // 時間格式
    static let language: String        = "\u{E918}" // 語言
    static let themeday: String        = "\u{E925}" // 白天
    static let themenight: String        = "\u{E924}" // 夜晚
    static let feedback: String        = "\u{E926}" // 夜晚
    static let about: String        = "\u{E927}" // 夜晚
    static let paywall: String        = "\u{E928}" // 夜晚

    static let scrollDown: String       = "\u{E919}" // Example: History icon codepoint?
    static let scrollUp: String         = "\u{E920}" // Example: History icon codepoint?
    
    // MARK: - Location相關圖標
    // static let edit: String             = "\u{E921}" // 編輯圖標
    // static let delete: String           = "\u{E922}" // 刪除圖標
    // static let location: String         = "\u{E923}" // 位置圖標
} 