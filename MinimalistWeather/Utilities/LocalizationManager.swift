//
//  LocalizationManager.swift
//  MinimalistWeather
//
//  Created by Kered on 2025/4/15.
//

import Foundation

/// 多語系管理器
class LocalizationManager: ObservableObject {
    
    static let shared = LocalizationManager()
    
    @Published var currentLanguage: String = "en"
    
    private init() {
        // 從UserDefaults讀取已儲存的語言設定
        if let savedLanguage = UserDefaults.standard.string(forKey: "AppLanguage") {
            currentLanguage = savedLanguage
        } else {
            // 如果沒有儲存的設定，使用系統語言
            currentLanguage = getSystemLanguage()
        }
    }
    
    /// 設定語言
    func setLanguage(_ language: String) {
        currentLanguage = language
        UserDefaults.standard.set(language, forKey: "AppLanguage")
    }
    
    /// 獲取系統語言對應的Google Maps API語言代碼
    private func getSystemLanguage() -> String {
        let systemLanguage = Locale.current.languageCode ?? "en"
        let region = Locale.current.regionCode ?? ""
        
        // 根據系統語言返回對應的Google Maps API語言代碼
        switch systemLanguage {
        case "zh":
            return region == "CN" ? "zh-CN" : "zh-TW"
        case "ja":
            return "ja"
        case "ko":
            return "ko"
        case "en":
            return "en"
        case "es":
            return "es"
        case "fr":
            return "fr"
        case "de":
            return "de"
        case "it":
            return "it"
        case "pt":
            return "pt"
        case "ru":
            return "ru"
        case "ar":
            return "ar"
        case "hi":
            return "hi"
        case "th":
            return "th"
        case "vi":
            return "vi"
        case "tr":
            return "tr"
        case "pl":
            return "pl"
        case "nl":
            return "nl"
        case "sv":
            return "sv"
        case "da":
            return "da"
        case "no":
            return "no"
        case "fi":
            return "fi"
        case "he":
            return "he"
        case "fa":
            return "fa"
        case "bn":
            return "bn"
        case "id":
            return "id"
        default:
            return "en" // 預設使用英文
        }
    }
    
    /// 支援的語言清單 (基於Google Maps API支援的語言)
    static let supportedLanguages: [(code: String, name: String)] = [
        ("zh-TW", "繁體中文"),
        ("zh-CN", "简体中文"),
        ("en", "English"),
        ("ja", "日本語"),
        ("ko", "한국어"),
        ("es", "Español"),
        ("fr", "Français"),
        ("de", "Deutsch"),
        ("it", "Italiano"),
        ("pt", "Português"),
        ("ru", "Русский"),
        ("ar", "العربية"),
        ("hi", "हिन्दी"),
        ("th", "ไทย"),
        ("vi", "Tiếng Việt"),
        ("tr", "Türkçe"),
        ("pl", "Polski"),
        ("nl", "Nederlands"),
        ("sv", "Svenska"),
        ("da", "Dansk"),
        ("no", "Norsk"),
        ("fi", "Suomi"),
        ("he", "עברית"),
        ("fa", "فارسی"),
        ("bn", "বাংলা"),
        ("id", "Bahasa Indonesia")
    ]
} 