//
//  LoadingView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI

struct LoadingView: View {
    @State private var isAnimating: Bool = false
    
    // 動畫設置參數
    private let timing: Double
    private let frame: CGSize
    private let primaryColor: Color
    
    // 初始化方法，允許自定義
    init(color: Color = HexColor.themed(.primaryText), size: CGFloat = 50, speed: Double = 0.5) {
        self.timing = speed * 4
        self.frame = CGSize(width: size, height: size)
        self.primaryColor = color
    }
    
    var body: some View {
        ZStack(alignment: .leading) {
            // 背景條
            RoundedRectangle(cornerRadius: 25.0)
                .fill(HexColor.themed(.primaryBackground))
            
            // 動畫條
            RoundedRectangle(cornerRadius: 25.0)
                .fill(primaryColor)
                .frame(width: isAnimating ? frame.width / 2 : frame.width)
                .offset(x: isAnimating ? frame.width : -frame.width)
                .animation(Animation.easeInOut(duration: timing).repeatForever(autoreverses: false), value: isAnimating)
        }
        .frame(width: frame.width, height: frame.height / 10, alignment: .center)
        .clipped()
        .frame(width: frame.width, height: frame.height, alignment: .center)
        .onAppear {
            isAnimating.toggle()
        }
    }
}

// 預覽 - 使用與 iOS 16 及更早版本兼容的方式
struct LoadingView_Previews: PreviewProvider {
    static var previews: some View {
        LoadingView()
    }
}
