//
//  FontLoader.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation
import UIKit
import CoreGraphics
import CoreText

/// 用於加載自定義字體的工具類
struct FontLoader {
    // 字體名稱常量
    static let customFontName = "Lim"
    
    // 加載字體並返回是否成功
    static func loadCustomFonts() -> Bool {
        // 檢查字體是否已經註冊
        if UIFont.fontNames(forFamilyName: customFontName).count > 0 {
            print("字體已註冊：\(customFontName)")
            return true
        }
        
        // 嘗試註冊字體
        guard let fontURL = Bundle.main.url(forResource: customFontName, withExtension: "ttf") else {
            print("找不到字體文件: \(customFontName).ttf")
            return false
        }
        
        // 使用新API註冊字體
        var error: Unmanaged<CFError>?
        
        // 使用CTFontManagerRegisterFontsForURL替代棄用的API
        if !CTFontManagerRegisterFontsForURL(fontURL as CFURL, .process, &error) {
            print("註冊字體錯誤: \(error.debugDescription)")
            return false
        }
        
        print("成功註冊字體：\(customFontName)")
        return true
    }
    
    // 備用方法，使用Data創建字體描述符（iOS 18及以上推薦）
    static func loadCustomFontsUsingData() -> Bool {
        // 檢查字體是否已經註冊
        if UIFont.fontNames(forFamilyName: customFontName).count > 0 {
            print("字體已註冊：\(customFontName)")
            return true
        }
        
        // 嘗試註冊字體
        guard let fontURL = Bundle.main.url(forResource: customFontName, withExtension: "ttf") else {
            print("找不到字體文件: \(customFontName).ttf")
            return false
        }
        
        guard let fontData = try? Data(contentsOf: fontURL) else {
            print("無法讀取字體文件")
            return false
        }
        
        // 使用CTFontManagerCreateFontDescriptorsFromData創建字體描述符
        let descriptors = CTFontManagerCreateFontDescriptorsFromData(fontData as CFData)
        
        // 檢查描述符是否有效
        if CFArrayGetCount(descriptors) == 0 {
            print("無法創建有效的字體描述符或描述符為空")
            return false
        }
        
        // 註冊所有字體描述符 - 不檢查返回值，而是通過加載後的字體是否存在來驗證
        CTFontManagerRegisterFontDescriptors(descriptors, .process, true, nil)
        
        // 驗證字體是否成功加載
        if UIFont.fontNames(forFamilyName: customFontName).count > 0 {
            print("成功註冊字體（使用Data方法）：\(customFontName)")
            return true
        } else {
            print("註冊字體描述符失敗：無法找到字體 \(customFontName)")
            return false
        }
    }
    
    // 列出所有可用字體（用於調試）
    static func listAllAvailableFonts() {
        for family in UIFont.familyNames.sorted() {
            print("字體家族: \(family)")
            for name in UIFont.fontNames(forFamilyName: family).sorted() {
                print("   字體: \(name)")
            }
        }
    }
} 