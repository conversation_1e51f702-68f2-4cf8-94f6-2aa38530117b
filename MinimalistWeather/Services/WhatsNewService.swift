//
//  WhatsNewService.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/23.
//

import Foundation
import SwiftUI

/// What's New 服務，管理版本更新提示
class WhatsNewService: ObservableObject {
    static let shared = WhatsNewService()
    
    @Published var shouldShowWhatsNew = false
    
    private let userDefaults = UserDefaults.standard
    private let lastVersionKey = "LastShownWhatsNewVersion"
    
    private init() {}
    
    /// 檢查是否需要顯示 What's New
    func checkShouldShowWhatsNew() {
        let currentVersion = getCurrentAppVersion()
        let lastShownVersion = userDefaults.string(forKey: lastVersionKey) ?? ""
        
        print("WhatsNewService: 當前版本 \(currentVersion), 上次顯示版本 \(lastShownVersion)")
        
        // 如果版本不同，顯示 What's New
        if currentVersion != lastShownVersion {
            shouldShowWhatsNew = true
            print("WhatsNewService: 需要顯示 What's New")
        } else {
            shouldShowWhatsNew = false
            print("WhatsNewService: 不需要顯示 What's New")
        }
    }
    
    /// 標記當前版本已顯示 What's New
    func markWhatsNewAsShown() {
        let currentVersion = getCurrentAppVersion()
        userDefaults.set(currentVersion, forKey: lastVersionKey)
        shouldShowWhatsNew = false
        print("WhatsNewService: 標記版本 \(currentVersion) 已顯示 What's New")
    }
    
    /// 手動顯示 What's New（從設定頁面觸發）
    func showWhatsNewManually() {
        shouldShowWhatsNew = true
    }
    
    /// 獲取當前應用版本
    private func getCurrentAppVersion() -> String {
        guard let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String else {
            return "1.0.0"
        }
        return version
    }
    
    /// 獲取多語系的遠端 JSON URL
    func getRemoteDataURL(for languageCode: String) -> String {
        // 根據語言代碼構建 URL
        // 這裡使用您提供的格式：https://example.com/release/{langcode}/release.json
        return "https://weather.minlsm.com/release/\(languageCode).json"
    }
    
    /// 獲取本地 JSON 檔案名稱（作為後備方案）
    /// SwiftNEW 會在遠端失敗時自動尋找本地檔案
    func getLocalDataFileName() -> String {
        return "whats_new_data"
    }

    /// 根據語言代碼獲取對應的本地檔案名稱
    func getLocalDataFileName(for languageCode: String) -> String {
        // 檢查是否有特定語言的檔案
        let specificFileName = "whats_new_\(languageCode)"
        if Bundle.main.path(forResource: specificFileName, ofType: "json") != nil {
            return specificFileName
        }

        // 回退到預設檔案
        return getLocalDataFileName()
    }
    
    /// 重置 What's New 狀態（用於測試）
    func resetWhatsNewState() {
        userDefaults.removeObject(forKey: lastVersionKey)
        shouldShowWhatsNew = false
        print("WhatsNewService: 重置 What's New 狀態")
    }

    /// 檢查是否有可用的本地檔案（用於調試）
    func checkAvailableLocalFiles() {
        // let fileNames = ["whats_new_data", "whats_new_en", "whats_new_ja", "whats_new_zh-Hant"]
        let fileNames = ["whats_new_data", "whats_new_en", "whats_new_ja"]

        print("WhatsNewService: 檢查可用的本地檔案:")
        for fileName in fileNames {
            if Bundle.main.path(forResource: fileName, ofType: "json") != nil {
                print("  ✅ \(fileName).json")
            } else {
                print("  ❌ \(fileName).json")
            }
        }
    }
}
