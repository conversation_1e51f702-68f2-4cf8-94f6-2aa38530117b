//
//  LocationRepository.swift
//  MinimalistWeather
//
//  Created by Ke<PERSON> on 2025/4/15.
//

import Foundation

/// 位置儲存庫，負責保存和載入用戶查詢過的位置
class LocationRepository {
    // MARK: - 常數
    
    /// UserDefaults 儲存鍵
    private let locationEntriesKey = "savedLocations"
    private let savedLocationsKey = "savedLocationsV2" // 使用新的key儲存SavedLocation
    private let lastViewedLocationIDKey = "lastViewedLocationID"
    
    /// 最大儲存位置數量
    private let maxLocationEntries = 50
    
    // MARK: - 公開方法 - SavedLocation
    
    /// 獲取所有保存的位置
    /// - Returns: 保存的位置數組
    func getAllSavedLocations() -> [SavedLocation] {
        guard let data = UserDefaults.standard.data(forKey: savedLocationsKey) else {
            // 嘗試從舊數據遷移
            migrateOldSavedLocations()
            
            // 再次檢查是否有數據
            if let migratedData = UserDefaults.standard.data(forKey: savedLocationsKey) {
                do {
                    return try JSONDecoder().decode([SavedLocation].self, from: migratedData)
                } catch {
                    print("讀取遷移後的SavedLocation時發生錯誤: \(error.localizedDescription)")
                    // 如果遷移失敗，清除損壞的資料
                    UserDefaults.standard.removeObject(forKey: savedLocationsKey)
                    return []
                }
            }
            
            return []
        }
        
        do {
            let locations = try JSONDecoder().decode([SavedLocation].self, from: data)
            return locations
        } catch {
            print("讀取SavedLocation時發生錯誤: \(error.localizedDescription)")
            print("嘗試清除損壞的資料並重新遷移...")
            
            // 清除損壞的資料
            UserDefaults.standard.removeObject(forKey: savedLocationsKey)
            
            // 嘗試重新遷移
            migrateOldSavedLocations()
            
            // 再次嘗試讀取
            if let retryData = UserDefaults.standard.data(forKey: savedLocationsKey) {
                do {
                    return try JSONDecoder().decode([SavedLocation].self, from: retryData)
                } catch {
                    print("重新遷移後仍然失敗: \(error.localizedDescription)")
                    return []
                }
            }
            
            return []
        }
    }
    
    /// 遷移舊版保存的位置數據到新格式
    private func migrateOldSavedLocations() {
        print("嘗試遷移舊數據到新格式")
        
        // 檢查是否已經遷移過
        if UserDefaults.standard.bool(forKey: "hasCompletedSavedLocationMigration") {
            print("已經完成過遷移，跳過")
            return
        }
        
        // 獲取舊的LocationEntry數據
        let oldEntries = getAllLocations()
        
        if oldEntries.isEmpty {
            print("沒有舊數據需要遷移")
            UserDefaults.standard.set(true, forKey: "hasCompletedSavedLocationMigration")
            return
        }
        
        // 將舊數據轉換為新格式
        var newLocations: [SavedLocation] = []
        
        for entry in oldEntries {
            let nameParts = entry.locationName.split(separator: ",")
            let name = String(nameParts.first ?? "").trimmingCharacters(in: .whitespaces)
            let country = nameParts.count > 1 ? String(nameParts[1]).trimmingCharacters(in: .whitespaces) : ""
            
            // 由於舊數據沒有經緯度，這裡只能使用佔位符
            // 實際應用中，這些位置可能需要用戶重新搜索
            let location = SavedLocation(
                name: name.isEmpty ? "未知位置" : name,
                formattedAddress: entry.locationName, // 使用原始位置名稱作為地址
                lat: 0.0,  // 佔位符
                lon: 0.0,  // 佔位符
                country: country
            )
            
            newLocations.append(location)
        }
        
        // 保存轉換後的數據
        if !newLocations.isEmpty {
            do {
                let data = try JSONEncoder().encode(newLocations)
                UserDefaults.standard.set(data, forKey: savedLocationsKey)
                UserDefaults.standard.set(true, forKey: "hasCompletedSavedLocationMigration")
                print("成功遷移 \(newLocations.count) 個位置")
            } catch {
                print("遷移過程中保存失敗: \(error.localizedDescription)")
            }
        } else {
            UserDefaults.standard.set(true, forKey: "hasCompletedSavedLocationMigration")
        }
    }
    
    /// 添加保存位置
    /// - Parameter location: 要保存的位置
    func addSavedLocation(_ location: SavedLocation) {
        var locations = getAllSavedLocations()
        
        // 檢查是否已存在相同位置，如果存在則更新
        if let index = locations.firstIndex(where: { $0.lat == location.lat && $0.lon == location.lon }) {
            locations.remove(at: index)
        }
        
        // 新位置添加到最前面
        locations.insert(location, at: 0)
        
        // 如果超過最大數量，刪除最舊的
        if locations.count > maxLocationEntries {
            locations = Array(locations.prefix(maxLocationEntries))
        }
        
        // 保存到 UserDefaults
        saveSavedLocations(locations)
    }
    
    /// 保存位置數組
    /// - Parameter locations: 位置數組
    private func saveSavedLocations(_ locations: [SavedLocation]) {
        do {
            let data = try JSONEncoder().encode(locations)
            UserDefaults.standard.set(data, forKey: savedLocationsKey)
        } catch {
            print("保存SavedLocation時發生錯誤: \(error.localizedDescription)")
        }
    }
    
    /// 移除指定位置
    /// - Parameter location: 要移除的位置
    func removeSavedLocation(_ location: SavedLocation) {
        var locations = getAllSavedLocations()
        
        // 找到並移除指定位置
        if let index = locations.firstIndex(where: { $0.id == location.id }) {
            locations.remove(at: index)
            
            // 保存更新後的列表
            saveSavedLocations(locations)
        }
    }
    
    /// 移除指定索引的位置
    /// - Parameters:
    ///   - indexSet: 索引集合
    ///   - locations: 位置數組
    func removeSavedLocations(at indexSet: IndexSet, from locations: [SavedLocation]) {
        var allLocations = getAllSavedLocations()
        
        // 將索引集合轉換為對應的ID集合
        let idsToRemove = indexSet.map { locations[$0].id }
        
        // 過濾掉要刪除的項目
        allLocations = allLocations.filter { !idsToRemove.contains($0.id) }
        
        // 保存更新後的列表
        saveSavedLocations(allLocations)
    }
    
    /// 清除所有保存的位置
    func clearAllSavedLocations() {
        UserDefaults.standard.removeObject(forKey: savedLocationsKey)
    }
    
    /// 重置遷移狀態（用於測試）
    func resetMigrationStatus() {
        UserDefaults.standard.removeObject(forKey: "hasCompletedSavedLocationMigration")
        UserDefaults.standard.removeObject(forKey: savedLocationsKey)
        print("已重置遷移狀態")
    }
    
    /// 更新指定位置的天氣來源
    /// - Parameters:
    ///   - location: 要更新的位置
    ///   - newSource: 新的天氣來源
    func updateLocationWeatherSource(_ location: SavedLocation, newSource: WeatherSource) {
        var locations = getAllSavedLocations()
        
        // 找到並更新指定位置的天氣來源
        if let index = locations.firstIndex(where: { $0.id == location.id }) {
            var updatedLocation = locations[index]
            updatedLocation.weatherSource = newSource
            locations[index] = updatedLocation
            
            // 保存更新後的列表
            saveSavedLocations(locations)
            print("已更新位置 \(updatedLocation.name) 的天氣來源為 \(newSource.displayName)")
        }
    }
    
    // MARK: - Timezone 相關方法
    
    /// 檢查位置是否需要獲取時區資訊
    /// - Parameter location: 要檢查的位置
    /// - Returns: 是否需要獲取時區資訊
    func needsTimezone(_ location: SavedLocation) -> Bool {
        return location.timezone == nil
    }
    
    /// 為位置獲取並更新時區資訊
    /// - Parameters:
    ///   - location: 要更新的位置
    ///   - completion: 完成回調，返回更新後的位置或錯誤
    func ensureTimezone(for location: SavedLocation, completion: @escaping (Result<SavedLocation, Error>) -> Void) {
        // 如果已有時區資訊，直接返回
        if location.timezone != nil {
            completion(.success(location))
            return
        }
        
        // 獲取時區資訊
        GoogleTimezoneService.shared.getTimezone(lat: location.lat, lon: location.lon) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let timezoneId):
                    // 更新位置的時區資訊
                    self?.updateLocationTimezone(location, timezone: timezoneId)
                    
                    // 創建更新後的位置對象
                    var updatedLocation = location
                    updatedLocation.timezone = timezoneId
                    
                    completion(.success(updatedLocation))
                    
                case .failure(let error):
                    print("獲取時區失敗: \(error)")
                    completion(.failure(error))
                }
            }
        }
    }
    
    /// 更新指定位置的時區資訊
    /// - Parameters:
    ///   - location: 要更新的位置
    ///   - timezone: 時區ID
    func updateLocationTimezone(_ location: SavedLocation, timezone: String) {
        var locations = getAllSavedLocations()
        
        // 找到並更新指定位置的時區資訊
        if let index = locations.firstIndex(where: { $0.id == location.id }) {
            var updatedLocation = locations[index]
            updatedLocation.timezone = timezone
            locations[index] = updatedLocation
            
            // 保存更新後的列表
            saveSavedLocations(locations)
            print("已更新位置 \(updatedLocation.name) 的時區為 \(timezone)")
        }
    }
    
    /// 批量檢查並更新需要時區資訊的位置
    /// - Parameter completion: 完成回調，返回更新的位置數量
    func ensureTimezonesForAllLocations(completion: @escaping (Int) -> Void) {
        let locations = getAllSavedLocations()
        let locationsNeedingTimezone = locations.filter { needsTimezone($0) }
        
        guard !locationsNeedingTimezone.isEmpty else {
            completion(0)
            return
        }
        
        let dispatchGroup = DispatchGroup()
        var updatedCount = 0
        
        for location in locationsNeedingTimezone {
            dispatchGroup.enter()
            
            ensureTimezone(for: location) { result in
                switch result {
                case .success(_):
                    updatedCount += 1
                case .failure(let error):
                    print("為位置 \(location.name) 獲取時區失敗: \(error)")
                }
                dispatchGroup.leave()
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            completion(updatedCount)
        }
    }
    
    /// 保存最後查看的位置ID
    /// - Parameter locationID: 位置ID
    func saveLastViewedLocationID(_ locationID: UUID?) {
        if let id = locationID {
            UserDefaults.standard.set(id.uuidString, forKey: lastViewedLocationIDKey)
        } else {
            UserDefaults.standard.removeObject(forKey: lastViewedLocationIDKey)
        }
    }
    
    /// 獲取最後查看的位置ID
    /// - Returns: 位置ID
    func getLastViewedLocationID() -> UUID? {
        guard let idString = UserDefaults.standard.string(forKey: lastViewedLocationIDKey),
              let id = UUID(uuidString: idString) else {
            return nil
        }
        return id
    }
    
    // MARK: - 公開方法 - 舊的LocationEntry (保留向後兼容)
    
    /// 獲取所有儲存的位置
    /// - Returns: 位置條目陣列
    func getAllLocations() -> [LocationEntry] {
        guard let data = UserDefaults.standard.data(forKey: locationEntriesKey) else {
            return []
        }
        
        do {
            let locations = try JSONDecoder().decode([LocationEntry].self, from: data)
            return locations
        } catch {
            print("讀取位置時發生錯誤: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 添加位置條目
    /// - Parameter entry: 位置條目
    func addEntry(_ entry: LocationEntry) {
        var locations = getAllLocations()
        
        // 檢查是否已存在相同位置，如果存在則更新
        if let index = locations.firstIndex(where: { $0.locationName == entry.locationName }) {
            locations.remove(at: index)
        }
        
        // 新位置添加到最前面
        locations.insert(entry, at: 0)
        
        // 如果超過最大數量，刪除最舊的
        if locations.count > maxLocationEntries {
            locations = Array(locations.prefix(maxLocationEntries))
        }
        
        // 保存到 UserDefaults
        do {
            let data = try JSONEncoder().encode(locations)
            UserDefaults.standard.set(data, forKey: locationEntriesKey)
        } catch {
            print("保存位置時發生錯誤: \(error.localizedDescription)")
        }
    }
    
    /// 移除位置條目
    /// - Parameter entry: 位置條目
    func removeEntry(_ entry: LocationEntry) {
        print("移除位置: \(entry.locationName)")
        var locations = getAllLocations()
        
        // 找到並移除指定位置
        if let index = locations.firstIndex(where: { $0.id == entry.id }) {
            locations.remove(at: index)
            
            // 保存更新後的列表
            do {
                let data = try JSONEncoder().encode(locations)
                UserDefaults.standard.set(data, forKey: locationEntriesKey)
            } catch {
                print("移除位置時發生錯誤: \(error.localizedDescription)")
            }
        }
    }
    
    /// 移除指定索引的位置
    /// - Parameter indexSet: 索引集合
    func removeEntries(at indexSet: IndexSet, from entries: [LocationEntry]) {
        print("移除位置索引: \(indexSet)")
        var locations = getAllLocations()
        
        // 將索引集合轉換為對應的ID集合
        let idsToRemove = indexSet.map { entries[$0].id }
        
        // 過濾掉要刪除的項目
        locations = locations.filter { !idsToRemove.contains($0.id) }
        
        // 保存更新後的列表
        do {
            let data = try JSONEncoder().encode(locations)
            UserDefaults.standard.set(data, forKey: locationEntriesKey)
        } catch {
            print("批量移除位置時發生錯誤: \(error.localizedDescription)")
        }
    }
    
    /// 清除所有位置
    func clearAllLocations() {
        UserDefaults.standard.removeObject(forKey: locationEntriesKey)
    }
} 