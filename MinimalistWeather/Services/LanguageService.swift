// LanguageService.swift
// 語言管理服務，處理多語系切換

import Foundation
import SwiftUI

class LanguageService: ObservableObject {
    static let shared = LanguageService()
    
    @Published var currentLanguage: Language {
        didSet {
            // 更新 NSLocalizedString 的語言設置
            Bundle.setLanguage(currentLanguage.code)
        }
    }
    
    enum Language: String, CaseIterable {
        case english = "en"
        case traditionalChinese = "zh-Hant"
        case japanese = "ja"
        case australian = "en-AU"
        case danish = "da"
        case swedish = "sv"
        case french = "fr"
        case frenchCanadian = "fr-CA"
        case british = "en-GB"
        case german = "de"
        case norwegian = "no"
        case finnish = "fi"
        case dutch = "nl"
        case italian = "it"
        case spanish = "es"
        
        var code: String {
            return self.rawValue
        }
        
        var displayName: String {
            switch self {
            case .english:
                return "English (US)"
            case .traditionalChinese:
                return "繁體中文"
            case .japanese:
                return "日本語"
            case .australian:
                return "English (Australia)"
            case .danish:
                return "Dansk"
            case .swedish:
                return "Svenska"
            case .french:
                return "Français"
            case .frenchCanadian:
                return "Français (Canada)"
            case .british:
                return "English (UK)"
            case .german:
                return "Deutsch"
            case .norwegian:
                return "Norsk"
            case .finnish:
                return "Suomi"
            case .dutch:
                return "Nederlands"
            case .italian:
                return "Italiano"
            case .spanish:
                return "Español"
            }
        }
        
        var nativeDisplayName: String {
            switch self {
            case .english:
                return "English (US)"
            case .traditionalChinese:
                return "繁體中文"
            case .japanese:
                return "日本語"
            case .australian:
                return "English (Australia)"
            case .danish:
                return "Dansk"
            case .swedish:
                return "Svenska"
            case .french:
                return "Français"
            case .frenchCanadian:
                return "Français (Canada)"
            case .british:
                return "English (UK)"
            case .german:
                return "Deutsch"
            case .norwegian:
                return "Norsk"
            case .finnish:
                return "Suomi"
            case .dutch:
                return "Nederlands"
            case .italian:
                return "Italiano"
            case .spanish:
                return "Español"
            }
        }
    }
    
    private init() {
        // 自動根據系統語言設定
        self.currentLanguage = Self.detectSystemLanguage()
        
        // 設置初始語言
        Bundle.setLanguage(currentLanguage.code)
    }
    
    /// 根據系統語言自動偵測應該使用的語言
    private static func detectSystemLanguage() -> Language {
        let systemLanguages = Locale.preferredLanguages
        
        for languageCode in systemLanguages {
            // 完整語言代碼匹配（包含地區）
            if languageCode.hasPrefix("en-AU") {
                return .australian
            } else if languageCode.hasPrefix("en-GB") {
                return .british
            } else if languageCode.hasPrefix("fr-CA") {
                return .frenchCanadian
            } else if languageCode.hasPrefix("da") {
                return .danish
            } else if languageCode.hasPrefix("sv") {
                return .swedish
            } else if languageCode.hasPrefix("fr") {
                return .french
            } else if languageCode.hasPrefix("de") {
                return .german
            } else if languageCode.hasPrefix("no") || languageCode.hasPrefix("nb") || languageCode.hasPrefix("nn") {
                return .norwegian
            } else if languageCode.hasPrefix("fi") {
                return .finnish
            } else if languageCode.hasPrefix("nl") {
                return .dutch
            } else if languageCode.hasPrefix("it") {
                return .italian
            } else if languageCode.hasPrefix("es") {
                return .spanish
            }
            
            // 提取語言代碼的主要部分（去掉地區代碼）
            let mainLanguageCode = String(languageCode.prefix(2))
            
            // 檢查是否為英文（預設美式英文）
            if mainLanguageCode == "en" {
                return .english
            }
            // 檢查是否為中文（繁體或簡體）
            else if mainLanguageCode == "zh" {
                return .traditionalChinese
            }
            // 檢查是否為日文
            else if mainLanguageCode == "ja" {
                return .japanese
            }
        }
        
        // 其他語言都使用英文
        return .english
    }
    
    func setLanguage(_ language: Language) {
        currentLanguage = language
    }
    
    func localizedString(for key: String) -> String {
        return Bundle.localizedBundle().localizedString(forKey: key, value: key, table: nil)
    }
}

// MARK: - Bundle Extension for Language Switching
extension Bundle {
    private static var bundle: Bundle!
    
    public static func localizedBundle() -> Bundle {
        if bundle == nil {
            return Bundle.main
        }
        return bundle
    }
    
    public static func setLanguage(_ language: String) {
        defer {
            object_setClass(Bundle.main, LanguageBundle.self)
        }
        
        if let path = Bundle.main.path(forResource: language, ofType: "lproj") {
            bundle = Bundle(path: path)
        } else {
            // 如果找不到對應語言，回退到英文
            if let path = Bundle.main.path(forResource: "en", ofType: "lproj") {
                bundle = Bundle(path: path)
            } else {
                bundle = Bundle.main
            }
        }
    }
}

class LanguageBundle: Bundle, @unchecked Sendable {
    override func localizedString(forKey key: String, value: String?, table tableName: String?) -> String {
        return Bundle.localizedBundle().localizedString(forKey: key, value: value, table: tableName)
    }
}

// MARK: - String Extension for Localization
extension String {
    var localized: String {
        return LanguageService.shared.localizedString(for: self)
    }
    
    func localized(with arguments: [CVarArg]) -> String {
        return String(format: self.localized, arguments: arguments)
    }
} 