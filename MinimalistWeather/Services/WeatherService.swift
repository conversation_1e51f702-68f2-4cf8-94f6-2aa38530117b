//
//  WeatherService.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import Foundation

/// 天氣服務，負責獲取天氣數據
class WeatherService {
    /// 錯誤類型
    enum WeatherError: Error {
        case networkError
        case invalidResponse
        case invalidLocation
        case apiError(String)
        case decodingError
        case unknown
        
        /// 錯誤訊息
        var message: String {
            switch self {
            case .networkError:
                return "Network connection error"
            case .invalidResponse:
                return "Invalid response"
            case .invalidLocation:
                return "Invalid location"
            case .apiError(let message):
                return "API error: \(message)"
            case .decodingError:
                return "Data decoding error"
            case .unknown:
                return "Unknown error"
            }
        }
    }
    
    // MARK: - 屬性
    // private let baseURL = "https://weather-api.minlsm.com"
    // private let baseURL = "https://weather-api.minlsm-com.workers.dev" // 替換為實際的中繼 API 域名
    // private let baseURL = "https://0f3a54f4-weather-api.minlsm-com.workers.dev" // 替換為實際的中繼 API 域名
    private let baseURL = "http://localhost:8787" // 替換為實際的中繼 API 域名
    private let defaultDataSource = "AW" // 預設使用 Google Weather
    
    /// 是否使用模擬數據 (開發測試用)
    private let useMockData = false
    
    /// 是否強制使用API數據（即使useMockData為true）
    private var forceUseAPIData = false
    
    /// 檢查是否應該使用模擬數據
    /// - Returns: 是否使用模擬數據
    private func shouldUseMockData() -> Bool {
        // 如果強制使用API數據，則返回false
        if forceUseAPIData {
            return false
        }
        // 否則返回useMockData的值
        return useMockData
    }

    /// 為 URLRequest 設置 API key
    /// - Parameter request: 要設置的 URLRequest
    private func setAPIKey(for request: inout URLRequest) {
        request.setValue("lUzaSyD97xHJnaaCsYMDKWqIR9vPCn9lwzb0qAI", forHTTPHeaderField: "x-api-key")
    }
    
    /// 獲取當前語系對應的 API 語言參數
    /// - Returns: API 語言參數字串
    private func getCurrentLanguageCode() -> String {
        let currentLanguage = LanguageService.shared.currentLanguage
        
        // 將 LanguageService.Language 映射到 API 語言參數
        switch currentLanguage {
        case .english:
            return "en-US"
        case .traditionalChinese:
            return "zh-TW"
        case .japanese:
            return "ja-JP"
        case .australian:
            return "en-AU"
        case .british:
            return "en-GB"
        case .french:
            return "fr-FR"
        case .frenchCanadian:
            return "fr-CA"
        case .german:
            return "de-DE"
        case .spanish:
            return "es-ES"
        case .italian:
            return "it-IT"
        case .dutch:
            return "nl-NL"
        case .danish:
            return "da-DK"
        case .swedish:
            return "sv-SE"
        case .norwegian:
            return "nb-NO"
        case .finnish:
            return "fi-FI"
        }
    }
    
    // MARK: - 公開方法
    
    /// 獲取指定位置的天氣數據
    /// - Parameters:
    ///   - location: 位置名稱
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調，返回天氣數據或錯誤
    func getWeatherForLocation(_ location: String, weatherSource: WeatherSource? = nil, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        // 如果使用模擬數據，直接返回模擬數據
        if shouldUseMockData() {
            getMockWeatherForLocation(location, completion: completion)
            return
        }
        
        // 實際API調用: 先獲取地理坐標，再獲取天氣數據
        getCoordinatesForLocation(location) { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let coordinates):
                self.getWeatherForCoordinates(coordinates.lat, lon: coordinates.lon, locationName: coordinates.name, weatherSource: weatherSource, completion: completion)
            case .failure(let error):
                // 檢查是否為網路錯誤
                if case .networkError = error {
                    // 網路錯誤時，標記下次應使用API而非模擬數據
                    self.forceUseAPIData = true
                }
                completion(.failure(error))
            }
        }
    }
    
    /// 使用經緯度獲取天氣數據
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調，返回天氣數據或錯誤
    func getWeatherForCoordinates(lat: Double, lon: Double, weatherSource: WeatherSource? = nil, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        if shouldUseMockData() {
            // 使用臺北模擬數據作為座標查詢的預設值
            getMockWeatherForLocation("台北市", completion: completion)
            return
        }
        
        // 先獲取該座標的位置名稱
        getReverseGeocodingForCoordinates(lat: lat, lon: lon) { [weak self] result in
            guard let self = self else { return }
            
            let locationName: String
            switch result {
            case .success(let name):
                locationName = name
            case .failure(let error):
                // 如果是網路錯誤，標記下次應使用API
                if case .networkError = error {
                    self.forceUseAPIData = true
                }
                locationName = "未知位置"
            }
            
            // 調用內部私有方法來獲取天氣數據
            self.getWeatherDataForCoordinates(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, completion: completion)
        }
    }
    
    /// 搜尋位置
    /// - Parameters:
    ///   - query: 搜尋關鍵詞
    ///   - completion: 完成回調，返回搜尋結果
    func searchLocations(_ query: String, completion: @escaping (Result<[GeocodingResult], WeatherError>) -> Void) {
        if useMockData {
            // 模擬搜尋結果
            let mockResults = [
                GeocodingResult(name: "台北市", local_names: ["en": "Taipei"], lat: 25.032969, lon: 121.565414, country: "TW", state: "Taipei"),
                GeocodingResult(name: "高雄市", local_names: ["en": "Kaohsiung"], lat: 22.626822, lon: 120.301742, country: "TW", state: "Kaohsiung"),
                GeocodingResult(name: "東京", local_names: ["en": "Tokyo"], lat: 35.689487, lon: 139.691711, country: "JP", state: nil)
            ]
            DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
                completion(.success(mockResults))
            }
            return
        }
        
        // 使用 Google Geocoding API 或其他地理編碼服務
        // 這裡暫時使用模擬數據，實際應用中需要實現真正的搜尋功能
        let mockResults = [
            GeocodingResult(name: "台北市", local_names: ["en": "Taipei"], lat: 25.032969, lon: 121.565414, country: "TW", state: "Taipei"),
            GeocodingResult(name: "高雄市", local_names: ["en": "Kaohsiung"], lat: 22.626822, lon: 120.301742, country: "TW", state: "Kaohsiung"),
            GeocodingResult(name: "東京", local_names: ["en": "Tokyo"], lat: 35.689487, lon: 139.691711, country: "JP", state: nil)
        ]
        
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
            completion(.success(mockResults))
        }
    }
    
    /// 根據經緯度獲取天氣數據
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - locationName: 位置名稱
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調
    func getWeatherForCoordinates(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource? = nil, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        if shouldUseMockData() {
            // 使用模擬數據
            getMockWeatherForLocation(locationName.isEmpty ? "未知位置" : locationName, completion: completion)
            return
        }

        // 調用私有實現
        getWeatherDataForCoordinates(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, completion: completion)
    }

    /// 根據經緯度獲取天氣數據（支援選擇每小時或每日預報）
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - locationName: 位置名稱
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - useDaily: 是否使用每日預報（true: current+daily, false: current+hourly）
    ///   - completion: 完成回調
    func getWeatherDataForCoordinates(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource? = nil, useDaily: Bool, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        if shouldUseMockData() {
            // 使用模擬數據
            getMockWeatherForLocation(locationName.isEmpty ? "未知位置" : locationName, completion: completion)
            return
        }

        // 調用私有實現
        getWeatherDataForCoordinatesInternal(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, useDaily: useDaily, completion: completion)
    }
    
    // MARK: - 私有方法 - 中繼 API 調用
    
    /// 使用反向地理編碼獲取座標對應的位置名稱
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - completion: 完成回調
    private func getReverseGeocodingForCoordinates(lat: Double, lon: Double, completion: @escaping (Result<String, WeatherError>) -> Void) {
        // 使用 Google Geocoding API 或其他服務
        // 這裡暫時返回座標格式的位置名稱
        let locationName = "位置 (\(String(format: "%.2f", lat)), \(String(format: "%.2f", lon)))"
        completion(.success(locationName))
    }
    
    /// 獲取位置的地理坐標
    /// - Parameters:
    ///   - location: 位置名稱
    ///   - completion: 完成回調
    private func getCoordinatesForLocation(_ location: String, completion: @escaping (Result<(lat: Double, lon: Double, name: String), WeatherError>) -> Void) {
        // 使用 Google Geocoding API 或其他服務
        // 這裡暫時返回台北的座標
        completion(.success((lat: 25.032969, lon: 121.565414, name: location)))
    }
    
    /// 根據經緯度獲取天氣數據的內部實現（每小時預報模式）
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - locationName: 位置名稱
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調，返回天氣數據或錯誤
    private func getWeatherDataForCoordinates(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource? = nil, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        getWeatherDataForCoordinatesInternal(lat, lon: lon, locationName: locationName, weatherSource: weatherSource, useDaily: false, completion: completion)
    }

    /// 根據經緯度獲取天氣數據的內部實現（支援選擇每小時或每日預報）
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - locationName: 位置名稱
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - useDaily: 是否使用每日預報（true: current+daily, false: current+hourly）
    ///   - completion: 完成回調，返回天氣數據或錯誤
    private func getWeatherDataForCoordinatesInternal(_ lat: Double, lon: Double, locationName: String, weatherSource: WeatherSource? = nil, useDaily: Bool, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        Logger.debug("開始獲取天氣數據: 位置=\(locationName), 緯度=\(lat), 經度=\(lon), 模式=\(useDaily ? "current+daily" : "current+hourly")")

        // 先獲取即時天氣
        Logger.debug("準備獲取即時天氣...")
        getCurrentWeather(lat: lat, lon: lon, weatherSource: weatherSource) { [weak self] currentResult in
            guard let self = self else { return }

            Logger.debug("即時天氣 API 調用完成")

            switch currentResult {
            case .success(let currentWeather):
                if useDaily {
                    Logger.success("即時天氣獲取成功，開始獲取每日預報...")

                    // 即時天氣成功後，再獲取每日預報
                    self.getDailyForecast(lat: lat, lon: lon, weatherSource: weatherSource) { dailyResult in
                        Logger.debug("每日預報 API 調用完成")

                        switch dailyResult {
                        case .success(let dailyForecast):
                            // 兩個請求都成功
                            Logger.success("即時天氣和每日預報都成功")
                            let weatherData = self.mapMiddlewareResponseToWeatherData(
                                currentWeather: currentWeather,
                                dailyForecast: dailyForecast,
                                locationName: locationName
                            )
                            completion(.success(weatherData))

                        case .failure(let dailyError):
                            // 即時天氣成功，每日預報失敗
                            Logger.error("每日預報獲取失敗: \(dailyError.message)")
                            let weatherData = self.mapMiddlewareResponseToWeatherData(
                                currentWeather: currentWeather,
                                hourlyForecast: nil,
                                locationName: locationName
                            )
                            completion(.success(weatherData))
                        }
                    }
                } else {
                    Logger.success("即時天氣獲取成功，開始獲取每小時預報...")

                    // 即時天氣成功後，再獲取每小時預報
                    self.getHourlyForecast(lat: lat, lon: lon, weatherSource: weatherSource) { hourlyResult in
                        Logger.debug("每小時預報 API 調用完成")

                        switch hourlyResult {
                        case .success(let hourlyForecast):
                            // 兩個請求都成功
                            Logger.success("即時天氣和每小時預報都成功")
                            let weatherData = self.mapMiddlewareResponseToWeatherData(
                                currentWeather: currentWeather,
                                hourlyForecast: hourlyForecast,
                                locationName: locationName
                            )
                            completion(.success(weatherData))

                        case .failure(let hourlyError):
                            // 即時天氣成功，每小時預報失敗
                            Logger.error("每小時預報獲取失敗: \(hourlyError.message)")
                            let weatherData = self.mapMiddlewareResponseToWeatherData(
                                currentWeather: currentWeather,
                                hourlyForecast: nil,
                                locationName: locationName
                            )
                            completion(.success(weatherData))
                        }
                    }
                }

            case .failure(let currentError):
                // 即時天氣失敗，直接返回錯誤
                Logger.error("即時天氣獲取失敗: \(currentError.message)")
                completion(.failure(currentError))
            }
        }
    }
    
    /// 獲取即時天氣
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調
    private func getCurrentWeather(lat: Double, lon: Double, weatherSource: WeatherSource? = nil, completion: @escaping (Result<MiddlewareCurrentResponse, WeatherError>) -> Void) {
        let dataSource = weatherSource?.rawValue ?? defaultDataSource
        let urlString = "\(baseURL)/api/v1/current?data=\(dataSource)&lat=\(lat)&lon=\(lon)&plan=app&lang=\(getCurrentLanguageCode())"
        
        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidLocation))
            return
        }
        
        Logger.debug("請求即時天氣 URL: \(urlString)")

        var request = URLRequest(url: url)
        setAPIKey(for: &request)

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                Logger.error("即時天氣請求錯誤: \(error.localizedDescription)")
                completion(.failure(.networkError))
                return
            }
            
            guard let data = data else {
                Logger.debug("即時天氣：沒有收到數據")
                completion(.failure(.invalidResponse))
                return
            }
            
            // 打印原始回應以便調試
            if let responseString = String(data: data, encoding: .utf8) {
                Logger.debug("即時天氣原始回應: \(responseString)")
            }
            
            do {
                let response = try JSONDecoder().decode(MiddlewareCurrentResponse.self, from: data)
                Logger.success("即時天氣解析成功，數據筆數: \(response.current.count)")
                
                if response.current.isEmpty {
                    Logger.debug("即時天氣回應中沒有數據")
                    completion(.failure(.invalidResponse))
                } else {
                    completion(.success(response))
                }
            } catch {
                Logger.error("即時天氣數據解析錯誤: \(error)")
                if let decodingError = error as? DecodingError {
                    Logger.error("解析錯誤詳情: \(decodingError)")
                }
                completion(.failure(.decodingError))
            }
        }.resume()
    }
    
    /// 獲取每小時預報
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調
    private func getHourlyForecast(lat: Double, lon: Double, weatherSource: WeatherSource? = nil, completion: @escaping (Result<MiddlewareHourlyResponse, WeatherError>) -> Void) {
        let dataSource = weatherSource?.rawValue ?? defaultDataSource
        let urlString = "\(baseURL)/api/v1/hourly?data=\(dataSource)&lat=\(lat)&lon=\(lon)&plan=full&lang=\(getCurrentLanguageCode())"

        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidLocation))
            return
        }

        Logger.debug("請求每小時預報 URL: \(urlString)")

        var request = URLRequest(url: url)
        setAPIKey(for: &request)

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                Logger.error("每小時預報請求錯誤: \(error.localizedDescription)")
                completion(.failure(.networkError))
                return
            }

            guard let data = data else {
                Logger.debug("每小時預報：沒有收到數據")
                completion(.failure(.invalidResponse))
                return
            }

            // 打印原始回應以便調試
            if let responseString = String(data: data, encoding: .utf8) {
                Logger.debug("每小時預報原始回應: \(responseString)")
            }

            do {
                let response = try JSONDecoder().decode(MiddlewareHourlyResponse.self, from: data)
                Logger.success("每小時預報解析成功，數據筆數: \(response.hourly.count)")

                if response.hourly.isEmpty {
                    Logger.debug("每小時預報回應中沒有數據")
                    completion(.failure(.invalidResponse))
                } else {
                    completion(.success(response))
                }
            } catch {
                Logger.error("每小時預報數據解析錯誤: \(error)")
                if let decodingError = error as? DecodingError {
                    Logger.error("解析錯誤詳情: \(decodingError)")
                }
                completion(.failure(.decodingError))
            }
        }.resume()
    }

    /// 獲取每日預報
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - weatherSource: 天氣來源（可選，未指定時使用預設值）
    ///   - completion: 完成回調
    private func getDailyForecast(lat: Double, lon: Double, weatherSource: WeatherSource? = nil, completion: @escaping (Result<MiddlewareDailyResponse, WeatherError>) -> Void) {
        let dataSource = weatherSource?.rawValue ?? defaultDataSource
        let urlString = "\(baseURL)/api/v1/daily?data=\(dataSource)&lat=\(lat)&lon=\(lon)&plan=full&lang=\(getCurrentLanguageCode())"

        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidLocation))
            return
        }

        Logger.debug("請求每日預報 URL: \(urlString)")

        var request = URLRequest(url: url)
        setAPIKey(for: &request)

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                Logger.error("每日預報請求錯誤: \(error.localizedDescription)")
                completion(.failure(.networkError))
                return
            }

            guard let data = data else {
                Logger.debug("每日預報：沒有收到數據")
                completion(.failure(.invalidResponse))
                return
            }

            // 打印原始回應以便調試
            if let responseString = String(data: data, encoding: .utf8) {
                Logger.debug("每日預報原始回應: \(responseString)")
            }

            do {
                let response = try JSONDecoder().decode(MiddlewareDailyResponse.self, from: data)
                Logger.success("每日預報解析成功，數據筆數: \(response.daily.count)")

                if response.daily.isEmpty {
                    Logger.debug("每日預報回應中沒有數據")
                    completion(.failure(.invalidResponse))
                } else {
                    completion(.success(response))
                }
            } catch {
                Logger.error("每日預報數據解析錯誤: \(error)")
                if let decodingError = error as? DecodingError {
                    Logger.error("解析錯誤詳情: \(decodingError)")
                }
                completion(.failure(.decodingError))
            }
        }.resume()
    }
    
    /// 將中繼 API 回應轉換為 WeatherData（每小時預報模式）
    /// - Parameters:
    ///   - currentWeather: 即時天氣回應
    ///   - hourlyForecast: 每小時預報回應
    ///   - locationName: 位置名稱
    /// - Returns: WeatherData 物件
    private func mapMiddlewareResponseToWeatherData(
        currentWeather: MiddlewareCurrentResponse,
        hourlyForecast: MiddlewareHourlyResponse?,
        locationName: String
    ) -> WeatherData {
        return mapMiddlewareResponseToWeatherData(
            currentWeather: currentWeather,
            hourlyForecast: hourlyForecast,
            dailyForecast: nil,
            locationName: locationName
        )
    }

    /// 將中繼 API 回應轉換為 WeatherData（每日預報模式）
    /// - Parameters:
    ///   - currentWeather: 即時天氣回應
    ///   - dailyForecast: 每日預報回應
    ///   - locationName: 位置名稱
    /// - Returns: WeatherData 物件
    private func mapMiddlewareResponseToWeatherData(
        currentWeather: MiddlewareCurrentResponse,
        dailyForecast: MiddlewareDailyResponse,
        locationName: String
    ) -> WeatherData {
        return mapMiddlewareResponseToWeatherData(
            currentWeather: currentWeather,
            hourlyForecast: nil,
            dailyForecast: dailyForecast,
            locationName: locationName
        )
    }

    /// 將中繼 API 回應轉換為 WeatherData（統一處理方法）
    /// - Parameters:
    ///   - currentWeather: 即時天氣回應
    ///   - hourlyForecast: 每小時預報回應（可選）
    ///   - dailyForecast: 每日預報回應（可選）
    ///   - locationName: 位置名稱
    /// - Returns: WeatherData 物件
    private func mapMiddlewareResponseToWeatherData(
        currentWeather: MiddlewareCurrentResponse,
        hourlyForecast: MiddlewareHourlyResponse?,
        dailyForecast: MiddlewareDailyResponse?,
        locationName: String
    ) -> WeatherData {
        
        guard let currentData = currentWeather.current.first else {
            // 如果沒有即時天氣數據，返回預設值
            return createDefaultWeatherData(locationName: locationName)
        }
        
        // 轉換即時天氣數據
        let temperature = Int(currentData.temperature.current.rounded())
        let condition = currentData.weather.condition
        let humidity = currentData.atmosphere.humidity
        let windSpeed = currentData.wind.speed // API 已提供 m/s 單位，不需轉換
        let precipitationProbability = currentData.precipitation.probability == -999 ? 0 : currentData.precipitation.probability
        let feelsLike = Int(currentData.temperature.feels_like.rounded())
        let iconCode = currentData.weather.icon
        
        // 處理可能為 -999 的數值
        let pressure = currentData.atmosphere.sea_level == -999 ? 1013.25 : currentData.atmosphere.sea_level
        let seaLevelPressure = currentData.atmosphere.sea_level == -999 ? 1013.25 : currentData.atmosphere.sea_level
        let groundLevelPressure = currentData.atmosphere.ground_level
        let visibility = currentData.atmosphere.visibility == -999 ? 10.0 : currentData.atmosphere.visibility
        let windDirection = currentData.wind.direction == -999 ? 0.0 : currentData.wind.direction
        let windGust = (currentData.wind.gust == -999 || currentData.wind.gust == -99) ? 0.0 : currentData.wind.gust
        let rainVolume = (currentData.precipitation.rain_amount == -999 || currentData.precipitation.rain_amount == -99) ? 0.0 : currentData.precipitation.rain_amount
        let snowVolume = (currentData.precipitation.snow_amount == -999 || currentData.precipitation.snow_amount == -99) ? 0.0 : currentData.precipitation.snow_amount
        let uvIndex = currentData.uv.index == -999 ? nil : currentData.uv.index

        Logger.debug("即時天氣數據轉換完成: 溫度=\(temperature)°C, 狀況=\(condition), 濕度=\(humidity)%, 降雨機率=\(precipitationProbability)%, UV指數=\(uvIndex ?? -999)")
        
        // 轉換日出日落時間 - 優先使用 daily 資料，否則使用 current 資料
        var sunrise: Date? = nil
        var sunset: Date? = nil

        // 如果是 daily 模式且有 daily 資料，使用第一筆 daily 資料的 sunevents
        if let dailyData = dailyForecast?.daily, let firstDaily = dailyData.first {
            sunrise = firstDaily.sunevents.sunrise.dateValue
            sunset = firstDaily.sunevents.sunset.dateValue

            if let sunriseDate = sunrise {
                Logger.debug("日出時間（來自 daily）: \(firstDaily.sunevents.sunrise) -> \(sunriseDate)")
            } else {
                Logger.debug("日出時間（來自 daily）: 無效數據，嘗試使用 current 資料")
                sunrise = currentData.sunevents.sunrise.dateValue
            }

            if let sunsetDate = sunset {
                Logger.debug("日落時間（來自 daily）: \(firstDaily.sunevents.sunset) -> \(sunsetDate)")
            } else {
                Logger.debug("日落時間（來自 daily）: 無效數據，嘗試使用 current 資料")
                sunset = currentData.sunevents.sunset.dateValue
            }
        } else {
            // 使用 current 資料的 sunevents
            sunrise = currentData.sunevents.sunrise.dateValue
            sunset = currentData.sunevents.sunset.dateValue

            if let sunriseDate = sunrise {
                Logger.debug("日出時間（來自 current）: \(currentData.sunevents.sunrise) -> \(sunriseDate)")
            } else {
                Logger.debug("日出時間（來自 current）: 無效數據 (-999)，UI 將顯示 --:--")
            }

            if let sunsetDate = sunset {
                Logger.debug("日落時間（來自 current）: \(currentData.sunevents.sunset) -> \(sunsetDate)")
            } else {
                Logger.debug("日落時間（來自 current）: 無效數據 (-999)，UI 將顯示 --:--")
            }
        }
        
        // 轉換每小時預報，並將 current 數據作為第一筆
        var hourlyForecasts: [HourlyForecast] = []
        var dailyForecasts: [Forecast] = []

        // 1. 首先添加當前天氣作為第一筆（顯示為 "NOW"）
        let currentTime = parseFlexibleDate(currentData.timestamp)
        let currentHourlyForecast = HourlyForecast(
            dt: Int(currentTime.timeIntervalSince1970),
            time: currentTime,
            temperature: temperature,
            condition: condition,
            iconCode: iconCode,
            precipitationProbability: precipitationProbability,
            humidity: humidity,
            feelsLike: feelsLike,
            pressure: pressure,
            seaLevelPressure: seaLevelPressure,
            groundLevelPressure: groundLevelPressure,
            windSpeed: windSpeed,
            windDirection: windDirection,
            windGust: windGust,
            cloudiness: currentData.clouds.coverage,
            visibility: visibility,
            rainVolume: rainVolume,
            snowVolume: snowVolume,
            uvIndex: uvIndex
        )
        hourlyForecasts.append(currentHourlyForecast)
        Logger.debug("添加當前天氣作為第一筆時間軸數據: \(currentTime)")

        // 2. 處理預報數據
        if let dailyData = dailyForecast?.daily {
            Logger.debug("開始轉換每日預報，數據筆數: \(dailyData.count)")

            // 轉換每日預報數據
            dailyForecasts = dailyData.map { item in
                let time = parseFlexibleDate(item.timestamp)
                Logger.debug("轉換每日預報時間戳: \(item.timestamp) -> \(time)")

                // 處理可能為 -999 的數值
                let precipitationProbability = item.precipitation.probability == -999 ? 0 : item.precipitation.probability

                return Forecast(
                    date: time,
                    highTemperature: Int(item.temperature.max.rounded()),
                    lowTemperature: Int(item.temperature.min.rounded()),
                    condition: item.weather.condition,
                    iconCode: item.weather.icon,
                    precipitationProbability: precipitationProbability,
                    humidity: item.atmosphere.humidity
                )
            }

            // 從每日預報 API 資料直接生成時間軸資料
            let generatedHourlyData = generateHourlyForecastsFromDailyAPI(dailyData)
            hourlyForecasts.append(contentsOf: generatedHourlyData)
            Logger.debug("完成每日預報轉換，總共生成 \(dailyForecasts.count) 筆每日資料，\(hourlyForecasts.count) 筆時間軸資料（包含當前天氣）")

        } else if let hourlyData = hourlyForecast?.hourly {
            Logger.debug("開始轉換每小時預報，數據筆數: \(hourlyData.count)")
            let hourlyForecastData = hourlyData.map { item in
                let time = parseFlexibleDate(item.timestamp)
                Logger.debug("轉換時間戳: \(item.timestamp) -> \(time)")

                // 處理可能為 -999 的數值
                let precipitationProbability = item.precipitation.probability == -999 ? 0 : item.precipitation.probability
                let pressure = item.atmosphere.sea_level == -999 ? 1013.25 : item.atmosphere.sea_level
                let seaLevelPressure = item.atmosphere.sea_level == -999 ? 1013.25 : item.atmosphere.sea_level
                let groundLevelPressure = item.atmosphere.ground_level
                let visibility = item.atmosphere.visibility == -999 ? 10.0 : item.atmosphere.visibility
                let windDirection = item.wind.direction == -999 ? 0.0 : item.wind.direction
                let windGust = (item.wind.gust == -999 || item.wind.gust == -99) ? 0.0 : item.wind.gust
                let rainVolume = (item.precipitation.rain_amount == -999 || item.precipitation.rain_amount == -99) ? 0.0 : item.precipitation.rain_amount
                let snowVolume = (item.precipitation.snow_amount == -999 || item.precipitation.snow_amount == -99) ? 0.0 : item.precipitation.snow_amount
                let uvIndex = item.uv.index == -999 ? nil : item.uv.index

                return HourlyForecast(
                    dt: Int(time.timeIntervalSince1970),
                    time: time,
                    temperature: Int(item.temperature.current.rounded()),
                    condition: item.weather.condition,
                    iconCode: item.weather.icon,
                    precipitationProbability: precipitationProbability,
                    humidity: item.atmosphere.humidity,
                    feelsLike: Int(item.temperature.feels_like.rounded()),
                    pressure: pressure,
                    seaLevelPressure: seaLevelPressure,
                    groundLevelPressure: groundLevelPressure,
                    windSpeed: item.wind.speed,
                    windDirection: windDirection,
                    windGust: windGust,
                    cloudiness: item.clouds.coverage,
                    visibility: visibility,
                    rainVolume: rainVolume,
                    snowVolume: snowVolume,
                    uvIndex: uvIndex
                )
            }

            // 添加到 hourlyForecasts 中
            hourlyForecasts.append(contentsOf: hourlyForecastData)
            Logger.debug("完成每小時預報轉換，總共生成 \(hourlyForecasts.count) 筆資料（包含當前天氣）")

            // 生成每日預報（從每小時預報中提取）
            dailyForecasts = generateDailyForecastsFromHourly(hourlyForecasts)
        } else {
            Logger.debug("沒有預報數據，只有當前天氣")
            // 如果沒有預報數據，創建空的每日預報
            dailyForecasts = []
        }
        
        let weatherData = WeatherData(
            location: locationName,
            temperature: temperature,
            condition: condition,
            humidity: humidity,
            windSpeed: windSpeed,
            precipitationProbability: precipitationProbability,
            feelsLike: feelsLike,
            iconCode: iconCode,
            sunrise: sunrise,
            sunset: sunset,
            forecast: dailyForecasts,
            hourlyForecast: hourlyForecasts,
            timezoneOffset: 0, // 暫時使用 UTC+0
            pressure: pressure,
            seaLevelPressure: seaLevelPressure,
            groundLevelPressure: groundLevelPressure,
            windDirection: windDirection,
            windGust: windGust,
            cloudiness: currentData.clouds.coverage,
            visibility: visibility,
            rainVolume: rainVolume,
            snowVolume: snowVolume,
            uvIndex: uvIndex
        )
        
        Logger.debug("WeatherData 創建完成，每小時預報數量: \(weatherData.hourlyForecast.count)")
        return weatherData
    }
    
    /// 從每小時預報生成每日預報
    /// - Parameter hourlyForecasts: 每小時預報列表
    /// - Returns: 每日預報列表
    private func generateDailyForecastsFromHourly(_ hourlyForecasts: [HourlyForecast]) -> [Forecast] {
        let calendar = Calendar.current
        var groupedForecasts: [String: [HourlyForecast]] = [:]
        
        // 按日期分組
        for forecast in hourlyForecasts {
            let dateKey = calendar.dateInterval(of: .day, for: forecast.time)?.start ?? forecast.time
            let key = DateFormatter().string(from: dateKey)
            
            if groupedForecasts[key] == nil {
                groupedForecasts[key] = []
            }
            groupedForecasts[key]?.append(forecast)
        }
        
        // 生成每日預報
        var dailyForecasts: [Forecast] = []
        let sortedKeys = groupedForecasts.keys.sorted()
        
        for key in sortedKeys.prefix(5) { // 只取前5天
            guard let dayForecasts = groupedForecasts[key], !dayForecasts.isEmpty else { continue }
            
            let highTemp = dayForecasts.max { $0.temperature < $1.temperature }?.temperature ?? 0
            let lowTemp = dayForecasts.min { $0.temperature < $1.temperature }?.temperature ?? 0
            
            // 使用中午時段的天氣狀況
            let noonForecast = dayForecasts.first { forecast in
                let hour = calendar.component(.hour, from: forecast.time)
                return hour >= 12 && hour <= 15
            } ?? dayForecasts.first
            
            let condition = noonForecast?.condition ?? "未知"
            let iconCode = noonForecast?.iconCode ?? "01d"
            
            // 計算平均降雨概率和濕度
            let avgPop = dayForecasts.reduce(0) { $0 + $1.precipitationProbability } / dayForecasts.count
            let avgHumidity = dayForecasts.reduce(0) { $0 + $1.humidity } / dayForecasts.count
            
            if let firstForecast = dayForecasts.first {
                let forecast = Forecast(
                    date: firstForecast.time,
                    highTemperature: highTemp,
                    lowTemperature: lowTemp,
                    condition: condition,
                    iconCode: iconCode,
                    precipitationProbability: avgPop,
                    humidity: avgHumidity
                )
                dailyForecasts.append(forecast)
            }
        }
        
        return dailyForecasts
    }

    /// 從每日預報 API 資料直接轉換為時間軸顯示用的每小時預報
    /// - Parameter dailyData: 每日預報 API 資料
    /// - Returns: 每小時預報列表
    private func generateHourlyForecastsFromDailyAPI(_ dailyData: [MiddlewareDailyItem]) -> [HourlyForecast] {
        var hourlyForecasts: [HourlyForecast] = []

        for item in dailyData {
            let time = parseFlexibleDate(item.timestamp)
            Logger.debug("轉換每日預報時間戳: \(item.timestamp) -> \(time)")

            // 處理可能為 -999 的數值
            let precipitationProbability = item.precipitation.probability == -999 ? 0 : item.precipitation.probability
            let pressure = item.atmosphere.sea_level == -999 ? 1013.25 : item.atmosphere.sea_level
            let seaLevelPressure = item.atmosphere.sea_level == -999 ? 1013.25 : item.atmosphere.sea_level
            let groundLevelPressure = item.atmosphere.ground_level == -999 ? 1011.0 : item.atmosphere.ground_level
            let visibility = item.atmosphere.visibility == -999 ? 10.0 : item.atmosphere.visibility
            let windDirection = item.wind.direction == -999 ? 0.0 : item.wind.direction
            let windGust = (item.wind.gust == -999 || item.wind.gust == -99) ? 0.0 : item.wind.gust
            let rainVolume = (item.precipitation.rain_amount == -999 || item.precipitation.rain_amount == -99) ? 0.0 : item.precipitation.rain_amount
            let snowVolume = (item.precipitation.snow_amount == -999 || item.precipitation.snow_amount == -99) ? 0.0 : item.precipitation.snow_amount

            // 使用 API 提供的 current 溫度，如果沒有則使用平均溫度
            let temperature = Int(item.temperature.current.rounded())
            let feelsLike = item.temperature.feels_like == -999 ? temperature : Int(item.temperature.feels_like.rounded())

            let hourlyForecast = HourlyForecast(
                dt: Int(time.timeIntervalSince1970),
                time: time,
                temperature: temperature,
                condition: item.weather.condition,
                iconCode: item.weather.icon,
                precipitationProbability: precipitationProbability,
                humidity: item.atmosphere.humidity,
                feelsLike: feelsLike,
                pressure: pressure,
                seaLevelPressure: seaLevelPressure,
                groundLevelPressure: groundLevelPressure,
                windSpeed: item.wind.speed,
                windDirection: windDirection,
                windGust: windGust,
                cloudiness: item.clouds.coverage,
                visibility: visibility,
                rainVolume: rainVolume,
                snowVolume: snowVolume
            )

            hourlyForecasts.append(hourlyForecast)
        }

        Logger.debug("從 \(dailyData.count) 筆每日 API 資料生成 \(hourlyForecasts.count) 筆時間軸資料")
        return hourlyForecasts
    }

    /// 創建預設的天氣數據
    /// - Parameter locationName: 位置名稱
    /// - Returns: 預設的 WeatherData
    private func createDefaultWeatherData(locationName: String) -> WeatherData {
        return WeatherData(
            location: locationName,
            temperature: 20,
            condition: "未知",
            humidity: 50,
            windSpeed: 0,
            precipitationProbability: 0,
            feelsLike: 20,
            iconCode: "01d",
            sunrise: Date(),
            sunset: Date(),
            forecast: [],
            hourlyForecast: [],
            timezoneOffset: 0
        )
    }
    
    /// 創建靈活的日期格式化器，支援多種 ISO 8601 格式
    /// - Returns: 能夠解析不同時間格式的 DateFormatter
    private func createFlexibleDateFormatter() -> DateFormatter {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        
        // 支援多種 ISO 8601 格式
        let formats = [
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",     // OpenWeather: 2025-07-09T09:00:00.000Z
            "yyyy-MM-dd'T'HH:mm:ss'Z'",         // 標準 ISO: 2025-07-09T09:00:00Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ",   // 完整 ISO: 2025-07-09T09:00:00.000+00:00
            "yyyy-MM-dd'T'HH:mm:ssZZZZZ"        // 基本 ISO: 2025-07-09T09:00:00+00:00
        ]
        
        formatter.dateFormat = formats[0] // 預設使用第一種格式
        return formatter
    }
    
    /// 靈活解析日期字符串
    /// - Parameter dateString: 日期字符串
    /// - Returns: 解析後的 Date 物件，失敗則返回當前時間
    private func parseFlexibleDate(_ dateString: String) -> Date {
        let formats = [
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",     // OpenWeather: 2025-07-09T09:00:00.000Z
            "yyyy-MM-dd'T'HH:mm:ss'Z'",         // 標準 ISO: 2025-07-09T09:00:00Z
            "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ",   // 完整 ISO: 2025-07-09T09:00:00.000+00:00
            "yyyy-MM-dd'T'HH:mm:ssZZZZZ"        // 基本 ISO: 2025-07-09T09:00:00+00:00
        ]
        
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(abbreviation: "UTC")
        
        for format in formats {
            formatter.dateFormat = format
            if let date = formatter.date(from: dateString) {
                return date
            }
        }
        
        Logger.warning("無法解析時間格式: \(dateString)，使用當前時間")
        return Date()
    }
    
    // MARK: - 模擬數據方法（保持原有的模擬數據邏輯）
    
    /// 獲取模擬天氣數據
    /// - Parameters:
    ///   - location: 位置名稱
    ///   - completion: 完成回調
    private func getMockWeatherForLocation(_ location: String, completion: @escaping (Result<WeatherData, WeatherError>) -> Void) {
        // 模擬網路延遲
        DispatchQueue.global().asyncAfter(deadline: .now() + 1.0) {
            let mockWeatherData = self.createMockWeatherData(for: location)
            completion(.success(mockWeatherData))
        }
    }
    
    /// 創建模擬天氣數據
    /// - Parameter location: 位置名稱
    /// - Returns: 模擬的 WeatherData
    private func createMockWeatherData(for location: String) -> WeatherData {
        let calendar = Calendar.current
        let now = Date()
        
        // 生成模擬的每小時預報
        var hourlyForecasts: [HourlyForecast] = []
        for i in 0..<40 {
            let time = calendar.date(byAdding: .hour, value: i * 3, to: now) ?? now
            let temperature = Int.random(in: 15...25)
            let hourlyForecast = HourlyForecast(
                dt: Int(time.timeIntervalSince1970),
                time: time,
                temperature: temperature,
                condition: "多雲",
                iconCode: "04d",
                precipitationProbability: Int.random(in: 0...30),
                humidity: Int.random(in: 40...80),
                feelsLike: temperature + Int.random(in: -2...2),
                pressure: Double.random(in: 1010...1020),
                seaLevelPressure: Double.random(in: 1010...1020),
                groundLevelPressure: Double.random(in: 1005...1015),
                windSpeed: Double.random(in: 0...10),
                windDirection: Double.random(in: 0...360),
                windGust: Double.random(in: 0...15),
                cloudiness: Int.random(in: 20...80),
                visibility: Double.random(in: 5...15),
                rainVolume: 0,
                snowVolume: 0
            )
            hourlyForecasts.append(hourlyForecast)
        }
        
        // 生成模擬的每日預報
        var dailyForecasts: [Forecast] = []
        for i in 0..<5 {
            let date = calendar.date(byAdding: .day, value: i, to: now) ?? now
            let forecast = Forecast(
                date: date,
                highTemperature: Int.random(in: 20...30),
                lowTemperature: Int.random(in: 10...20),
                condition: "多雲",
                iconCode: "04d",
                precipitationProbability: Int.random(in: 0...30),
                humidity: Int.random(in: 40...80)
            )
            dailyForecasts.append(forecast)
        }
        
        return WeatherData(
            location: location,
            temperature: 22,
            condition: "多雲",
            humidity: 65,
            windSpeed: 5.2,
            precipitationProbability: 20,
            feelsLike: 24,
            iconCode: "04d",
            sunrise: calendar.date(bySettingHour: 6, minute: 30, second: 0, of: now) ?? now,
            sunset: calendar.date(bySettingHour: 18, minute: 30, second: 0, of: now) ?? now,
            forecast: dailyForecasts,
            hourlyForecast: hourlyForecasts,
            timezoneOffset: 28800, // UTC+8
            pressure: 1013.25,
            seaLevelPressure: 1013.25,
            groundLevelPressure: 1011.0,
            windDirection: 230,
            windGust: 7.5,
            cloudiness: 75,
            visibility: 10.0,
            rainVolume: 0,
            snowVolume: 0,
            uvIndex: 6
        )
    }
}

// MARK: - 中繼 API 回應模型

/// 中繼 API 即時天氣回應
struct MiddlewareCurrentResponse: Codable {
    let current: [MiddlewareCurrentItem]
    let metadata: MiddlewareMetadata?
}

struct MiddlewareCurrentItem: Codable {
    let timestamp: String
    let weather: MiddlewareWeather
    let temperature: MiddlewareTemperature
    let atmosphere: MiddlewareAtmosphere
    let wind: MiddlewareWind
    let precipitation: MiddlewarePrecipitation
    let clouds: MiddlewareClouds
    let uv: MiddlewareUV
    let sunevents: MiddlewareSunEvents
}

/// 中繼 API 每小時預報回應
struct MiddlewareHourlyResponse: Codable {
    let hourly: [MiddlewareHourlyItem]
    let metadata: MiddlewareMetadata?
}

struct MiddlewareHourlyItem: Codable {
    let timestamp: String
    let weather: MiddlewareWeather
    let temperature: MiddlewareTemperature
    let atmosphere: MiddlewareAtmosphere
    let wind: MiddlewareWind
    let precipitation: MiddlewarePrecipitation
    let clouds: MiddlewareClouds
    let uv: MiddlewareUV
    let sunevents: MiddlewareSunEvents
}

/// 中繼 API 每日預報回應
struct MiddlewareDailyResponse: Codable {
    let daily: [MiddlewareDailyItem]
    let metadata: MiddlewareMetadata?
}

struct MiddlewareDailyItem: Codable {
    let timestamp: String
    let weather: MiddlewareWeather
    let temperature: MiddlewareTemperature
    let atmosphere: MiddlewareAtmosphere
    let wind: MiddlewareWind
    let precipitation: MiddlewarePrecipitation
    let clouds: MiddlewareClouds
    let uv: MiddlewareUV
    let sunevents: MiddlewareSunEvents
}

// MARK: - 中繼 API 共用模型

struct MiddlewareWeather: Codable {
    let condition: String
    let icon: String
}

struct MiddlewareTemperature: Codable {
    let current: Double
    let feels_like: Double
    let min: Double
    let max: Double
}

struct MiddlewareAtmosphere: Codable {
    let humidity: Int
    let sea_level: Double
    let ground_level: Double
    let visibility: Double
}

struct MiddlewareWind: Codable {
    let speed: Double
    let direction: Double
    let gust: Double
}

struct MiddlewarePrecipitation: Codable {
    let rain_amount: Double
    let snow_amount: Double
    let probability: Int
}

struct MiddlewareClouds: Codable {
    let coverage: Int
}

struct MiddlewareUV: Codable {
    let index: Int
}

struct MiddlewareSunEvents: Codable {
    let sunrise: SunTime
    let sunset: SunTime
    
    enum SunTime: Codable {
        case string(String)
        case number(Double)
        
        init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            if let stringValue = try? container.decode(String.self) {
                self = .string(stringValue)
            } else if let doubleValue = try? container.decode(Double.self) {
                self = .number(doubleValue)
            } else {
                throw DecodingError.typeMismatch(SunTime.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Expected String or Double"))
            }
        }
        
        func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            switch self {
            case .string(let value):
                try container.encode(value)
            case .number(let value):
                try container.encode(value)
            }
        }
        
        var isValid: Bool {
            switch self {
            case .string(let value):
                return !value.isEmpty
            case .number(let value):
                return value != -999 && value != -99
            }
        }
        
        var dateValue: Date? {
            switch self {
            case .string(let value):
                if value.isEmpty { 
                    return nil 
                }
                
                // 首先嘗試使用正確配置的 ISO8601DateFormatter
                let isoFormatter = ISO8601DateFormatter()
                isoFormatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
                if let date = isoFormatter.date(from: value) {
                    return date
                }
                
                // 如果 ISO8601 失敗，使用多種格式嘗試解析
                let formats = [
                    "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",     // 2025-07-09T21:11:00.000Z
                    "yyyy-MM-dd'T'HH:mm:ss'Z'",         // 2025-07-09T21:11:00Z
                    "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ",   // 2025-07-09T21:11:00.000+00:00
                    "yyyy-MM-dd'T'HH:mm:ssZZZZZ"        // 2025-07-09T21:11:00+00:00
                ]
                
                let formatter = DateFormatter()
                formatter.locale = Locale(identifier: "en_US_POSIX")
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                
                for format in formats {
                    formatter.dateFormat = format
                    if let date = formatter.date(from: value) {
                        return date
                    }
                }
                
                return nil
            case .number(let value):
                if value == -999 || value == -99 { 
                    return nil 
                }
                return Date(timeIntervalSince1970: value)
            }
        }
    }
}

struct MiddlewareMetadata: Codable {
    let provider: String?
    let api_type: String?
    let response_code: ResponseCode?
    let base: String?
    let plan: String?
    
    enum ResponseCode: Codable {
        case int(Int)
        case string(String)
        
        init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            if let intValue = try? container.decode(Int.self) {
                self = .int(intValue)
            } else if let stringValue = try? container.decode(String.self) {
                self = .string(stringValue)
            } else {
                throw DecodingError.typeMismatch(ResponseCode.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Expected Int or String"))
            }
        }
        
        func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            switch self {
            case .int(let value):
                try container.encode(value)
            case .string(let value):
                try container.encode(value)
            }
        }
        
        var value: String {
            switch self {
            case .int(let intValue):
                return String(intValue)
            case .string(let stringValue):
                return stringValue
            }
        }
    }
}

// MARK: - 原有的 OpenWeather API 模型（保留用於向後相容）

/// 地理編碼結果
struct GeocodingResult: Codable {
    let name: String
    let local_names: [String: String]?
    let lat: Double
    let lon: Double
    let country: String
    let state: String?
} 

