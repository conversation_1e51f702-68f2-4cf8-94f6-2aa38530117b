import Foundation
import RevenueCat
import StoreKit
import SwiftUI

// 定義通知名稱
extension Notification.Name {
    static let proStatusChanged = Notification.Name("proStatusChanged")
    static let iapServiceInitialized = Notification.Name("iapServiceInitialized")
}

/// IAP 服務類，處理應用內購買和 RevenueCat 整合
class IAPService: NSObject, ObservableObject {
    // MARK: - 單例設計模式
    static let shared = IAPService()
    
    // MARK: - 屬性
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    @Published var isPro: Bool = false
    
    // 用於監聽 CustomerInfo 更新的回調
    var customerInfoUpdateHandler: ((CustomerInfo) -> Void)?
    
    // API Keys - 使用環境變數或配置文件替換
    private let apiKey = "appl_zubhLRXYLLuvcGKiPhAHQHHUZwe" // TODO: 替換為你的 API Key
    
    // 訂閱權益 ID
    private let proEntitlementId = "pro"
    
    // MARK: - 初始化
    private override init() {
        super.init()
        configureRevenueCat()
        updateCustomerInfo()
    }
    
    // MARK: - RevenueCat 配置
    private func configureRevenueCat() {
        // 設置 RevenueCat 的 debug 日誌級別
        Purchases.logLevel = .info
        
        // 配置 RevenueCat SDK
        Purchases.configure(withAPIKey: apiKey)
        
        // 設置代理以接收更新
        Purchases.shared.delegate = self
        
        // 立即檢查購買狀態
        updateCustomerInfo()
    }
    
    // MARK: - 顧客信息更新
    func updateCustomerInfo() {
        Purchases.shared.getCustomerInfo { [weak self] (customerInfo, error) in
            guard let self = self else { return }
            
            if let error = error {
                print("獲取顧客信息錯誤: \(error.localizedDescription)")
                return
            }
            
            DispatchQueue.main.async {
                self.customerInfo = customerInfo
                // 檢查 Pro 訂閱狀態
                self.updateProStatus(with: customerInfo)
                
                // 發送通知，確保所有依賴 IAPService 的組件都能更新
                NotificationCenter.default.post(name: .iapServiceInitialized, object: nil)
            }
        }
    }
    
    // 檢查並更新 Pro 訂閱狀態
    private func updateProStatus(with customerInfo: CustomerInfo?) {
        guard let customerInfo = customerInfo else {
            self.isPro = false
            return
        }
        
        let oldValue = self.isPro
        // 檢查用戶是否有 pro 權益
        self.isPro = customerInfo.entitlements[proEntitlementId]?.isActive == true
        
        // 如果 Pro 狀態發生變化，發送通知
        if oldValue != self.isPro {
            NotificationCenter.default.post(name: .proStatusChanged, object: nil)
        }
    }
    
    // MARK: - 檢查訂閱狀態
    
    /// 檢查特定權益是否啟用
    /// - Parameter entitlementId: 權益 ID
    /// - Returns: 是否啟用
    func isEntitlementActive(_ entitlementId: String = "pro") -> Bool {
        return customerInfo?.entitlements[entitlementId]?.isActive == true
    }
    
    /// 檢查用戶是否有任何啟用的權益
    /// - Returns: 是否有任何啟用的權益
    func hasActiveEntitlements() -> Bool {
        return !(customerInfo?.entitlements.active.isEmpty ?? true)
    }
    
    /// 獲取訂閱過期日期
    /// - Parameter entitlementId: 權益 ID
    /// - Returns: 過期日期（如果存在）
    func getExpirationDate(for entitlementId: String = "pro") -> Date? {
        return customerInfo?.entitlements[entitlementId]?.expirationDate
    }
    
    /// 獲取訂閱信息詳情
    /// - Parameter entitlementId: 權益 ID
    /// - Returns: 權益信息
    func getEntitlementInfo(for entitlementId: String = "pro") -> EntitlementInfo? {
        return customerInfo?.entitlements[entitlementId]
    }
    
    // MARK: - 獲取商品信息
    func getOfferings(completion: ((Offerings?) -> Void)? = nil) {
        Purchases.shared.getOfferings { [weak self] (offerings, error) in
            guard let self = self else { return }
            
            if let error = error {
                print("獲取商品錯誤: \(error.localizedDescription)")
                completion?(nil)
                return
            }
            
            DispatchQueue.main.async {
                self.offerings = offerings
                completion?(offerings)
            }
        }
    }
    
    // MARK: - 購買產品
    func purchasePackage(_ package: Package, completion: @escaping (Bool, CustomerInfo?, Error?) -> Void) {
        Purchases.shared.purchase(package: package) { [weak self] (transaction, customerInfo, error, userCancelled) in
            guard let self = self else { return }
            
            if userCancelled {
                completion(false, nil, nil)
                return
            }
            
            if let error = error {
                completion(false, nil, error)
                return
            }
            
            DispatchQueue.main.async {
                self.customerInfo = customerInfo
                self.updateProStatus(with: customerInfo)
                completion(true, customerInfo, nil)
            }
        }
    }
    
    // MARK: - 恢復購買
    func restorePurchases(completion: @escaping (Bool, Error?) -> Void) {
        Purchases.shared.restorePurchases { [weak self] (customerInfo, error) in
            guard let self = self else { return }
            
            if let error = error {
                completion(false, error)
                return
            }
            
            DispatchQueue.main.async {
                self.customerInfo = customerInfo
                self.updateProStatus(with: customerInfo)
                completion(true, nil)
            }
        }
    }
}

// MARK: - RevenueCat 代理實現
extension IAPService: PurchasesDelegate {
    func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.customerInfo = customerInfo
            self.updateProStatus(with: customerInfo)
            
            // 調用註冊的更新處理程序
            self.customerInfoUpdateHandler?(customerInfo)
        }
    }
} 