//
//  GoogleTimezoneService.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/15.
//

import Foundation

/// Google Timezone API 服務
class GoogleTimezoneService {
    
    /// 單例實例
    static let shared = GoogleTimezoneService()
    
    /// 錯誤類型
    enum TimezoneError: Error {
        case invalidURL
        case networkError
        case invalidResponse
        case decodingError
        case apiError(String)
        case noApiKey
        
        var message: String {
            switch self {
            case .invalidURL:
                return "無效的URL"
            case .networkError:
                return "網路連接錯誤"
            case .invalidResponse:
                return "無效的回應"
            case .decodingError:
                return "數據解析錯誤"
            case .apiError(let message):
                return "API錯誤: \(message)"
            case .noApiKey:
                return "缺少API金鑰"
            }
        }
    }
    
    /// Google Timezone API 金鑰
    private let apiKey: String = {
        guard let apiKey = Bundle.main.object(forInfoDictionaryKey: "GoogleTimezoneAPIKey") as? String, !apiKey.isEmpty else {
            print("警告：無法從Info.plist讀取Google Timezone API key")
            return "YOUR_API_KEY"
        }
        print("Google Timezone API Key已載入: \(String(apiKey.prefix(10)))...")
        return apiKey
    }()
    
    /// 基礎URL
    private let baseURL = "https://maps.googleapis.com/maps/api/timezone/json"
    
    /// URLSession 配置 - 修復iOS 18.4模擬器網路問題
    private lazy var urlSession: URLSession = {
        #if targetEnvironment(simulator)
        // 使用ephemeral配置修復模擬器網路問題
        print("🔧 使用ephemeral URLSession配置修復模擬器網路問題")
        return URLSession(configuration: .ephemeral)
        #else
        return URLSession.shared
        #endif
    }()
    
    private init() {}
    
    /// 根據經緯度獲取時區資訊
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - completion: 完成回調，返回時區ID或錯誤
    func getTimezone(lat: Double, lon: Double, completion: @escaping (Result<String, TimezoneError>) -> Void) {
        guard apiKey != "YOUR_API_KEY" else {
            print("Google Timezone API: 缺少有效的API金鑰，請在Info.plist中設置GoogleTimezoneAPIKey")
            completion(.failure(.noApiKey))
            return
        }
        
        let timestamp = Int(Date().timeIntervalSince1970)
        let urlString = "\(baseURL)?location=\(lat),\(lon)&timestamp=\(timestamp)&key=\(apiKey)"
        
        guard let url = URL(string: urlString) else {
            completion(.failure(.invalidURL))
            return
        }
        
        print("Google Timezone API 請求URL: \(urlString)")
        
        // 創建請求並設置模擬器修復選項
        var request = URLRequest(url: url)

        // 添加 iOS Bundle Identifier 標頭以符合 API 安全性限制
        if let bundleIdentifier = Bundle.main.bundleIdentifier {
            request.setValue(bundleIdentifier, forHTTPHeaderField: "X-Ios-Bundle-Identifier")
            print("Google Timezone API 設置 Bundle Identifier: \(bundleIdentifier)")
        } else {
            print("警告：無法獲取 Bundle Identifier")
        }

        #if targetEnvironment(simulator)
        // 禁用HTTP/3以修復iOS 18.4模擬器問題
        request.assumesHTTP3Capable = false
        print("🔧 禁用HTTP/3以修復iOS 18.4模擬器問題")
        #endif
        
        urlSession.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Google Timezone API 網路錯誤: \(error.localizedDescription)")
                completion(.failure(.networkError))
                return
            }
            
            guard let data = data else {
                print("Google Timezone API: 沒有收到數據")
                completion(.failure(.invalidResponse))
                return
            }
            
            // 打印原始回應以便調試
            if let responseString = String(data: data, encoding: .utf8) {
                print("Google Timezone API 原始回應: \(responseString)")
            }
            
            do {
                let response = try JSONDecoder().decode(TimezoneResponse.self, from: data)
                
                if response.status == "OK" {
                    print("Google Timezone API 成功，時區ID: \(response.timeZoneId)")
                    completion(.success(response.timeZoneId))
                } else {
                    print("Google Timezone API 錯誤狀態: \(response.status)")
                    completion(.failure(.apiError(response.status)))
                }
            } catch {
                print("Google Timezone API 數據解析錯誤: \(error)")
                completion(.failure(.decodingError))
            }
        }.resume()
    }
}

// MARK: - 回應模型

/// Google Timezone API 回應模型
struct TimezoneResponse: Codable {
    let dstOffset: Int
    let rawOffset: Int
    let status: String
    let timeZoneId: String
    let timeZoneName: String
} 