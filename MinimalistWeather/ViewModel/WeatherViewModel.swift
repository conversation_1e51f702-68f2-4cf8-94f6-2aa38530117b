//
//  WeatherViewModel.swift
//  MinimalistWeather
//
//  Created by Ke<PERSON> on 2025/4/15.
//

import Foundation
import SwiftUI
import Combine
import CoreLocation
import WidgetKit

class WeatherViewModel: ObservableObject {
    // MARK: - 發布屬性
    @Published var currentLocation: String = ""  // 不預設為台北市
    @Published var displayLocation: String = ""  // 不預設位置名稱
    @Published var currentTemperature: String = "--°C"
    @Published var temperatureNumber: String = "--" // 無數據時的溫度顯示
    @Published var weatherCondition: String = "--"
    @Published var historyEntries: [LocationEntry] = []
    @Published var savedLocations: [SavedLocation] = []
    @Published var currentSavedLocation: SavedLocation?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var temperatureUnit: TemperatureUnit = .celsius
    @Published var timeFormat: TimeFormat = .twentyFourHour
    @Published var measurementSystem: MeasurementSystem = .metric
    @Published var themeMode: ThemeMode = .light
    @Published var forecasts: [ForecastViewModel] = []
    @Published var tempRangeString: String = "--°C - --°C"
    @Published var humidityString: String = "--%"
    @Published var precipitationString: String = "--%"
    @Published var weatherIconName: String = "-"
    @Published var locationSuggestions: [GeocodingResult] = []
    
    // 新增每三小時預報相關屬性
    @Published var hourlyForecasts: [HourlyForecastViewModel] = []
    @Published var showHourlyForecast: Bool = false
    
    // 新增五日預報相關屬性
    @Published var isShowingHourlyData: Bool = true

    // 預報模式（從設定載入）
    @Published var forecastMode: ForecastMode = .hourly
    
    // 主頁面專用數據
    @Published var temperatureChartData: [TemperaturePoint] = []
    @Published var timelinePreviewData: [TimelineHourlyData] = []
    
    // 新增：統一的數據處理結果
    @Published var forecastDateTimes: [String] = []
    @Published var timeLabels: [String] = []
    
    // 新增：溫度單位變更通知
    @Published var didChangeTemperatureUnit: Bool = false
    
    // 主頁面數據緩存
    private var lastMainPageUpdateHash: String = ""
    private var lastMainPageUpdateTime: Date?
    
    // 新增：時區信息和當前地點時間
    @Published var timezone: String? = nil
    @Published var currentLocationTime: Date? = nil
    
    // 新增：上次更新時間
    @Published var lastUpdatedTime: Date? = nil

    // 新增：自動重試相關屬性
    @Published var retryCount: Int = 0
    @Published var isAutoRetrying: Bool = false
    private let maxRetryCount: Int = 1 // 最多自動重試1次
    private var autoRetryTimer: Timer?

    // MARK: - 私有屬性
    internal var currentWeatherData: WeatherData?
    private var cancellables = Set<AnyCancellable>()
    private var currentCoordinates: (lat: Double, lon: Double)?
    
    // 新增：自動刷新計時器
    private var autoRefreshTimer: Timer?
    private let autoRefreshInterval: TimeInterval = 10 * 60 // 10分鐘
    
    // MARK: - 服務
    private let weatherService = WeatherService()
    private let locationRepository = LocationRepository()
    private let appSettings = AppSettings.shared
    private let iapService = IAPService.shared // 加入 IAP 服務
    
    // MARK: - 初始化
    init() {
        // 從設置載入溫度單位、時間格式、測量單位、主題模式和預報模式
        temperatureUnit = appSettings.temperatureUnit
        timeFormat = appSettings.timeFormat
        measurementSystem = appSettings.measurementSystem
        themeMode = appSettings.themeMode
        forecastMode = appSettings.forecastMode

        // 同步預報模式到 isShowingHourlyData
        isShowingHourlyData = forecastMode.isHourly
        Logger.debug("🔧 從設定載入預報模式: \(forecastMode.displayName)")

        // 同步設定到 App Group 供 Widget 使用
        appSettings.syncSettingsToAppGroups()
        
        // 訂閱溫度單位變化
        $temperatureUnit
            .sink { [weak self] unit in
                guard let self = self else { return }
                // 儲存設定
                self.appSettings.temperatureUnit = unit
                // 更新顯示
                self.updateTemperatureDisplay()
                // 更新預報數據顯示
                if let forecasts = self.currentWeatherData?.forecast {
                    self.updateForecastsData(forecasts)
                }
            }
            .store(in: &cancellables)
        
        // 訂閱時間格式變化
        $timeFormat
            .sink { [weak self] format in
                guard let self = self else { return }
                // 儲存設定
                self.appSettings.timeFormat = format
                // 更新時間顯示
                self.updateTimeDisplay()
            }
            .store(in: &cancellables)
        
        // 訂閱測量單位變化
        $measurementSystem
            .sink { [weak self] system in
                guard let self = self else { return }
                // 儲存設定
                self.appSettings.measurementSystem = system
                // 更新顯示（如果有當前天氣數據）
                if let weather = self.currentWeatherData {
                    self.updateWeatherDisplay(weather)
                }
            }
            .store(in: &cancellables)
        
        // 訂閱主題模式變化
        $themeMode
            .sink { [weak self] theme in
                guard let self = self else { return }
                // 儲存設定
                self.appSettings.themeMode = theme
            }
            .store(in: &cancellables)

        // 訂閱預報模式變化
        $forecastMode
            .sink { [weak self] mode in
                guard let self = self else { return }
                // 儲存設定
                self.appSettings.forecastMode = mode
                // 同步到 isShowingHourlyData
                self.isShowingHourlyData = mode.isHourly
                Logger.debug("🔧 預報模式變化並儲存: \(mode.displayName)")
            }
            .store(in: &cancellables)
        
        // 監聽 IAP 狀態變化
        iapService.$isPro
            .dropFirst() // 忽略初始值
            .sink { [weak self] isPro in
                guard let self = self else { return }
                Logger.debug("IAP 狀態變化: \(isPro ? "升級到 Pro" : "降級到免費版")")

                // 如果降級到免費版，執行相關清理操作
                if !isPro {
                    // 清除 Widget 位置數據
                    self.clearWidgetLocationData()

                    // 將所有儲存位置的天氣來源設定為 OW
                    self.resetAllLocationWeatherSourcesToOW()

                    // 重置主題設定為免費的白天主題
                    self.resetThemeToFree()

                    // 確保使用第一個位置
                    if !self.savedLocations.isEmpty {
                        let firstLocation = self.savedLocations[0]
                        if self.currentSavedLocation?.id != firstLocation.id {
                            Logger.debug("免費使用者切換到第一個位置: \(firstLocation.name)")
                            self.useLocation(firstLocation)
                        }
                    }
                }

                // 當 IAP 狀態變化時，重新生成主頁面數據
                if let weather = self.currentWeatherData, !weather.hourlyForecast.isEmpty {
                    let timeZone = TimeZone(secondsFromGMT: weather.timezoneOffset) ?? TimeZone.current
                    self.updateMainPageData(weather, hourlyForecasts: weather.hourlyForecast, locationTimeZone: timeZone)
                }
            }
            .store(in: &cancellables)
        
        // 載入儲存的位置
        loadSavedLocations()

        // 檢查並重置主題設定（如果 IAP 已過期）
        checkAndResetThemeIfNeeded()

        Logger.debug("===== 初始化位置載入 =====")
        Logger.debug("儲存的位置數量: \(savedLocations.count)")
        for (index, location) in savedLocations.enumerated() {
            Logger.debug("位置 \(index): \(location.name) (ID: \(location.id))")
        }
        Logger.debug("========================")
        
        // 嘗試載入上次查看的位置
        if let lastLocationID = locationRepository.getLastViewedLocationID() {
            Logger.debug("上次查看的位置ID: \(lastLocationID)")
            // 先找到對應的位置
            if let lastLocation = savedLocations.first(where: { $0.id == lastLocationID }) {
                Logger.success("找到上次查看的位置: \(lastLocation.name)")
                
                // 如果是免費使用者且不是第一個位置，強制使用第一個位置
                if !iapService.isPro && !savedLocations.isEmpty && lastLocation.id != savedLocations[0].id {
                    Logger.debug("免費使用者強制使用第一個位置: \(savedLocations[0].name)")
                    useLocation(savedLocations[0])
                } else {
                    useLocation(lastLocation)
                }
                startAutoRefreshTimer()
            } else {
                Logger.error("找不到上次查看的位置ID: \(lastLocationID)，使用第一個位置")
                // 找不到上次查看的位置，使用第一個位置
                if !savedLocations.isEmpty, let firstSavedLocation = savedLocations.first {
                    useLocation(firstSavedLocation)
                    startAutoRefreshTimer()
                }
            }
        } else if !savedLocations.isEmpty, let firstSavedLocation = savedLocations.first {
            // 如果沒有上次查看的位置，但有儲存的位置，使用第一個儲存的位置
            Logger.debug("沒有上次查看的位置記錄，使用第一個位置: \(firstSavedLocation.name)")
            useLocation(firstSavedLocation)
            startAutoRefreshTimer()
        } else {
            // 沒有任何位置時，不執行任何操作
            Logger.debug("沒有任何儲存的位置，等待 onboarding 流程")
        }
        
        // 監聽應用程式生命週期通知
        registerForLifecycleNotifications()
    }
    
    deinit {
        // 停止所有計時器
        cleanupTimers()

        // 移除應用程式生命週期觀察者
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 自動刷新天氣
    
    /// 啟動自動刷新計時器
    private func startAutoRefreshTimer() {
        // 確保先停止舊的計時器
        stopAutoRefreshTimer()
        
        // 創建新的計時器
        autoRefreshTimer = Timer.scheduledTimer(withTimeInterval: autoRefreshInterval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.refreshWeatherData()
            Logger.debug("自動刷新計時器觸發，已更新天氣數據")
        }
        
        // 確保計時器在RunLoop中添加和運行
        if let timer = autoRefreshTimer {
            RunLoop.current.add(timer, forMode: .common)
        }
        
        Logger.debug("已啟動自動刷新計時器，間隔為\(autoRefreshInterval/60)分鐘")
    }
    
    /// 停止自動刷新計時器
    private func stopAutoRefreshTimer() {
        autoRefreshTimer?.invalidate()
        autoRefreshTimer = nil
        Logger.debug("已停止自動刷新計時器")
    }

    /// 清理所有計時器
    private func cleanupTimers() {
        stopAutoRefreshTimer()
        stopAutoRetryTimer()
    }
    
    /// 刷新當前天氣數據
    func refreshWeatherData() {
        if let coordinates = currentCoordinates {
            // 優先使用座標更新，因為更精確
            fetchWeatherForCoordinates(lat: coordinates.lat, lon: coordinates.lon, locationName: currentLocation)
        } else if !currentLocation.isEmpty {
            // 使用位置名稱更新
            fetchWeatherForLocation(currentLocation)
        }
    }
    
    /// 註冊應用程式生命週期通知
    private func registerForLifecycleNotifications() {
        // 監聽應用從背景回到前台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        // 監聽應用進入背景
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
    }
    
    /// 當應用程式變為活躍狀態
    @objc private func appDidBecomeActive() {
        // 當應用回到前台，啟動計時器並刷新天氣
        if !currentLocation.isEmpty {
            startAutoRefreshTimer()
            refreshWeatherData()
            Logger.debug("應用程式回到前台，已刷新天氣數據")
        }
    }
    
    /// 當應用程式進入背景
    @objc private func appDidEnterBackground() {
        // 當應用進入背景，暫停所有計時器以節省資源
        cleanupTimers()
        Logger.debug("應用程式進入背景，已暫停所有計時器")
    }
    
    // MARK: - 公開方法 - SavedLocation
    
    /// 載入已儲存的位置
    /// - Parameter checkTimezones: 是否檢查並更新缺失的時區資訊，預設為 true
    func loadSavedLocations(checkTimezones: Bool = true) {
        savedLocations = locationRepository.getAllSavedLocations()
        historyEntries = locationRepository.getAllLocations()

        // 只有在需要時才自動檢查並獲取缺失的時區資訊
        if checkTimezones {
            checkAndUpdateMissingTimezones()
        }
    }
    
    /// 檢查並更新缺失的時區資訊
    private func checkAndUpdateMissingTimezones() {
        locationRepository.ensureTimezonesForAllLocations { [weak self] updatedCount in
            DispatchQueue.main.async {
                if updatedCount > 0 {
                    Logger.debug("已為 \(updatedCount) 個位置更新時區資訊")
                    // 重新載入位置數據以反映時區更新
                    self?.savedLocations = self?.locationRepository.getAllSavedLocations() ?? []
                    
                    // 如果當前選中的位置被更新了，重新獲取天氣數據以應用新的時區
                    if let currentLocation = self?.currentSavedLocation,
                       let updatedLocation = self?.savedLocations.first(where: { $0.id == currentLocation.id }),
                       updatedLocation.timezone != nil && currentLocation.timezone == nil {
                        Logger.debug("當前位置的時區已更新，重新獲取天氣數據")
                        self?.currentSavedLocation = updatedLocation
                        self?.refreshWeatherData()
                    }
                } else {
                    Logger.debug("所有位置都已有時區資訊，無需更新")
                }
            }
        }
    }
    
    // MARK: - 地區管理方法
    
    /// 獲取當前選中地區的顯示名稱
    func getCurrentLocationDisplayName() -> String {
        return currentSavedLocation?.name ?? displayLocation
    }
    
    /// 清除所有儲存的位置
    func clearSavedLocations() {
        locationRepository.clearAllSavedLocations()
        locationRepository.clearAllLocations()
        savedLocations = []
        historyEntries = []
    }

    /// 清空當前位置
    func clearCurrentLocation() {
        currentLocation = ""
        currentSavedLocation = nil
        currentCoordinates = nil
    }
    
    /// 使用已儲存的位置
    /// - Parameter location: 已儲存的位置
    func useLocation(_ location: SavedLocation) {
        Logger.debug("===== 使用位置 =====")
        Logger.debug("位置名稱: \(location.name)")
        Logger.debug("位置座標: (\(location.lat), \(location.lon))")
        Logger.debug("位置ID: \(location.id)")
        Logger.debug("==================")
        
        currentLocation = location.name
        currentSavedLocation = location
        currentCoordinates = (lat: location.lat, lon: location.lon)
        locationRepository.saveLastViewedLocationID(location.id)
        
        fetchWeatherForCoordinates(lat: location.lat, lon: location.lon, locationName: location.name)
    }
    
    /// 儲存當前位置
    func saveCurrentLocation() {
        guard let coordinates = currentCoordinates else { return }
        
        // 獲取國家代碼
        let country = (currentLocation.contains(", ") && currentLocation.components(separatedBy: ", ").count > 1) ? 
            currentLocation.components(separatedBy: ", ")[1] : ""
        let locationName = currentLocation.contains(", ") ? 
            currentLocation.components(separatedBy: ", ")[0] : currentLocation
        
        // 創建並儲存新的SavedLocation，使用當前位置名稱
        let savedLocation = SavedLocation(
            name: locationName,
            formattedAddress: currentLocation, // 使用完整的當前位置作為地址
            lat: coordinates.lat,
            lon: coordinates.lon,
            country: country
        )
        locationRepository.addSavedLocation(savedLocation)
        
        // 保留舊版相容性
        let temperatureString = currentTemperature
        let locationEntry = LocationEntry(
            locationName: currentLocation,
            lastTemperature: temperatureString,
            lastCondition: weatherCondition,
            lastUpdated: Date()
        )
        locationRepository.addEntry(locationEntry)

        loadSavedLocations(checkTimezones: false)
    }
    
    /// 移除指定位置
    /// - Parameter location: 要移除的位置
    func removeLocation(_ location: SavedLocation) {
        locationRepository.removeSavedLocation(location)
        loadSavedLocations(checkTimezones: false)
    }
    
    /// 移除指定索引的位置
    /// - Parameter indexSet: 索引集合
    func removeSavedLocations(at indexSet: IndexSet) {
        locationRepository.removeSavedLocations(at: indexSet, from: savedLocations)
        loadSavedLocations(checkTimezones: false)
    }
    
    /// 搜尋位置
    /// - Parameter query: 搜尋關鍵詞
    func searchLocations(query: String) {
        guard !query.isEmpty else {
            locationSuggestions = []
            return
        }
        
        isLoading = true
        weatherService.searchLocations(query) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                switch result {
                case .success(let results):
                    self?.locationSuggestions = results
                case .failure(let error):
                    Logger.error("搜尋位置失敗: \(error.message)")
                    self?.locationSuggestions = []
                    self?.errorMessage = error.message
                }
            }
        }
    }
    
    /// 從搜尋結果中選擇位置
    /// - Parameter result: 地理編碼結果
    func selectLocation(_ result: GeocodingResult) {
        currentLocation = "\(result.name), \(result.country)"
        currentCoordinates = (lat: result.lat, lon: result.lon)
        
        // 直接將完整名稱傳遞給fetchWeatherForCoordinates
        fetchWeatherForCoordinates(lat: result.lat, lon: result.lon, locationName: "\(result.name), \(result.country)")
        
        // 啟動自動刷新計時器
        startAutoRefreshTimer()
    }
    
    // MARK: - 公開方法 - 原有相容性
    
    /// 使用儲存的位置
    /// - Parameter entry: 已儲存的位置
    func useLocationEntry(_ entry: LocationEntry) {
        currentLocation = entry.locationName
        fetchWeatherForLocation(entry.locationName)
    }
    
    /// 搜尋位置的天氣
    /// - Parameter location: 位置名稱
    func searchLocation(_ location: String) {
        guard !location.isEmpty else { return }
        
        Logger.debug("搜尋位置: \(location)")
        currentLocation = location
        fetchWeatherForLocation(location)
    }
    
    /// 設置溫度單位
    /// - Parameter unit: 溫度單位
    func setTemperatureUnit(_ unit: TemperatureUnit) {
        Logger.debug("設置溫度單位: \(unit.displayName)")
        temperatureUnit = unit
        
        // 手動強制更新溫度顯示
        updateTemperatureDisplay()
        
        // 手動強制更新預報數據
        if let forecasts = currentWeatherData?.forecast {
            updateForecastsData(forecasts)
        }
        
        // 更新主畫面上的溫度範圍
        if let weather = currentWeatherData, let todayForecast = weather.forecast.first {
            let lowTemp = formatTemperature(todayForecast.lowTemperature)
            let highTemp = formatTemperature(todayForecast.highTemperature)
            tempRangeString = "\(lowTemp) - \(highTemp)"
        }
        
        // 更新主頁面專用數據
        if let weather = currentWeatherData, !weather.hourlyForecast.isEmpty {
            let timeZone = TimeZone(secondsFromGMT: weather.timezoneOffset) ?? TimeZone.current
            updateMainPageData(weather, hourlyForecasts: weather.hourlyForecast, locationTimeZone: timeZone)
        }
        
        // 觸發溫度單位變更通知
        didChangeTemperatureUnit.toggle()
    }
    
    /// 設置時間格式
    /// - Parameter format: 時間格式
    func setTimeFormat(_ format: TimeFormat) {
        Logger.debug("設置時間格式: \(format.displayName)")
        timeFormat = format
        
        // 手動強制更新時間顯示
        updateTimeDisplay()
    }
    
    /// 設置測量單位系統
    /// - Parameter system: 測量單位系統
    func setMeasurementSystem(_ system: MeasurementSystem) {
        Logger.debug("設置測量單位: \(system.displayName)")
        measurementSystem = system
    }
    
    /// 設置主題模式
    /// - Parameter theme: 主題模式
    func setThemeMode(_ theme: ThemeMode) {
        Logger.debug("設置主題模式: \(theme.displayName)")
        themeMode = theme // 更新 ViewModel 的主題模式
        
        // 通知UI更新（可能需要重新載入顏色）
        objectWillChange.send()
    }
    
    /// 更新時間顯示
    func updateTimeDisplay() {
        // 更新主頁面專用數據中的時間顯示
        if let weather = currentWeatherData, !weather.hourlyForecast.isEmpty {
            let timeZone = TimeZone(secondsFromGMT: weather.timezoneOffset) ?? TimeZone.current
            updateMainPageData(weather, hourlyForecasts: weather.hourlyForecast, locationTimeZone: timeZone)
        }
        
        Logger.debug("時間顯示已更新為: \(timeFormat.displayName)")
    }
    
    /// 刪除指定索引的位置
    /// - Parameter indexSet: 索引集合
    func removeLocations(at indexSet: IndexSet) {
        Logger.debug("WeatherViewModel - 刪除位置索引: \(indexSet)")
        locationRepository.removeEntries(at: indexSet, from: historyEntries)
        loadSavedLocations(checkTimezones: false)
    }
    
    // MARK: - 私有方法
    
    /// 獲取當前位置的天氣
    private func fetchWeatherForCurrentLocation() {
        fetchWeatherForLocation(currentLocation)
    }
    
    /// 獲取指定位置的天氣
    /// - Parameter location: 位置名稱
    private func fetchWeatherForLocation(_ location: String) {
        isLoading = true
        errorMessage = nil
        
        Logger.debug("開始獲取位置天氣: \(location)")
        
        // 獲取當前位置的天氣來源
        let weatherSource = currentSavedLocation?.effectiveWeatherSource
        Logger.debug("使用天氣來源: \(weatherSource?.displayName ?? "預設")")
        
        weatherService.getWeatherForLocation(location, weatherSource: weatherSource) { [weak self] result in
            self?.handleWeatherResult(result)
        }
    }
    
    /// 使用座標獲取天氣數據
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - locationName: 位置名稱
    private func fetchWeatherForCoordinates(lat: Double, lon: Double, locationName: String? = nil) {
        isLoading = true
        errorMessage = nil
        
        Logger.debug("===== 開始獲取天氣數據 =====")
        Logger.debug("緯度: \(lat)")
        Logger.debug("經度: \(lon)")
        Logger.debug("位置名稱: \(locationName ?? "未提供")")
        Logger.debug("==========================")
        
        // 獲取當前位置的天氣來源和預報模式
        let weatherSource = currentSavedLocation?.effectiveWeatherSource
        let useDaily = !forecastMode.isHourly
        Logger.debug("使用天氣來源: \(weatherSource?.displayName ?? "預設")")
        Logger.debug("使用預報模式: \(forecastMode.displayName)")

        if let name = locationName {
            // 使用提供的位置名稱
            weatherService.getWeatherDataForCoordinates(lat, lon: lon, locationName: name, weatherSource: weatherSource, useDaily: useDaily) { [weak self] result in
                self?.handleWeatherResult(result)
            }
        } else {
            // 使用API自動解析的位置名稱
            weatherService.getWeatherForCoordinates(lat: lat, lon: lon, weatherSource: weatherSource) { [weak self] result in
                self?.handleWeatherResult(result)
            }
        }
    }
    
    /// 載入指定地區的天氣資料
    /// - Parameters:
    ///   - lat: 緯度
    ///   - lon: 經度
    ///   - locationName: 位置名稱
    func loadWeatherForLocation(lat: Double, lon: Double, locationName: String) {
        // 更新當前座標
        currentCoordinates = (lat: lat, lon: lon)
        
        // 獲取天氣資料
        fetchWeatherForCoordinates(lat: lat, lon: lon, locationName: locationName)
        
        // 啟動自動刷新計時器
        startAutoRefreshTimer()
        
        Logger.debug("載入地區天氣: \(locationName) (\(lat), \(lon))")
    }
    
    /// 處理天氣API結果
    /// - Parameter result: 天氣數據結果
    private func handleWeatherResult(_ result: Result<WeatherData, WeatherService.WeatherError>) {
        DispatchQueue.main.async { [weak self] in
            self?.isLoading = false

            switch result {
            case .success(let weather):
                Logger.success("成功獲取天氣數據: \(weather.location), 溫度: \(weather.temperature)°C")
                Logger.debug("===== 天氣詳細資訊 =====")
                Logger.debug("位置: \(weather.location)")
                Logger.debug("溫度: \(weather.temperature)°C")
                Logger.debug("天氣狀況: \(weather.condition)")
                Logger.debug("濕度: \(weather.humidity)%")
                Logger.debug("風速: \(weather.windSpeed) m/s")
//                Logger.debug("日出時間: \(weather.sunrise)")
//                Logger.debug("日落時間: \(weather.sunset)")
                Logger.debug("每小時預報數量: \(weather.hourlyForecast.count)")
                Logger.debug("時區偏移: \(weather.timezoneOffset) 秒")
                Logger.debug("========================")

                self?.currentWeatherData = weather
                self?.updateWeatherDisplay(weather)
                self?.updateForecastsData(weather.forecast)
                self?.updateHourlyForecastData(weather.hourlyForecast)

                // 更新時區信息和當地時間
                self?.updateTimeZoneInfo(weather)

                // 更新上次更新時間
                self?.lastUpdatedTime = Date()

                // 成功時重置重試計數
                self?.retryCount = 0
                self?.isAutoRetrying = false
                self?.stopAutoRetryTimer()

            case .failure(let error):
                Logger.error("獲取天氣失敗: \(error.message)")
                self?.handleWeatherError(error.message)
            }
        }
    }

    /// 處理天氣錯誤，包含自動重試邏輯
    /// - Parameter errorMessage: 錯誤訊息
    private func handleWeatherError(_ errorMessage: String) {
        Logger.error("處理天氣錯誤: \(errorMessage), 當前重試次數: \(retryCount)")

        if retryCount < maxRetryCount {
            // 第一次錯誤，自動重試
            retryCount += 1
            isAutoRetrying = true
            Logger.debug("開始自動重試 (第\(retryCount)次)")

            // 延遲2秒後自動重試
            autoRetryTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { [weak self] _ in
                guard let self = self else { return }
                Logger.debug("執行自動重試")
                self.refreshWeatherData()
            }
        } else {
            // 已達最大重試次數，顯示錯誤訊息
            Logger.error("已達最大重試次數，顯示錯誤畫面")
            self.errorMessage = errorMessage
            self.currentTemperature = "Error"
            self.weatherCondition = "Unable to get weather"
            self.isAutoRetrying = false
            self.stopAutoRetryTimer()
        }
    }

    /// 停止自動重試計時器
    private func stopAutoRetryTimer() {
        autoRetryTimer?.invalidate()
        autoRetryTimer = nil
    }
    
    /// 嘗試重新獲取天氣數據（網路恢復時調用）
    func retryFetchingWeather() {
        // 只有在網路恢復且之前有錯誤時才重試
        if errorMessage != nil {
            Logger.debug("網路恢復，嘗試重新獲取天氣數據")

            // 重置錯誤狀態和重試計數
            errorMessage = nil
            retryCount = 0
            isAutoRetrying = false
            stopAutoRetryTimer()

            // 重新獲取天氣數據
            refreshWeatherData()
        }
    }

    /// 手動重試（用戶點擊重試按鈕時調用）
    func manualRetry() {
        Logger.debug("用戶手動重試")

        // 重置所有重試相關狀態
        errorMessage = nil
        retryCount = 0
        isAutoRetrying = false
        stopAutoRetryTimer()

        // 重新獲取天氣數據
        refreshWeatherData()
    }

    /// 清除 Widget 位置數據
    func clearWidgetLocationData() {
        // 清除 App Group 中的位置數據
        if let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") {
            groupDefaults.removeObject(forKey: "widget_selected_location_name")
            groupDefaults.removeObject(forKey: "widget_selected_location_id")
            groupDefaults.removeObject(forKey: "widget_selected_location_lat")
            groupDefaults.removeObject(forKey: "widget_selected_location_lon")
            groupDefaults.removeObject(forKey: "widget_selected_location_country")
            groupDefaults.removeObject(forKey: "widget_selected_location_timezone")
            groupDefaults.removeObject(forKey: "widget_selected_location_weather_source")
            groupDefaults.removeObject(forKey: "widget_selected_location_address")
            groupDefaults.synchronize()

            Logger.debug("🔧 已清除 App Group 中的 Widget 位置數據")
        }

        // 使用 WidgetSettingsManager 清除設定
        WidgetSettingsManager.shared.clearSelectedLocation()

        // 通知 Widget 更新
        WidgetCenter.shared.reloadAllTimelines()

        Logger.debug("🔧 IAP 狀態變化：已清除 Widget 位置數據並通知 Widget 更新")
    }

    /// 將所有儲存位置的天氣來源重置為 OW（免費版限制）
    func resetAllLocationWeatherSourcesToOW() {
        let locations = locationRepository.getAllSavedLocations()
        var updatedCount = 0

        for location in locations {
            // 如果位置的天氣來源不是 OW，則更新為 OW
            if location.effectiveWeatherSource != .OW {
                locationRepository.updateLocationWeatherSource(location, newSource: .OW)
                updatedCount += 1
                Logger.debug("🔧 已將位置 \(location.name) 的天氣來源重置為 OW")
            }
        }

        if updatedCount > 0 {
            // 重新載入儲存的位置列表
            loadSavedLocations(checkTimezones: false)
            Logger.debug("🔧 IAP 狀態變化：已將 \(updatedCount) 個位置的天氣來源重置為 OW")
        } else {
            Logger.debug("🔧 IAP 狀態變化：所有位置的天氣來源已經是 OW，無需更新")
        }
    }

    /// 重置主題設定為免費版本（白天主題）
    func resetThemeToFree() {
        let currentTheme = themeMode

        // 檢查當前主題是否為需要 Pro 訂閱的主題
        let isProTheme = (currentTheme == .dark || currentTheme == .system)

        if isProTheme {
            // 重置為免費的白天主題
            themeMode = .light
            Logger.debug("🔧 IAP 狀態變化：主題已從 \(currentTheme.displayName) 重置為 \(ThemeMode.light.displayName)")

            // 手動觸發 UI 更新
            objectWillChange.send()
        } else {
            Logger.debug("🔧 IAP 狀態變化：當前主題 \(currentTheme.displayName) 為免費主題，無需重置")
        }
    }

    /// 檢查並重置主題設定（應用啟動時調用）
    private func checkAndResetThemeIfNeeded() {
        // 檢查當前 IAP 狀態
        let isPro = iapService.isPro
        let currentTheme = themeMode

        Logger.debug("🔧 應用啟動檢查：IAP 狀態 = \(isPro ? "Pro" : "免費"), 當前主題 = \(currentTheme.displayName)")

        // 如果不是 Pro 用戶，但使用了需要 Pro 的主題，則重置
        if !isPro {
            let isProTheme = (currentTheme == .dark || currentTheme == .system)
            if isProTheme {
                themeMode = .light
                Logger.debug("🔧 應用啟動：免費用戶使用 Pro 主題，已重置為白天主題")

                // 手動觸發 UI 更新
                objectWillChange.send()
            } else {
                Logger.debug("🔧 應用啟動：免費用戶使用免費主題，無需重置")
            }
        } else {
            Logger.debug("🔧 應用啟動：Pro 用戶，主題設定無需檢查")
        }
    }

    /// 更新時區信息和當地時間
    /// - Parameter weather: 天氣數據
    private func updateTimeZoneInfo(_ weather: WeatherData) {
        // 獲取該位置的時區偏移量
        let timezoneOffset = weather.timezoneOffset
        
        // 嘗試獲取基於位置ID的時區標識符
        if let timezoneName = TimeZone(secondsFromGMT: timezoneOffset)?.identifier {
            timezone = timezoneName
            Logger.debug("時區標識符: \(timezoneName)")
        } else {
            // 如果無法獲取標識符，使用UTC偏移量創建自定義標識符
            let hours = abs(timezoneOffset) / 3600
            let minutes = (abs(timezoneOffset) % 3600) / 60
            let sign = timezoneOffset >= 0 ? "+" : "-"
            timezone = "UTC\(sign)\(hours):\(minutes)"
            Logger.debug("自定義時區標識符: \(timezone ?? "未知")")
        }
        
        // 計算當地時間
        let now = Date()
        
        // 使用時區建立新的日曆實例
        var calendarWithTimeZone = Calendar.current
        if let timeZone = TimeZone(secondsFromGMT: timezoneOffset) {
            calendarWithTimeZone.timeZone = timeZone
        }
        
        // 使用指定時區的日曆獲取當地時間
        let components = calendarWithTimeZone.dateComponents([.year, .month, .day, .hour, .minute, .second], from: now)
        currentLocationTime = calendarWithTimeZone.date(from: components)
        
        Logger.debug("當地時間: \(currentLocationTime?.description ?? "未知")")
    }
    
    /// 更新天氣顯示
    /// - Parameter weather: 天氣數據
    private func updateWeatherDisplay(_ weather: WeatherData) {
        // 不要覆蓋 currentLocation，保持我們設定的位置名稱
        // currentLocation = weather.location  // 移除這行
        updateTemperatureDisplay()
        // API 已直接提供本地化的天氣描述，無需轉換
        weatherCondition = weather.condition
        
        // 更新顯示位置（只取地區名稱）
        displayLocation = extractLocationName(from: weather.location)
        
        // 更新濕度 (直接從API獲取)
        humidityString = "\(weather.humidity)%"
        
        // 將降雨機率更新為體感溫度
        precipitationString = "\(formatTemperature(weather.feelsLike))"
        
        // 更新溫度範圍
        if let todayForecast = weather.forecast.first {
            let lowTemp = formatTemperature(todayForecast.lowTemperature)
            let highTemp = formatTemperature(todayForecast.highTemperature)
            tempRangeString = "\(lowTemp) - \(highTemp)"
        } else {
            tempRangeString = formatTemperature(weather.temperature)
        }
        
        // 更新天氣圖標 (從API獲取的icon代碼)
        weatherIconName = getWeatherIconNameFromCode(weather.iconCode)
    }
    
    /// 更新溫度顯示
    func updateTemperatureDisplay() {
        guard let weather = currentWeatherData else { return }
        currentTemperature = formatTemperature(weather.temperature)
        temperatureNumber = "\(Int(temperatureUnit.convert(fromCelsius: Double(weather.temperature)).rounded()))"
    }
    
    /// 更新預報數據
    /// - Parameter forecasts: 預報數據
    private func updateForecastsData(_ forecastsData: [Forecast]) {
        self.forecasts = forecastsData.map { forecast in
            ForecastViewModel(
                date: formatDate(forecast.date),
                highTemperature: formatTemperature(forecast.highTemperature),
                lowTemperature: formatTemperature(forecast.lowTemperature),
                condition: forecast.condition, // API 已提供本地化描述
                iconCode: forecast.iconCode,
                precipitationProbability: "\(forecast.precipitationProbability)%",
                humidity: "\(forecast.humidity)%"
            )
        }
    }
    
    /// 更新每三小時預報數據
    /// - Parameter hourlyForecastData: 每三小時預報數據
    private func updateHourlyForecastData(_ hourlyForecastData: [HourlyForecast]) {
        Logger.debug("===== updateHourlyForecastData 開始 =====")
        Logger.debug("接收到的每小時預報數據數量: \(hourlyForecastData.count)")
        
        guard let weather = currentWeatherData else {
            Logger.error("警告：currentWeatherData 為 nil，無法更新每小時預報數據")
            hourlyForecasts = []
            return
        }
        
        // 獲取該位置的時區偏移量
        let timezoneOffset = weather.timezoneOffset
        
        // 創建該地點的時區（基於UTC偏移）
        let locationTimeZone = TimeZone(secondsFromGMT: timezoneOffset) ?? TimeZone.current
        
        Logger.debug("時區偏移量: \(timezoneOffset) 秒")
        Logger.debug("位置時區: \(locationTimeZone.identifier)")
        
        // 將API提供的數據直接處理為ViewModel
        var updatedForecasts: [HourlyForecastViewModel] = []
        
        // 處理每筆預報數據
        for (index, forecast) in hourlyForecastData.enumerated() {
            let formattedTime: String
            
            // 如果是第一筆資料（當前天氣）
            if index == 0 {
                formattedTime = "NOW"
                Logger.debug("處理第一筆資料（當前天氣），顯示為: \(formattedTime)")
            } else {
                formattedTime = formatTimeWithTimeZone(forecast.time, timeZone: locationTimeZone)
                Logger.debug("處理第\(index)筆資料，時間為: \(formattedTime)")
            }
            
            // 查找對應日期的預報數據，用於獲取濕度和溫度範圍
            var humidityStr = index == 0 && weather.humidity > 0 ? "\(weather.humidity)%" : "0%"
            var tempRangeStr = ""
            
            // 從預報數據中找出同一天的預報
            let calendar = Calendar.current
            if let matchingForecast = weather.forecast.first(where: { dailyForecast in
                return calendar.isDate(forecast.time, inSameDayAs: dailyForecast.date)
            }) {
                // 找到了對應的日預報
                humidityStr = "\(matchingForecast.humidity)%"
                let lowTemp = formatTemperature(matchingForecast.lowTemperature)
                let highTemp = formatTemperature(matchingForecast.highTemperature)
                tempRangeStr = "\(lowTemp.replacingOccurrences(of: "°C", with: "").replacingOccurrences(of: "°F", with: "")) ~ \(highTemp.replacingOccurrences(of: "°C", with: "").replacingOccurrences(of: "°F", with: ""))"
            } else if index == 0 {
                // 當前天氣，但找不到匹配的預報
                humidityStr = "\(weather.humidity)%"
                tempRangeStr = formatTemperature(weather.temperature)
            } else {
                // 其他情況，使用當天的溫度作為範圍（因為沒有範圍數據）
                let temp = formatTemperature(forecast.temperature)
                tempRangeStr = "\(temp.replacingOccurrences(of: "°C", with: "").replacingOccurrences(of: "°F", with: "")) ~ \(temp.replacingOccurrences(of: "°C", with: "").replacingOccurrences(of: "°F", with: ""))"
            }
            
            let viewModel = HourlyForecastViewModel(
                time: formattedTime,
                date: forecast.time,
                temperature: formatTemperature(forecast.temperature),
                iconCode: forecast.iconCode,
                precipitationProbability: "\(forecast.precipitationProbability)%",
                humidity: humidityStr,
                lowHighTemp: tempRangeStr
            )
            
            updatedForecasts.append(viewModel)
        }
        
        self.hourlyForecasts = updatedForecasts
        
        Logger.debug("更新後的每小時預報資料數量: \(self.hourlyForecasts.count)")
        
        // 更新主頁面專用數據
        updateMainPageData(weather, hourlyForecasts: hourlyForecastData, locationTimeZone: locationTimeZone)
    }
    
    /// 更新主頁面專用數據
    /// - Parameters:
    ///   - weather: 天氣數據
    ///   - hourlyForecasts: 每小時預報數據
    ///   - locationTimeZone: 位置時區
    private func updateMainPageData(_ weather: WeatherData, hourlyForecasts: [HourlyForecast], locationTimeZone: TimeZone) {
        // 生成更新哈希值，用於檢查是否需要重新計算
        let isProUser = iapService.isEntitlementActive("pro")
        let maxDataCount = isProUser ? hourlyForecasts.count : min(17, hourlyForecasts.count)
        
        let currentHash = "\(weather.location)-\(hourlyForecasts.count)-\(temperatureUnit.rawValue)-\(timeFormat.rawValue)-\(maxDataCount)-\(locationTimeZone.identifier)"
        
        // 檢查是否需要重新計算
        if currentHash == lastMainPageUpdateHash {
            Logger.debug("updateMainPageData: 數據未變化，跳過重新計算")
            return
        }
        
        Logger.debug("===== updateMainPageData 開始 =====")
        Logger.debug("接收到的每小時預報數據數量: \(hourlyForecasts.count)")
        
        let limitedForecasts = Array(hourlyForecasts.prefix(maxDataCount))
        
        Logger.debug("IAP 狀態: \(isProUser ? "Pro 用戶" : "免費用戶"), 原始數據: \(hourlyForecasts.count) 項, 限制後: \(limitedForecasts.count) 項")
        
        if limitedForecasts.isEmpty {
            Logger.error("警告：limitedForecasts 為空，無法生成主頁面數據")
            return
        }
        
        // 創建 API 時間格式化器
        let apiFormatter = DateFormatter()
        apiFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        // 1. 計算 forecastDateTimes（統一數據處理管道 - 步驟 1）
        Logger.debug("📊 統一數據處理管道：步驟 1 - 計算 forecastDateTimes")
        var calculatedForecastTimes: [String] = []
        
        for (index, forecast) in limitedForecasts.enumerated() {
            let apiTimeString = (index == 0) ? "NOW" : apiFormatter.string(from: forecast.time)
            
            if apiTimeString == "NOW" {
                // 對於 NOW，使用當前時間格式化為 API 格式
                let nowString = apiFormatter.string(from: Date())
                calculatedForecastTimes.append(nowString)
            } else {
                calculatedForecastTimes.append(apiTimeString)
            }
        }
        
        Logger.debug("📊 forecastDateTimes 計算完成，數量: \(calculatedForecastTimes.count)")
        
        // 2. 計算 timeLabels（統一數據處理管道 - 步驟 2）
        Logger.debug("📊 統一數據處理管道：步驟 2 - 計算 timeLabels")
        let calculatedTimeLabels = computeTimeLabels(
            forecastTimes: calculatedForecastTimes,
            timezone: currentSavedLocation?.timezone ?? locationTimeZone.identifier,
            currentTime: Date()
        )
        
        Logger.debug("📊 timeLabels 計算完成，數量: \(calculatedTimeLabels.count)")
        
        // 3. 更新溫度圖表數據
        Logger.debug("📊 統一數據處理管道：步驟 3 - 更新溫度圖表數據")
        var chartData: [TemperaturePoint] = []
        
        for (index, forecast) in limitedForecasts.enumerated() {
            // 使用 API 格式的時間字串，讓視圖自己處理時區轉換
            let apiTimeString = (index == 0) ? "NOW" : apiFormatter.string(from: forecast.time)
            
            // 使用正確的溫度單位
            let temp = temperatureUnit.convert(fromCelsius: Double(forecast.temperature))
            
            let point = TemperaturePoint(
                time: apiTimeString,
                date: forecast.time,
                temperature: temp, // 已轉換的溫度
                iconCode: forecast.iconCode
            )
            chartData.append(point)
        }
        
        // 4. 更新時間軸預覽數據
        Logger.debug("📊 統一數據處理管道：步驟 4 - 更新時間軸預覽數據")
        var timelineData: [TimelineHourlyData] = []
        
        for (index, forecast) in limitedForecasts.enumerated() {
            // 使用 API 格式的時間字串，讓視圖自己處理時區轉換
            let apiTimeString = (index == 0) ? "NOW" : apiFormatter.string(from: forecast.time)
            
            // 直接從每小時預報獲取濕度資訊
            let humidityStr = "\(forecast.humidity)%"
            
            // 使用當前溫度單位格式化溫度
            let temp = formatTemperature(forecast.temperature)
            let tempNum = temp.replacingOccurrences(of: "°C", with: "").replacingOccurrences(of: "°F", with: "")
            let tempRangeStr = "\(tempNum) ~ \(tempNum)"
            
            let timelineItem = TimelineHourlyData(
                time: apiTimeString,
                date: forecast.time,
                temperature: formatTemperature(forecast.temperature),
                iconCode: forecast.iconCode,
                precipitationProbability: "\(forecast.precipitationProbability)%",
                humidity: humidityStr,
                lowHighTemp: tempRangeStr
            )
            
            timelineData.append(timelineItem)
        }
        
        Logger.debug("總共創建了 \(timelineData.count) 個時間軸預覽數據項目")
        
        // 5. 一次性更新所有 @Published 屬性（統一數據處理管道 - 步驟 5）
        Logger.debug("📊 統一數據處理管道：步驟 5 - 一次性更新所有數據")
        
        // 確保在主線程更新 @Published 屬性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 按順序更新，確保數據一致性
            self.forecastDateTimes = calculatedForecastTimes
            self.timeLabels = calculatedTimeLabels
            self.temperatureChartData = chartData
            self.timelinePreviewData = timelineData
            
            // 更新 hash 值，避免重複計算
            self.lastMainPageUpdateHash = currentHash
            self.lastMainPageUpdateTime = Date()
            
            Logger.debug("📊 所有數據已更新完成")
            Logger.debug("   - forecastDateTimes: \(self.forecastDateTimes.count) 項")
            Logger.debug("   - timeLabels: \(self.timeLabels.count) 項")
            Logger.debug("   - temperatureChartData: \(self.temperatureChartData.count) 項")
            Logger.debug("   - timelinePreviewData: \(self.timelinePreviewData.count) 項")
            Logger.debug("===== updateMainPageData 結束 =====")
        }
    }
    
    /// 計算時間標籤（從 RulerTestView 移植過來）
    /// - Parameters:
    ///   - forecastTimes: 預報時間數組
    ///   - timezone: 時區標識符
    ///   - currentTime: 當前時間
    /// - Returns: 時間標籤數組
    private func computeTimeLabels(forecastTimes: [String], timezone: String, currentTime: Date) -> [String] {
        Logger.debug("computeTimeLabels: 開始生成時間標籤，forecastTimes 數量: \(forecastTimes.count)")
        
        if forecastTimes.isEmpty {
            Logger.debug("computeTimeLabels: 沒有 API 數據，返回24小時制的標籤")
            return (0...23).map { formatHour(hour: $0) }
        }
        
        Logger.debug("computeTimeLabels: 使用 API 數據生成標籤，數據筆數: \(forecastTimes.count)")
        var labels: [String] = []
        var previousDay: Int? = nil
        
        let majorTickCount = Int(ceil(Double(forecastTimes.count) / 3.0))
        Logger.debug("computeTimeLabels: 計算主刻度數量: \(majorTickCount)")
        
        let currentTimezone = TimeZone(identifier: timezone) ?? TimeZone.current
        var calendar = Calendar.current
        calendar.timeZone = currentTimezone
        
        for i in 0..<majorTickCount {
            let timeIndex = i * 3
            
            if timeIndex >= forecastTimes.count {
                if let lastTimeString = forecastTimes.last,
                   let lastLabelDate = convertTimeStringToDate(lastTimeString),
                   previousDay != nil {
                    let lastIndex = forecastTimes.count - 1
                    let lastTimeIndex = (lastIndex / 3) * 3
                    let extraHours = (timeIndex - lastTimeIndex) * 3
                    
                    if let nextDate = calendar.date(byAdding: .hour, value: extraHours, to: lastLabelDate) {
                        let currentDay = calendar.component(.day, from: nextDate)
                        let hour = calendar.component(.hour, from: nextDate)
                        let formattedTime: String
                        
                        if currentDay != previousDay {
                            let dateString = DateTimeFormatter.shared.formatMonthDay(nextDate)
                            let timeString = DateTimeFormatter.shared.formatTimeString("\(hour):00")
                            formattedTime = "\(dateString) \(timeString)"
                        } else {
                            formattedTime = DateTimeFormatter.shared.formatTimeString("\(hour):00")
                        }
                        
                        labels.append(formattedTime)
                        previousDay = currentDay
                    } else {
                        labels.append("")
                    }
                } else {
                    labels.append("")
                }
                continue
            }
            
            if timeIndex == 0 {
                let nowLabel = "now".localized
                labels.append(nowLabel)
                
                let currentDay = calendar.component(.day, from: currentTime)
                previousDay = currentDay
            } else {
                let apiTimeString = forecastTimes[timeIndex]
                
                if let date = convertTimeStringToDate(apiTimeString) {
                    let localDate = DateTimeFormatter.shared.convertToTimezone(date, timezoneId: timezone)
                    let currentDay = calendar.component(.day, from: localDate)
                    let hour = calendar.component(.hour, from: localDate)
                    let formattedTime: String
                    
                    if currentDay != previousDay {
                        let dateString = DateTimeFormatter.shared.formatMonthDay(localDate)
                        let timeString = DateTimeFormatter.shared.formatTimeString("\(hour):00")
                        formattedTime = "\(dateString) \(timeString)"
                    } else {
                        formattedTime = DateTimeFormatter.shared.formatTimeString("\(hour):00")
                    }
                    
                    labels.append(formattedTime)
                    previousDay = currentDay
                } else {
                    labels.append("")
                }
            }
        }
        
        Logger.debug("computeTimeLabels: 最終生成了 \(labels.count) 個標籤")
        return labels
    }
    
    /// 將API時間字符串轉換為日期對象
    private func convertTimeStringToDate(_ dateTimeString: String) -> Date? {
        return DateTimeFormatter.shared.parseAPITimeString(dateTimeString)
    }
    
    /// 格式化小時顯示
    private func formatHour(hour: Int) -> String {
        return DateTimeFormatter.shared.formatTimeString("\(hour):00")
    }
    
    /// 格式化溫度
    /// - Parameter celsius: 攝氏度溫度
    /// - Returns: 格式化的溫度字符串
    private func formatTemperature(_ celsius: Int) -> String {
        let convertedTemp = temperatureUnit.convert(fromCelsius: Double(celsius))
        return "\(Int(convertedTemp.rounded()))\(temperatureUnit.symbol)"
    }
    
    /// 格式化日期
    /// - Parameter date: 日期
    /// - Returns: 格式化的日期字符串
    private func formatDate(_ date: Date) -> String {
        // 使用系統日期格式
        return DateTimeFormatter.shared.formatDate(date)
    }
    
    /// 格式化時間（考慮時區）
    /// - Parameters:
    ///   - date: UTC 日期
    ///   - timeZone: 指定時區
    /// - Returns: 格式化的時間字符串（根據系統設定）
    private func formatTimeWithTimeZone(_ date: Date, timeZone: TimeZone) -> String {
        // 將 UTC 時間轉換為指定時區的當地時間
        let timezoneId = timeZone.identifier
        let localDate = DateTimeFormatter.shared.convertToTimezone(date, timezoneId: timezoneId)
        
        // 使用 DateTimeFormatter 來格式化時間
        return DateTimeFormatter.shared.formatTime(localDate)
    }
    
    /// 格式化時間
    /// - Parameter date: 日期
    /// - Returns: 格式化的時間字符串（根據系統設定）
    private func formatTime(_ date: Date) -> String {
        // 如果有當前天氣數據，使用其時區偏移量
        if let weather = currentWeatherData {
            let locationTimeZone = TimeZone(secondsFromGMT: weather.timezoneOffset) ?? TimeZone.current
            return formatTimeWithTimeZone(date, timeZone: locationTimeZone)
        } else {
            // 否則使用系統時間格式
            return DateTimeFormatter.shared.formatTime(date)
        }
    }
    
    /// 根據天氣圖標代碼獲取對應的自定義字型圖標
    /// - Parameter iconCode: 天氣圖標代碼
    /// - Returns: 自定義字型圖標
    private func getWeatherIconNameFromCode(_ iconCode: String) -> String {
        // 使用AppIconsSymbol中的共用方法
        return AppIconsSymbol.getWeatherIconFromCode(iconCode)
    }
    
    /// 根據天氣狀況獲取對應系統圖標名稱 (當自定義字體不可用時使用)
    /// - Parameter condition: 天氣狀況
    /// - Returns: 系統圖標名稱
    private func getWeatherIconName(for condition: String) -> String {
        // 使用AppIconsSymbol中的共用方法獲取對應的自定義圖標
        let appIcon = AppIconsSymbol.getWeatherIconFromCode(condition)
        // 獲取對應的系統圖標名稱
        return AppIconsSymbol.getSystemSymbolNameForIcon(appIcon)
    }
    
    /// 從完整位置中提取地區名稱
    /// - Parameter fullLocation: 完整位置字符串 (如 "台北市, TW")
    /// - Returns: 只含地區名稱的字符串 (如 "台北市")
    private func extractLocationName(from fullLocation: String) -> String {
        // 分割字符串，取第一個部分（通常是地區名稱）
        let components = fullLocation.components(separatedBy: ",")
        return components[0].trimmingCharacters(in: .whitespaces)
    }
    
    /// 切換是否顯示每三小時預報
    func toggleHourlyForecast() {
        showHourlyForecast.toggle()
    }
    
    /// 切換預報類型 (每小時/每日)
    func toggleForecastType() {
        // 切換預報模式
        forecastMode = forecastMode == .hourly ? .daily : .hourly
        Logger.debug("🎯 手勢切換預報模式: \(forecastMode.displayName)")

        // 切換預報類型時重新載入天氣資料
        if let currentLocation = currentSavedLocation {
            loadWeatherForLocation(currentLocation, useDaily: !forecastMode.isHourly)
        }
    }

    /// 載入指定位置的天氣資料（支援選擇每小時或每日預報）
    /// - Parameters:
    ///   - location: 儲存的位置
    ///   - useDaily: 是否使用每日預報（true: current+daily, false: current+hourly）
    private func loadWeatherForLocation(_ location: SavedLocation, useDaily: Bool) {
        Logger.debug("開始載入天氣資料: \(location.name), 模式: \(useDaily ? "current+daily" : "current+hourly")")

        isLoading = true
        errorMessage = nil

        // 使用位置的有效天氣來源
        let weatherSource = location.effectiveWeatherSource

        weatherService.getWeatherDataForCoordinates(
            location.lat,
            lon: location.lon,
            locationName: location.name,
            weatherSource: weatherSource,
            useDaily: useDaily
        ) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false

                switch result {
                case .success(let weather):
                    Logger.success("天氣資料載入成功: \(location.name)")
                    self?.currentWeatherData = weather
                    self?.updateWeatherDisplay(weather)
                    self?.updateForecastsData(weather.forecast)
                    self?.updateHourlyForecastData(weather.hourlyForecast)
                    self?.updateTimeZoneInfo(weather)

                    // 更新最後更新時間
                    self?.lastUpdatedTime = Date()

                case .failure(let error):
                    Logger.error("天氣資料載入失敗: \(error.message)")
                    self?.handleWeatherError(error.message)
                }
            }
        }
    }
    
    /// 手動刷新天氣數據
    func manualRefreshWeather() {
        if !currentLocation.isEmpty {
            refreshWeatherData()
            
            // 重置自動刷新計時器
            startAutoRefreshTimer()
            
            Logger.debug("手動刷新天氣數據")
        }
    }
    
    /// 載入第一筆儲存地區並刷新天氣數據
    func loadFirstSavedLocationAndRefresh() {
        let locationRepository = LocationRepository()
        let savedLocations = locationRepository.getAllSavedLocations()

        if let firstLocation = savedLocations.first {
            // 設定第一個位置為當前位置
            currentLocation = firstLocation.name
            currentCoordinates = (lat: firstLocation.lat, lon: firstLocation.lon)

            // 刷新天氣數據
            refreshWeatherData()

            Logger.debug("載入第一筆儲存地區並刷新天氣數據: \(firstLocation.name)")
        } else {
            // 沒有儲存的位置，清空當前位置
            currentLocation = ""
            currentCoordinates = nil
            Logger.debug("沒有儲存的位置")
        }
    }

    /// 載入第一筆儲存地區但不刷新天氣數據
    func loadFirstSavedLocationWithoutRefresh() {
        let locationRepository = LocationRepository()
        let savedLocations = locationRepository.getAllSavedLocations()

        if let firstLocation = savedLocations.first {
            // 只設定第一個位置為當前位置，不刷新天氣數據
            currentLocation = firstLocation.name
            currentSavedLocation = firstLocation
            currentCoordinates = (lat: firstLocation.lat, lon: firstLocation.lon)
            locationRepository.saveLastViewedLocationID(firstLocation.id)

            Logger.debug("載入第一筆儲存地區（不刷新天氣）: \(firstLocation.name)")
        } else {
            // 沒有儲存的位置，清空當前位置
            clearCurrentLocation()
            Logger.debug("沒有儲存的位置")
        }
    }
    
    /// 獲取格式化的上次更新時間
    /// - Returns: 格式化的上次更新時間字符串，如：「5分鐘前更新」
    func getFormattedLastUpdatedTime() -> String {
        guard let lastUpdatedTime = lastUpdatedTime else {
            return "尚未更新"
        }
        
        let now = Date()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.minute, .hour, .day], from: lastUpdatedTime, to: now)
        
        if let days = components.day, days > 0 {
            return "\(days)天前更新"
        } else if let hours = components.hour, hours > 0 {
            return "\(hours)小時前更新"
        } else if let minutes = components.minute, minutes > 0 {
            return "\(minutes)分鐘前更新"
        } else {
            return "剛剛更新"
        }
    }
}

/// 預報視圖模型
struct ForecastViewModel: Identifiable {
    var id = UUID()
    let date: String
    let highTemperature: String
    let lowTemperature: String
    let condition: String
    let iconCode: String
    let precipitationProbability: String
    let humidity: String
}

/// 每三小時預報視圖模型
struct HourlyForecastViewModel: Identifiable {
    var id = UUID()
    let time: String      // 顯示時間
    let date: Date        // 實際日期對象
    let temperature: String
    let iconCode: String
    let precipitationProbability: String
    let humidity: String  // 添加濕度屬性
    let lowHighTemp: String // 添加溫度範圍屬性
} 
