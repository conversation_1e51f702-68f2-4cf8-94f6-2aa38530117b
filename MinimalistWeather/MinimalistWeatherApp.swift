//
//  MinimalistWeatherApp.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import SwiftRater

// 隱藏 Home Indicator 的 ViewController
class HomeIndicatorHidingController: UIViewController {
    override var prefersHomeIndicatorAutoHidden: Bool {
        return true // 隱藏 Home Indicator
    }
    
    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        setNeedsStatusBarAppearanceUpdate() // 更新狀態欄外觀
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        setNeedsStatusBarAppearanceUpdate() // 更新狀態欄外觀
    }
    
    override var prefersStatusBarHidden: Bool {
        return true // 隱藏狀態欄
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 設置控制器的基本屬性
        view.backgroundColor = HexColor.themedUIColor(.primaryBackground)
        
        // 在 iOS 15 及以上版本中不再使用 UIApplication.shared.windows
        // 使用 UIWindowScene 的方式設置背景色
        if #available(iOS 15.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.backgroundColor = HexColor.themedUIColor(.primaryBackground)
                }
            }
        } else {
            // 舊版本的 iOS 仍然使用 UIApplication.shared.windows
            UIApplication.shared.windows.forEach { window in
                window.backgroundColor = HexColor.themedUIColor(.primaryBackground)
            }
        }
    }
}

// SwiftUI 與 UIKit 的橋接器
struct HomeIndicatorHidingView<Content: View>: UIViewControllerRepresentable {
    var content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    func makeUIViewController(context: Context) -> UIViewController {
        let hostingController = UIHostingController(rootView: content)
        hostingController.view.backgroundColor = HexColor.themedUIColor(.primaryBackground) // 設置 HostingController 的背景色
        
        let viewController = HomeIndicatorHidingController()
        viewController.view.backgroundColor = HexColor.themedUIColor(.primaryBackground) // 再次確認背景色
        
        // 將 SwiftUI 視圖內嵌到我們的自定義控制器中
        viewController.addChild(hostingController)
        viewController.view.addSubview(hostingController.view)
        hostingController.view.frame = viewController.view.bounds
        hostingController.view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        hostingController.didMove(toParent: viewController)
        
        return viewController
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        if let hostingController = uiViewController.children.first as? UIHostingController<Content> {
            hostingController.rootView = content
        }
    }
}

// MARK: - AppDelegate for SwiftRater
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // 設定 SwiftRater
        SwiftRater.daysUntilPrompt = 10
        SwiftRater.usesUntilPrompt = 5
        SwiftRater.daysBeforeReminding = 5
        SwiftRater.showLaterButton = true
        SwiftRater.debugMode = false // 開發階段開啟，正式版需要設為 false
        SwiftRater.appID = "6745883312" // 範例 App ID，請替換為你的實際 App Store ID
        SwiftRater.appLaunched()

        print("SwiftRater 已初始化")
        return true
    }
}

@main
struct MinimalistWeatherApp: App {
    // 添加 AppDelegate
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    // 創建網絡監控器
    @StateObject private var networkMonitor = NetworkMonitor()
    @StateObject private var languageService = LanguageService.shared
    @StateObject private var iapService = IAPService.shared
    @StateObject private var whatsNewService = WhatsNewService.shared
    @StateObject private var paywallManager = PaywallManager.shared
    @State private var showNetworkAlert = false
    @State private var isOnboardingCompleted = false
    @State private var shouldShowOnboarding = false
    @State private var isInitialized = false
    
    // MARK: - 初始化
    init() {
        // 嘗試加載自定義字體
        var success = FontLoader.loadCustomFonts()
        
        // 如果第一種方法失敗，嘗試第二種方法
        if !success {
            print("嘗試使用Data方法加載字體...")
            success = FontLoader.loadCustomFontsUsingData()
        }
        
        if !success {
            print("警告：自定義字體加載失敗，使用系統字體作為備用")
            // 打印所有可用字體以進行調試
            FontLoader.listAllAvailableFonts()
        }

        // 同步設定到 App Groups，供 Widget 使用
        AppSettings.shared.syncSettingsToAppGroups()

        print("應用程式初始化完成")
    }
    
    // MARK: - 視圖
    var body: some Scene {
        WindowGroup {
            // 使用包裝器隱藏 Home Indicator，同時顯示分頁滾動視圖
            HomeIndicatorHidingView {
                ZStack {
                    HexColor.themed(.primaryBackground)
                        .ignoresSafeArea(.all) // 真正填滿整個螢幕
                    
                    if !isInitialized {
                        // 顯示載入畫面
                        VStack {
                            Spacer()
                            LoadingView(color: HexColor.themed(.primaryText), size: CGFloat(60).auto(), speed: 0.3)
                                .padding(.top, CGFloat(40).auto())
                            Spacer()
                        }
                        .frame(maxWidth: .infinity)
                    } else if shouldShowOnboarding {
                        OnboardingCoordinator(
                            isOnboardingCompleted: $isOnboardingCompleted,
                            shouldShowOnboarding: $shouldShowOnboarding
                        )
                    } else {
                        MainPagingView()
                    }


                    
                    // 網絡離線覆蓋層
                    if !networkMonitor.isConnected {
                        ZStack {
                            // 全版面背景
                            HexColor.themed(.primaryBackground)
                                .ignoresSafeArea(.all)
                            
                            VStack(spacing: CGFloat(20).auto()) {
                                Spacer()
                                
                                AppIconsSymbol.createView(for: AppIcons.nonet, fontSize: CGFloat(80).auto(), color: HexColor.themed(.primaryText))
                                    // .font(.system(size: CGFloat(60).auto()))
                                    // .foregroundColor(HexColor.color("222222"))
                                
                                Text("no_connection".localized)
                                    .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.primaryText))
                                
                                Text("check_internet_connection".localized)
                                    .font(.system(size: CGFloat(12).auto(), weight: .regular))
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(HexColor.themed(.secondaryText))
                                    .padding(.horizontal, CGFloat(40).auto())
                                
                                Spacer()
                                HStack(spacing: CGFloat(10).auto()) {
                                    Text("feedback".localized)
                                        .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(HexColor.themed(.secondaryBackground))
                                        .onTapGesture {
                                            if let url = URL(string: "https://minlsm.featurebase.app/") {
                                                UIApplication.shared.open(url)
                                            }
                                        }
                                }
                                .padding(.vertical, CGFloat(10).auto())
                                .frame(width: CGFloat(200).auto())
                                .background(HexColor.themed(.accent))
                                .cornerRadius(CGFloat(5).auto())
                            }
                            .padding(.vertical, CGFloat(50).auto())
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                        }
                        .transition(.opacity)
                        .animation(.easeInOut, value: networkMonitor.isConnected)
                        .zIndex(100) // 確保在其他視圖之上
                    }
                }
                .environmentObject(networkMonitor) // 將網絡監控器注入環境
                .environmentObject(languageService) // 將語言服務注入環境
                .environmentObject(whatsNewService) // 將 What's New 服務注入環境
            }
            .ignoresSafeArea(.all) // 在最外層也加上，確保完全填滿
            .onAppear {
                // 如果首次啟動時就沒有網絡連接，顯示提醒
                if !networkMonitor.isConnected {
                    showNetworkAlert = true
                }

                // 初始化應用程式狀態
                initializeAppState()
            }
            .alert("no_connection_alert".localized, isPresented: $showNetworkAlert) {
                Button("ok".localized, role: .cancel) { }
            } message: {
                Text("connect_internet_message".localized)
            }
            .fullScreenCover(isPresented: $paywallManager.shouldShowPaywall) {
                PaywallView()
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 初始化應用程式狀態，決定是否顯示 onboarding
    private func initializeAppState() {
        // 等待 IAP 服務初始化完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")

            print("初始化狀態檢查:")
            print("- 已完成 onboarding: \(hasCompletedOnboarding)")

            // 決定是否需要顯示 onboarding
            if hasCompletedOnboarding {
                // 已完成 onboarding 的用戶直接進入主應用，不檢查其他條件
                print("用戶已完成 onboarding，直接進入主應用")
                isOnboardingCompleted = true
                shouldShowOnboarding = false
            } else {
                // 需要顯示 onboarding（包括有訂閱的用戶）
                print("顯示 onboarding")
                isOnboardingCompleted = false
                shouldShowOnboarding = true
            }

            isInitialized = true

            // 檢查可用的本地檔案（調試用）
            whatsNewService.checkAvailableLocalFiles()

            // 檢查是否需要顯示 What's New
            whatsNewService.checkShouldShowWhatsNew()

            // 檢查是否需要顯示 paywall（只有在完成 onboarding 後才檢查）
            if hasCompletedOnboarding {
                checkPaywallDisplay()
            }
        }
    }

    /// 檢查是否需要顯示 paywall
    private func checkPaywallDisplay() {
        // 延遲一點時間確保 IAP 服務完全初始化
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            paywallManager.checkAndShowPaywallIfNeeded { didShow in
                if didShow {
                    print("🎯 PaywallManager: 已顯示 paywall")
                } else {
                    print("🎯 PaywallManager: 不需要顯示 paywall")
                }
            }
        }
    }


}